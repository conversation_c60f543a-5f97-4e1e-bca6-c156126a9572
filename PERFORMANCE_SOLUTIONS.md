# Performance Optimization Solutions

## 4. 📊 Performance Issues Solutions

### Current Problems

#### Bundle Size Issues
Looking at the package.json files, each dashboard has 100+ dependencies:

```json
// ff-zee-dashboard-new/package.json - 122 lines of dependencies
"dependencies": {
  "@botpress/chat": "^0.5.1",
  "@clerk/backend": "^1.23.11",
  "@clerk/clerk-react": "^5.21.1",
  // ... 80+ more dependencies
}
```

#### No Caching Strategy
```typescript
// Current: Every API call hits the server
const fetchZors = async () => {
  const response = await fetch('/v1/zors'); // No caching
  return response.json();
};
```

#### Database Query Issues
```python
# ff-backend-new - Potential N+1 queries
async def get_zors_with_zees():
    zors = await session.exec(select(Zor))
    for zor in zors:
        zor.zees = await session.exec(select(Zee).where(Zee.zor_id == zor.id))
    return zors
```

### Solutions

#### 1. Bundle Size Optimization

##### Dynamic Imports for Large Components
```typescript
// ff-zor-dashboard/src/pages/Analytics.tsx
import { lazy, Suspense } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

// Lazy load heavy chart components
const AnalyticsCharts = lazy(() => import('@/components/analytics/AnalyticsCharts'));
const TerritoryMap = lazy(() => import('@/components/territories/TerritoryMap'));

export function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <h1>Analytics Dashboard</h1>
      
      <Suspense fallback={<LoadingSpinner />}>
        <AnalyticsCharts />
      </Suspense>
      
      <Suspense fallback={<LoadingSpinner />}>
        <TerritoryMap />
      </Suspense>
    </div>
  );
}
```

##### Vite Bundle Analysis Configuration
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    // ... other plugins
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
    }),
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          'chart-vendor': ['recharts', 'react-minimal-pie-chart'],
          'map-vendor': ['@react-google-maps/api', '@turf/turf'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
          
          // Feature chunks
          'analytics': ['src/components/analytics'],
          'territories': ['src/components/territories'],
          'documents': ['src/components/documents'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
});
```

##### Tree Shaking Optimization
```typescript
// Instead of importing entire libraries
import * as _ from 'lodash'; // ❌ Imports entire lodash

// Import only what you need
import { debounce, throttle } from 'lodash-es'; // ✅ Tree-shakeable
```

#### 2. Caching Strategy Implementation

##### React Query Setup with Caching
```typescript
// ff-shared-ui/src/hooks/useApiQuery.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiManager } from '@/helpers/api.manager';

interface CacheConfig {
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
}

export function useApiQuery<T>(
  key: string[],
  url: string,
  options: CacheConfig = {}
) {
  return useQuery({
    queryKey: key,
    queryFn: async () => {
      const response = await apiManager.get(url);
      return response.data as T;
    },
    staleTime: options.staleTime ?? 5 * 60 * 1000, // 5 minutes
    cacheTime: options.cacheTime ?? 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options.refetchOnWindowFocus ?? false,
  });
}

// Usage with different cache strategies
export function useZors() {
  return useApiQuery<Zor[]>(
    ['zors'],
    '/v1/zors',
    {
      staleTime: 10 * 60 * 1000, // 10 minutes - rarely changes
      cacheTime: 30 * 60 * 1000, // 30 minutes
    }
  );
}

export function useZees(zorId: string) {
  return useApiQuery<Zee[]>(
    ['zees', zorId],
    `/v1/zees?zor_id=${zorId}`,
    {
      staleTime: 2 * 60 * 1000, // 2 minutes - changes more frequently
      cacheTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

export function useTerritories(bounds: MapBounds) {
  return useApiQuery<Territory[]>(
    ['territories', bounds],
    `/api/territories?minLat=${bounds.minLat}&maxLat=${bounds.maxLat}&minLng=${bounds.minLng}&maxLng=${bounds.maxLng}`,
    {
      staleTime: 30 * 1000, // 30 seconds - real-time data
      cacheTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}
```

##### Backend Caching with Redis
```python
# ff-backend-new/app/core/cache.py
import redis
import json
from typing import Optional, Any
from functools import wraps
from app.core.config import settings

redis_client = redis.Redis.from_url(settings.REDIS_URL)

def cache_result(key_prefix: str, ttl: int = 300):
    """Decorator to cache function results"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            redis_client.setex(
                cache_key,
                ttl,
                json.dumps(result, default=str)
            )
            
            return result
        return wrapper
    return decorator

# Usage in services
@cache_result("zors", ttl=600)  # Cache for 10 minutes
async def get_zors(session: AsyncSession) -> List[Zor]:
    result = await session.exec(select(Zor))
    return result.all()

@cache_result("territories", ttl=60)  # Cache for 1 minute
async def get_territories_in_bounds(
    session: AsyncSession,
    min_lat: float,
    max_lat: float,
    min_lng: float,
    max_lng: float
) -> List[Territory]:
    result = await session.exec(
        select(Territory).where(
            Territory.lat.between(min_lat, max_lat),
            Territory.lng.between(min_lng, max_lng)
        )
    )
    return result.all()
```

#### 3. Database Query Optimization

##### Fix N+1 Queries with Eager Loading
```python
# ff-backend-new/app/services/zor_service.py
from sqlmodel import select
from sqlalchemy.orm import selectinload

# ❌ N+1 Query Problem
async def get_zors_with_zees_bad(session: AsyncSession):
    zors = await session.exec(select(Zor))
    for zor in zors:
        # This creates N additional queries!
        zor.zees = await session.exec(select(Zee).where(Zee.zor_id == zor.id))
    return zors

# ✅ Optimized with Eager Loading
async def get_zors_with_zees_optimized(session: AsyncSession):
    statement = select(Zor).options(selectinload(Zor.zees))
    result = await session.exec(statement)
    return result.all()

# ✅ Even better with specific fields
async def get_zors_with_zee_count(session: AsyncSession):
    statement = select(
        Zor.id,
        Zor.name,
        Zor.slug,
        func.count(Zee.id).label('zee_count')
    ).outerjoin(Zee).group_by(Zor.id)
    
    result = await session.exec(statement)
    return result.all()
```

##### Database Indexing Strategy
```sql
-- ff-backend-new/supabase/migrations/add_performance_indexes.sql

-- Index for frequent queries
CREATE INDEX CONCURRENTLY idx_zees_zor_id ON zees(zor_id);
CREATE INDEX CONCURRENTLY idx_territories_bounds ON territories USING GIST (
    ST_MakeEnvelope(lng, lat, lng, lat, 4326)
);
CREATE INDEX CONCURRENTLY idx_zee_documents_zee_id ON zee_documents(zee_id);
CREATE INDEX CONCURRENTLY idx_discovery_stages_zee_id ON discovery_stages(zee_id);

-- Composite indexes for common filter combinations
CREATE INDEX CONCURRENTLY idx_zees_zor_status ON zees(zor_id, status);
CREATE INDEX CONCURRENTLY idx_territories_zor_available ON territories(zor_id, is_available);

-- Partial indexes for common conditions
CREATE INDEX CONCURRENTLY idx_zees_active ON zees(id) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_territories_available ON territories(id) WHERE is_available = true;
```

#### 4. CDN and Static Asset Optimization

##### Vercel CDN Configuration
```typescript
// next.config.js (for Next.js services)
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['supabase.co', 'clerk.dev'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Enable static optimization
  output: 'standalone',
  
  // Optimize fonts
  optimizeFonts: true,
  
  // Headers for caching
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, s-maxage=60, stale-while-revalidate=300',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

##### Image Optimization
```typescript
// ff-shared-ui/src/components/OptimizedImage.tsx
import { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}

export function OptimizedImage({ 
  src, 
  alt, 
  width, 
  height, 
  className 
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  // Generate responsive image URLs
  const generateSrcSet = (baseSrc: string) => {
    const sizes = [320, 640, 768, 1024, 1280];
    return sizes
      .map(size => `${baseSrc}?w=${size}&q=75 ${size}w`)
      .join(', ');
  };

  if (error) {
    return (
      <div className={`bg-gray-200 flex items-center justify-center ${className}`}>
        <span className="text-gray-500">Failed to load image</span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      <img
        src={src}
        srcSet={generateSrcSet(src)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        alt={alt}
        width={width}
        height={height}
        loading="lazy"
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setError(true);
        }}
        className={`transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
      />
    </div>
  );
}
```

#### 5. Performance Monitoring

##### Web Vitals Tracking
```typescript
// ff-shared-ui/src/utils/performance.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];

  init() {
    getCLS(this.handleMetric.bind(this));
    getFID(this.handleMetric.bind(this));
    getFCP(this.handleMetric.bind(this));
    getLCP(this.handleMetric.bind(this));
    getTTFB(this.handleMetric.bind(this));
  }

  private handleMetric(metric: any) {
    const performanceMetric: PerformanceMetric = {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      timestamp: Date.now(),
    };

    this.metrics.push(performanceMetric);
    
    // Send to analytics
    this.sendToAnalytics(performanceMetric);
    
    // Log poor performance
    if (metric.rating === 'poor') {
      console.warn(`Poor ${metric.name} performance:`, metric.value);
    }
  }

  private async sendToAnalytics(metric: PerformanceMetric) {
    try {
      await fetch('/api/analytics/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metric),
      });
    } catch (error) {
      console.error('Failed to send performance metric:', error);
    }
  }

  getMetrics(): PerformanceMetric[] {
    return this.metrics;
  }
}

export const performanceMonitor = new PerformanceMonitor();
```

### Implementation Timeline

#### Week 1: Bundle Optimization
- Set up bundle analysis
- Implement dynamic imports for heavy components
- Configure manual chunks in Vite

#### Week 2: Caching Implementation
- Set up React Query with proper cache configuration
- Implement Redis caching in backend
- Add cache invalidation strategies

#### Week 3: Database Optimization
- Add database indexes
- Fix N+1 queries with eager loading
- Implement query result caching

#### Week 4: CDN and Assets
- Configure Vercel CDN properly
- Implement image optimization
- Set up proper cache headers

#### Week 5: Monitoring and Testing
- Implement performance monitoring
- Load testing with optimizations
- Performance regression testing

### Expected Performance Improvements

1. **Bundle Size**: 40-50% reduction
2. **Initial Load Time**: 60-70% improvement
3. **API Response Time**: 50-80% improvement with caching
4. **Database Query Time**: 70-90% improvement with indexes
5. **Image Load Time**: 50-60% improvement with optimization
