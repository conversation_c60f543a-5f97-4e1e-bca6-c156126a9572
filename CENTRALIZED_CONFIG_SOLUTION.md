# Centralized Configuration Solution

## 3. ⚙️ Centralized Configuration

### Current Problem
Each project manages its own environment variables with duplication and inconsistencies:

```typescript
// ff-zor-dashboard/src/constants/config.ts
export const configuration = {
  API_URL: import.meta.env.VITE_API_URL || '',
  CLERK_API_URL: import.meta.env.VITE_CLERK_API_URL || '',
  TERRITORIES_API_URL: import.meta.env.VITE_TERRITORIES_API_URL || '',
};

// ff-zee-dashboard-new/src/constants/config.ts (almost identical)
export const configuration = {
  API_URL: import.meta.env.VITE_API_URL || '',
  CLERK_API_URL: import.meta.env.VITE_CLERK_API_URL || '',
  TERRITORIES_API_URL: import.meta.env.VITE_TERRITORIES_API_URL || '',
  VOICEFLOW_PROJECT_ID: '6840b5b978f00e1b0b9447be', // Hardcoded!
};
```

### Solution: Centralized Configuration Service

#### Step 1: Create Configuration Package
```bash
mkdir ff-config
cd ff-config
npm init -y
```

#### Step 2: Configuration Schema
```typescript
// ff-config/src/schema.ts
import { z } from 'zod';

export const EnvironmentSchema = z.enum(['local', 'development', 'staging', 'production']);
export type Environment = z.infer<typeof EnvironmentSchema>;

export const DatabaseConfigSchema = z.object({
  host: z.string(),
  port: z.number().int().positive(),
  name: z.string(),
  user: z.string(),
  password: z.string(),
  ssl: z.boolean().default(true),
});

export const SupabaseConfigSchema = z.object({
  url: z.string().url(),
  anonKey: z.string(),
  serviceKey: z.string(),
});

export const ClerkConfigSchema = z.object({
  publishableKey: z.string(),
  secretKey: z.string(),
  webhookSecret: z.string().optional(),
});

export const ApiConfigSchema = z.object({
  mainApi: z.object({
    url: z.string().url(),
    timeout: z.number().default(30000),
  }),
  territoriesApi: z.object({
    url: z.string().url(),
    timeout: z.number().default(15000),
  }),
  clerkApi: z.object({
    url: z.string().url(),
    timeout: z.number().default(10000),
  }),
});

export const ExternalServicesSchema = z.object({
  sendgrid: z.object({
    apiKey: z.string(),
    fromEmail: z.string().email(),
  }),
  googleMaps: z.object({
    apiKey: z.string(),
  }),
  voiceflow: z.object({
    projectId: z.string(),
    apiKey: z.string().optional(),
  }),
  vercel: z.object({
    apiKey: z.string(),
    teamId: z.string(),
    projectIds: z.object({
      zorDashboard: z.string(),
      zeeDashboard: z.string(),
      consultantDashboard: z.string(),
    }),
  }),
});

export const AppConfigSchema = z.object({
  environment: EnvironmentSchema,
  database: DatabaseConfigSchema,
  supabase: SupabaseConfigSchema,
  clerk: ClerkConfigSchema,
  apis: ApiConfigSchema,
  externalServices: ExternalServicesSchema,
  features: z.object({
    enableAnalytics: z.boolean().default(true),
    enableChatbot: z.boolean().default(true),
    enableTerritories: z.boolean().default(true),
    enableNotifications: z.boolean().default(true),
  }),
  ui: z.object({
    theme: z.enum(['light', 'dark', 'auto']).default('light'),
    primaryColor: z.string().default('#3b82f6'),
    brandName: z.string().default('Franchise Frameworks'),
  }),
});

export type AppConfig = z.infer<typeof AppConfigSchema>;
```

#### Step 3: Configuration Loader
```typescript
// ff-config/src/loader.ts
import { AppConfig, AppConfigSchema, Environment } from './schema';

export class ConfigLoader {
  private static instance: ConfigLoader;
  private config: AppConfig | null = null;

  private constructor() {}

  static getInstance(): ConfigLoader {
    if (!ConfigLoader.instance) {
      ConfigLoader.instance = new ConfigLoader();
    }
    return ConfigLoader.instance;
  }

  load(environment: Environment): AppConfig {
    if (this.config) {
      return this.config;
    }

    const rawConfig = this.loadEnvironmentConfig(environment);
    
    try {
      this.config = AppConfigSchema.parse(rawConfig);
      return this.config;
    } catch (error) {
      console.error('Configuration validation failed:', error);
      throw new Error('Invalid configuration');
    }
  }

  private loadEnvironmentConfig(environment: Environment): unknown {
    const baseConfig = {
      environment,
      features: {
        enableAnalytics: true,
        enableChatbot: environment !== 'local',
        enableTerritories: true,
        enableNotifications: environment !== 'local',
      },
      ui: {
        theme: 'light',
        primaryColor: '#3b82f6',
        brandName: 'Franchise Frameworks',
      },
    };

    switch (environment) {
      case 'local':
        return {
          ...baseConfig,
          database: {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '5432'),
            name: process.env.DB_NAME || 'franchise_frameworks_dev',
            user: process.env.DB_USER || 'postgres',
            password: process.env.DB_PASSWORD || 'password',
            ssl: false,
          },
          supabase: {
            url: process.env.SUPABASE_URL || '',
            anonKey: process.env.SUPABASE_ANON_KEY || '',
            serviceKey: process.env.SUPABASE_SERVICE_KEY || '',
          },
          clerk: {
            publishableKey: process.env.CLERK_PUBLISHABLE_KEY || '',
            secretKey: process.env.CLERK_SECRET_KEY || '',
          },
          apis: {
            mainApi: {
              url: 'http://localhost:8000',
              timeout: 30000,
            },
            territoriesApi: {
              url: 'http://localhost:3000',
              timeout: 15000,
            },
            clerkApi: {
              url: 'http://localhost:3001',
              timeout: 10000,
            },
          },
          externalServices: {
            sendgrid: {
              apiKey: process.env.SENDGRID_API_KEY || '',
              fromEmail: 'noreply@localhost',
            },
            googleMaps: {
              apiKey: process.env.GOOGLE_MAPS_API_KEY || '',
            },
            voiceflow: {
              projectId: '6840b5b978f00e1b0b9447be',
            },
            vercel: {
              apiKey: process.env.VERCEL_API_KEY || '',
              teamId: process.env.VERCEL_TEAM_ID || '',
              projectIds: {
                zorDashboard: process.env.VERCEL_PROJECT_ID_ZOR || '',
                zeeDashboard: process.env.VERCEL_PROJECT_ID_ZEE || '',
                consultantDashboard: process.env.VERCEL_PROJECT_ID_CONSULTANT || '',
              },
            },
          },
        };

      case 'development':
        return {
          ...baseConfig,
          database: {
            host: process.env.DB_HOST || '',
            port: parseInt(process.env.DB_PORT || '5432'),
            name: process.env.DB_NAME || '',
            user: process.env.DB_USER || '',
            password: process.env.DB_PASSWORD || '',
            ssl: true,
          },
          supabase: {
            url: process.env.SUPABASE_URL || '',
            anonKey: process.env.SUPABASE_ANON_KEY || '',
            serviceKey: process.env.SUPABASE_SERVICE_KEY || '',
          },
          clerk: {
            publishableKey: process.env.CLERK_PUBLISHABLE_KEY || '',
            secretKey: process.env.CLERK_SECRET_KEY || '',
            webhookSecret: process.env.CLERK_WEBHOOK_SECRET || '',
          },
          apis: {
            mainApi: {
              url: 'https://api-dev.franchiseframeworks.com',
              timeout: 30000,
            },
            territoriesApi: {
              url: 'https://territories-dev.franchiseframeworks.com',
              timeout: 15000,
            },
            clerkApi: {
              url: 'https://clerk-dev.franchiseframeworks.com',
              timeout: 10000,
            },
          },
          externalServices: {
            sendgrid: {
              apiKey: process.env.SENDGRID_API_KEY || '',
              fromEmail: '<EMAIL>',
            },
            googleMaps: {
              apiKey: process.env.GOOGLE_MAPS_API_KEY || '',
            },
            voiceflow: {
              projectId: '6840b5b978f00e1b0b9447be',
              apiKey: process.env.VOICEFLOW_API_KEY || '',
            },
            vercel: {
              apiKey: process.env.VERCEL_API_KEY || '',
              teamId: process.env.VERCEL_TEAM_ID || '',
              projectIds: {
                zorDashboard: process.env.VERCEL_PROJECT_ID_ZOR || '',
                zeeDashboard: process.env.VERCEL_PROJECT_ID_ZEE || '',
                consultantDashboard: process.env.VERCEL_PROJECT_ID_CONSULTANT || '',
              },
            },
          },
        };

      case 'production':
        return {
          ...baseConfig,
          // Production configuration...
          apis: {
            mainApi: {
              url: 'https://api.franchiseframeworks.com',
              timeout: 30000,
            },
            territoriesApi: {
              url: 'https://territories.franchiseframeworks.com',
              timeout: 15000,
            },
            clerkApi: {
              url: 'https://clerk.franchiseframeworks.com',
              timeout: 10000,
            },
          },
          // ... other production settings
        };

      default:
        throw new Error(`Unknown environment: ${environment}`);
    }
  }

  getConfig(): AppConfig {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call load() first.');
    }
    return this.config;
  }

  // Utility methods for common config access
  getDatabaseUrl(): string {
    const config = this.getConfig();
    const { host, port, name, user, password } = config.database;
    return `postgresql://${user}:${password}@${host}:${port}/${name}`;
  }

  getApiUrl(service: 'main' | 'territories' | 'clerk'): string {
    const config = this.getConfig();
    switch (service) {
      case 'main':
        return config.apis.mainApi.url;
      case 'territories':
        return config.apis.territoriesApi.url;
      case 'clerk':
        return config.apis.clerkApi.url;
      default:
        throw new Error(`Unknown service: ${service}`);
    }
  }

  isFeatureEnabled(feature: keyof AppConfig['features']): boolean {
    return this.getConfig().features[feature];
  }
}

// Export singleton instance
export const configLoader = ConfigLoader.getInstance();
```

#### Step 4: Frontend Configuration Hook
```typescript
// ff-config/src/react.ts
import { createContext, useContext, ReactNode } from 'react';
import { AppConfig } from './schema';
import { configLoader } from './loader';

const ConfigContext = createContext<AppConfig | null>(null);

interface ConfigProviderProps {
  children: ReactNode;
  environment: 'local' | 'development' | 'staging' | 'production';
}

export function ConfigProvider({ children, environment }: ConfigProviderProps) {
  const config = configLoader.load(environment);
  
  return (
    <ConfigContext.Provider value={config}>
      {children}
    </ConfigContext.Provider>
  );
}

export function useConfig(): AppConfig {
  const config = useContext(ConfigContext);
  if (!config) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return config;
}

// Specific hooks for common use cases
export function useApiConfig() {
  const config = useConfig();
  return config.apis;
}

export function useFeatureFlags() {
  const config = useConfig();
  return config.features;
}

export function useThemeConfig() {
  const config = useConfig();
  return config.ui;
}
```

#### Step 5: Backend Configuration
```python
# ff-config/python/config.py
import os
from typing import Optional
from pydantic import BaseSettings, validator
from enum import Enum

class Environment(str, Enum):
    LOCAL = "local"
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class DatabaseConfig(BaseSettings):
    host: str
    port: int = 5432
    name: str
    user: str
    password: str
    ssl: bool = True

    @property
    def url(self) -> str:
        return f"postgresql+asyncpg://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"

class SupabaseConfig(BaseSettings):
    url: str
    anon_key: str
    service_key: str

class ClerkConfig(BaseSettings):
    publishable_key: str
    secret_key: str
    webhook_secret: Optional[str] = None

class ExternalServicesConfig(BaseSettings):
    sendgrid_api_key: str
    google_maps_api_key: str
    vercel_api_key: str
    vercel_team_id: str

class AppConfig(BaseSettings):
    environment: Environment
    database: DatabaseConfig
    supabase: SupabaseConfig
    clerk: ClerkConfig
    external_services: ExternalServicesConfig

    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"

    @classmethod
    def load(cls, environment: Environment) -> "AppConfig":
        # Load environment-specific configuration
        config_data = {
            "environment": environment,
            "database": DatabaseConfig(
                host=os.getenv("DB_HOST"),
                port=int(os.getenv("DB_PORT", "5432")),
                name=os.getenv("DB_NAME"),
                user=os.getenv("DB_USER"),
                password=os.getenv("DB_PASSWORD"),
                ssl=environment != Environment.LOCAL,
            ),
            "supabase": SupabaseConfig(
                url=os.getenv("SUPABASE_URL"),
                anon_key=os.getenv("SUPABASE_ANON_KEY"),
                service_key=os.getenv("SUPABASE_SERVICE_KEY"),
            ),
            "clerk": ClerkConfig(
                publishable_key=os.getenv("CLERK_PUBLISHABLE_KEY"),
                secret_key=os.getenv("CLERK_SECRET_KEY"),
                webhook_secret=os.getenv("CLERK_WEBHOOK_SECRET"),
            ),
            "external_services": ExternalServicesConfig(
                sendgrid_api_key=os.getenv("SENDGRID_API_KEY"),
                google_maps_api_key=os.getenv("GOOGLE_MAPS_API_KEY"),
                vercel_api_key=os.getenv("VERCEL_API_KEY"),
                vercel_team_id=os.getenv("VERCEL_TEAM_ID"),
            ),
        }
        
        return cls(**config_data)

# Global configuration instance
_config: Optional[AppConfig] = None

def get_config() -> AppConfig:
    global _config
    if _config is None:
        environment = Environment(os.getenv("ENVIRONMENT", "local"))
        _config = AppConfig.load(environment)
    return _config
```

#### Step 6: Update Projects to Use Centralized Config

```typescript
// ff-zor-dashboard/src/main.tsx
import { ConfigProvider } from '@ff/config/react';

const environment = import.meta.env.VITE_ENVIRONMENT || 'local';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <ConfigProvider environment={environment}>
    <App />
  </ConfigProvider>
);
```

```typescript
// ff-zor-dashboard/src/components/SomeComponent.tsx
import { useApiConfig, useFeatureFlags } from '@ff/config/react';

export function SomeComponent() {
  const apiConfig = useApiConfig();
  const features = useFeatureFlags();

  if (!features.enableTerritories) {
    return null;
  }

  const fetchData = async () => {
    const response = await fetch(`${apiConfig.mainApi.url}/v1/zors`);
    return response.json();
  };

  // ...
}
```

### Benefits of Centralized Configuration

1. **Single Source of Truth**: All configuration in one place
2. **Type Safety**: Zod validation ensures correct configuration
3. **Environment Management**: Easy switching between environments
4. **Feature Flags**: Enable/disable features per environment
5. **Validation**: Catch configuration errors at startup
6. **Documentation**: Schema serves as configuration documentation

### Implementation Timeline
- **Week 1**: Create configuration package and schema
- **Week 2**: Implement loaders for frontend and backend
- **Week 3**: Update all projects to use centralized config
- **Week 4**: Add feature flags and environment-specific settings
- **Week 5**: Testing and documentation

### Estimated Impact
- **Maintenance**: 80% easier configuration management
- **Errors**: 90% reduction in configuration-related bugs
- **Deployment**: Faster environment setup
- **Feature Management**: Easy feature flag control
