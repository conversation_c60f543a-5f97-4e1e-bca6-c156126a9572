# Franchise Frameworks System

A comprehensive franchise management platform with role-based dashboards and specialized backend services.

## 🏗️ System Overview

The Franchise Frameworks platform consists of 6 interconnected projects that provide a complete franchise management solution:

### Frontend Applications
- **ff-zor-dashboard** - Franchisor (Zor) management interface
- **ff-zee-dashboard-new** - <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>) onboarding and management
- **ff-consultant-dashboard-new** - Consultant tools for franchise opportunities

### Backend Services
- **ff-backend-new** - Main business logic API (FastAPI/Python)
- **ff-clerk-backend** - Authentication service (Next.js)
- **ff-territories-backend-new** - Geographic territory management (Next.js)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.9+
- PostgreSQL (via Supabase)
- Clerk account for authentication

### Environment Setup
Each project requires environment variables. See individual project directories for `.env.example` files.

### Development Setup

1. **Clone and install dependencies**:
```bash
# Install frontend dependencies
cd ff-zor-dashboard && npm install
cd ../ff-zee-dashboard-new && npm install
cd ../ff-consultant-dashboard-new && npm install

# Install backend dependencies
cd ../ff-backend-new && pip install -r requirements.txt
cd ../ff-clerk-backend && npm install
cd ../ff-territories-backend-new && npm install
```

2. **Start development servers**:
```bash
# Frontend applications (different terminals)
cd ff-zor-dashboard && npm run dev          # http://localhost:5173
cd ff-zee-dashboard-new && npm run dev      # http://localhost:5174
cd ff-consultant-dashboard-new && npm run dev # http://localhost:5175

# Backend services
cd ff-backend-new && python run.py          # http://localhost:8000
cd ff-clerk-backend && npm run dev          # http://localhost:3001
cd ff-territories-backend-new && npm run dev # http://localhost:3000
```

## 📋 Project Structure

```
franchise-frameworks/
├── ff-backend-new/              # Main API (FastAPI)
│   ├── app/
│   │   ├── core/               # Database, config, auth
│   │   ├── models/             # SQLModel data models
│   │   ├── routers/            # API endpoints
│   │   ├── services/           # Business logic
│   │   └── schemas/            # Pydantic schemas
│   └── requirements.txt
├── ff-clerk-backend/            # Auth service (Next.js)
│   └── src/pages/api/          # Authentication endpoints
├── ff-territories-backend-new/  # Territories API (Next.js)
│   └── src/app/api/            # Geographic endpoints
├── ff-zor-dashboard/            # Franchisor UI (React)
├── ff-zee-dashboard-new/        # Franchisee UI (React)
├── ff-consultant-dashboard-new/ # Consultant UI (React)
└── docs/                       # Documentation
    ├── ARCHITECTURE_ANALYSIS.md
    ├── PROJECT_ANALYSIS.md
    └── ISSUES_AND_RECOMMENDATIONS.md
```

## 🔧 Technology Stack

### Backend
- **FastAPI** - High-performance Python API framework
- **SQLModel** - SQL databases with Python type hints
- **Supabase** - PostgreSQL database and authentication
- **Next.js** - React framework for API routes

### Frontend
- **React 18** - UI library with hooks
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool and dev server
- **Zustand** - Lightweight state management
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives

### External Services
- **Clerk** - Authentication and user management
- **Supabase** - Database and file storage
- **SendGrid** - Email delivery
- **Google Maps** - Territory visualization
- **Vercel** - Deployment platform

## 🔐 Authentication Flow

1. Users authenticate via Clerk in frontend applications
2. JWT tokens are issued and stored in localStorage
3. API requests include Bearer tokens in Authorization headers
4. Backend services verify tokens with Clerk
5. User roles determine access to features and data

### User Roles
- **org:zor** - Franchisor with full system access
- **org:zee** - Franchisee with limited access to their data
- **org:partner_consultant** - Consultant with territory and lead access

## 🌐 API Endpoints

### Main API (ff-backend-new)
- **Base URL**: `/v1/`
- **Authentication**: Bearer token required
- **Documentation**: Available at `/docs` when running

Key endpoint groups:
- `/v1/zors/*` - Franchisor management
- `/v1/zees/*` - Franchisee management
- `/v1/consultants/*` - Consultant management
- `/v1/discovery-stages/*` - Franchise discovery workflow
- `/v1/territories/*` - Territory management
- `/v1/documents/*` - Document management

### Clerk API (ff-clerk-backend)
- **Base URL**: `/api/`
- **Port**: 3001
- **Purpose**: Authentication operations

### Territories API (ff-territories-backend-new)
- **Base URL**: `/api/`
- **Purpose**: Geographic and territory operations

## 🚨 Known Issues

### Critical Issues (Fix Immediately)
1. **Corrupted requirements.txt** in ff-backend-new
2. **Missing package-lock.json** in ff-zor-dashboard
3. **Security vulnerabilities** - CORS allows all origins
4. **No rate limiting** on APIs

### High Priority Issues
1. **Code duplication** (70-80% overlap between frontends)
2. **Missing error handling** across all projects
3. **No testing infrastructure**
4. **Performance optimization needed**

See [ISSUES_AND_RECOMMENDATIONS.md](./ISSUES_AND_RECOMMENDATIONS.md) for detailed analysis and solutions.

## 📚 Documentation

- **[Architecture Analysis](./ARCHITECTURE_ANALYSIS.md)** - System architecture and design patterns
- **[Project Analysis](./PROJECT_ANALYSIS.md)** - Detailed analysis of each project
- **[Issues & Recommendations](./ISSUES_AND_RECOMMENDATIONS.md)** - Known issues and improvement plans

## 🤝 Contributing

1. **Code Style**: Follow existing patterns and use TypeScript
2. **Testing**: Add tests for new features (when testing infrastructure is added)
3. **Documentation**: Update relevant documentation for changes
4. **Security**: Follow security best practices

### Development Workflow
1. Create feature branch from main
2. Make changes and test locally
3. Update documentation if needed
4. Create pull request with description
5. Code review and merge

## 🔄 Deployment

### Frontend Applications
- **Platform**: Vercel
- **Build Command**: `npm run build`
- **Environment**: Separate deployments per environment

### Backend Services
- **Main API**: AWS App Runner
- **Clerk Backend**: Vercel
- **Territories Backend**: Vercel

### Environment URLs
- **Local**: localhost with different ports
- **Development**: `*-dev.franchiseframeworks.com`
- **Production**: `*.franchiseframeworks.com`

## 📞 Support

For technical issues or questions:
1. Check existing documentation
2. Review known issues in ISSUES_AND_RECOMMENDATIONS.md
3. Create detailed issue report with reproduction steps

## 🗺️ Roadmap

### Immediate (Next 30 Days)
- [ ] Fix critical issues (requirements.txt, security)
- [ ] Add basic testing infrastructure
- [ ] Implement proper error handling

### Short-term (3 Months)
- [ ] Create shared component library
- [ ] Add comprehensive monitoring
- [ ] Implement performance optimizations

### Long-term (6+ Months)
- [ ] Microservices architecture improvements
- [ ] Mobile applications
- [ ] Advanced analytics and reporting

---

**Last Updated**: January 2025  
**System Version**: 0.1.0  
**Documentation Version**: 1.0.0
