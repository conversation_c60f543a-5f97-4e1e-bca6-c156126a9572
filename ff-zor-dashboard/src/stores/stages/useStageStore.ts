import { create } from 'zustand';

import { Stage } from '@/types/stage.type';

export interface StageState {
  stageList: Array<Stage> | [];
  getStageList: () => Array<Stage> | [];
  setStageList: (lists: Array<Stage> | []) => void;

  addStage: (list: Stage) => void;
  updateStage: (stage_id: string, updatedStage: Stage) => void;
  deleteStage: (stage_id: string) => void;
}

const useStageStore = create<StageState>((set, get) => ({
  stageList: [],
  getStageList: () => get().stageList,
  setStageList: (lists: Array<Stage> | []) => set(() => ({ stageList: lists })),

  addStage: (list: Stage) =>
    set((state) => ({ stageList: [...state.stageList, list] })),

  updateStage: (stage_id: string, updatedStage: Stage) =>
    set((state) => ({
      stageList: state.stageList.map((stage) =>
        stage.id === stage_id ? updatedStage : stage
      ),
    })),

  deleteStage: (stage_id: string) =>
    set((state) => ({
      stageList: state.stageList.filter((item) => item.id !== stage_id),
    })),
}));

export default useStageStore;
