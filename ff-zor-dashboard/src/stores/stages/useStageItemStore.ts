import { create } from 'zustand';

import { StageItem } from '@/types/stage.item.type';

export interface StageItemState {
  stageItems: Array<StageItem> | [];
  getStageItems: () => Array<StageItem> | [];
  setStageItems: (lists: Array<StageItem> | []) => void;
  // CUD Operations
  createStageItem: (item: StageItem) => void;
  updateStageItem: (id: string, item: StageItem) => void;
  deleteStageItem: (id: string) => void;
}

const useStageItemStore = create<StageItemState>((set, get) => ({
  stageItems: [],
  getStageItems: () => get().stageItems,
  setStageItems: (lists: Array<StageItem> | []) =>
    set(() => ({ stageItems: lists })),
  // CUD Operations
  createStageItem: (item: StageItem) =>
    set((state) => ({ stageItems: [...state.stageItems, item] })),
  updateStageItem: (id: string, newItem: StageItem) =>
    set((state) => ({
      stageItems: state.stageItems.map((item) =>
        item.id === id ? newItem : item
      ),
    })),
  deleteStageItem: (id: string) =>
    set((state) => {
      // Find the item to delete
      const itemToDelete = state.stageItems.find((item) => item.id === id);
      if (!itemToDelete) return state; // Item not found, no changes needed

      // Find the item that follows the deleted item (has previous_stage_item_id pointing to the deleted item)
      const followingItem = state.stageItems.find(
        (item) => item.previous_stage_item_id === id
      );

      // Update the following item's previous_stage_item_id to maintain the chain
      const updatedItems = state.stageItems.map((item) => {
        if (followingItem && item.id === followingItem.id) {
          return {
            ...item,
            previous_stage_item_id: itemToDelete.previous_stage_item_id,
          };
        }
        return item;
      });

      // Remove the deleted item from the array
      return {
        stageItems: [...updatedItems.filter((item) => item.id !== id)],
      };
    }),
}));

export default useStageItemStore;
