import { create } from 'zustand';

import { LeadsApiResponse } from '@/types/data-center/leads.type';

interface LeadsState {
  leadsData: LeadsApiResponse | null;
  isLoading: boolean;
  error: string | null;
  shouldRefresh: boolean;
  setLeadsData: (data: LeadsApiResponse) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  triggerRefresh: () => void;
  clearRefresh: () => void;
  reset: () => void;
}

const useLeadsStore = create<LeadsState>((set) => ({
  leadsData: null,
  isLoading: false,
  error: null,
  shouldRefresh: false,

  setLeadsData: (data) =>
    set({
      leadsData: data,
      error: null,
      isLoading: false,
    }),

  setLoading: (isLoading) => set({ isLoading }),

  setError: (error) =>
    set({
      error,
      isLoading: false,
    }),

  triggerRefresh: () =>
    set({
      shouldRefresh: true,
      error: null,
    }),

  clearRefresh: () =>
    set({
      shouldRefresh: false,
    }),

  reset: () =>
    set({
      leadsData: null,
      isLoading: false,
      error: null,
      shouldRefresh: false,
    }),
}));

export default useLeadsStore;
