import { create } from 'zustand';

import { US_STATES } from '@/constants/territories/states';
import { TerritorySettings } from '@/types/territories/territory.settings.type';
import { Territory } from '@/types/territories/territory.type';

export interface TerritoryState {
  territories: Territory[] | undefined;
  mapTerritories: Territory[] | undefined;
  allTerritories: Territory[] | undefined;
  likedTerritories: Territory[] | undefined;
  acsVariables: Array<{ code: string; new_label: string }> | undefined;
  statesCompliance: Record<string, { compliant: boolean; active: boolean }>;
  territorySettings: TerritorySettings | undefined;
  setTerritories: (territories: Territory[]) => void;
  setMapTerritories: (mapTerritories: Territory[]) => void;
  setAllTerritories: (allTerritories: Territory[]) => void;
  setLikedTerritories: (likedTerritories: Territory[]) => void;
  setAcsVariables: (
    acsVariables: Array<{ code: string; new_label: string }>
  ) => void;
  setStatesCompliance: (
    statesCompliance: Record<string, { compliant: boolean; active: boolean }>
  ) => void;
  setTerritorySettings: (territorySettings: TerritorySettings) => void;
}

const useTerritoryStore = create<TerritoryState>((set) => ({
  territories: undefined,
  mapTerritories: undefined,
  allTerritories: undefined,
  likedTerritories: undefined,
  acsVariables: undefined,
  statesCompliance: Object.keys(US_STATES).reduce(
    (acc, state) => ({
      ...acc,
      [state]: {
        compliant: false,
        active: false,
      },
    }),
    {}
  ),
  territorySettings: undefined,
  setTerritories: (territories: Territory[]) => set(() => ({ territories })),
  setMapTerritories: (mapTerritories: Territory[]) =>
    set(() => ({ mapTerritories })),
  setAllTerritories: (allTerritories: Territory[]) =>
    set(() => ({ allTerritories })),
  setLikedTerritories: (likedTerritories: Territory[]) =>
    set(() => ({ likedTerritories })),
  setAcsVariables: (acsVariables: Array<{ code: string; new_label: string }>) =>
    set(() => ({ acsVariables })),
  setStatesCompliance: (
    statesCompliance: Record<string, { compliant: boolean; active: boolean }>
  ) => set(() => ({ statesCompliance })),
  setTerritorySettings: (territorySettings: TerritorySettings) =>
    set(() => ({ territorySettings })),
}));

export default useTerritoryStore;
