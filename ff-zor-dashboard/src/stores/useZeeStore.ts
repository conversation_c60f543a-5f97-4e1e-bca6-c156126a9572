import { create } from 'zustand';

import {
  Zee,
  ZeeBackground,
  ZeeFinancial,
  ZeeOtherFinancial,
} from '@/types/zee.type';

export interface ZeeState {
  zees: Zee[] | undefined;
  setZees: (zees: <PERSON>ee[]) => void;

  // Individual components for easier access
  zee: Zee | undefined;
  zeeFinancial: ZeeFinancial | undefined;
  zeeBackground: ZeeBackground | undefined;
  zeeOtherFinancial: ZeeOtherFinancial | undefined;

  setZee: (zee: Zee) => void;
  setZeeFinancial: (zeeFinancial: ZeeFinancial) => void;
  setZeeBackground: (zeeBackground: ZeeBackground) => void;
  setZeeOtherFinancial: (zeeOtherFinancial: ZeeOtherFinancial) => void;

  clearCurrentZeeProfile: () => void;
}

const useZeeStore = create<ZeeState>((set) => ({
  zees: undefined,
  setZees: (zees: Zee[]) => set(() => ({ zees })),

  zee: undefined,
  zeeFinancial: undefined,
  zeeOtherFinancial: undefined,
  zeeBackground: undefined,

  setZee: (zee: Zee) => set(() => ({ zee })),
  setZeeFinancial: (zeeFinancial: ZeeFinancial) =>
    set(() => ({ zeeFinancial })),
  setZeeOtherFinancial: (zeeOtherFinancial: ZeeOtherFinancial) =>
    set(() => ({ zeeOtherFinancial })),
  setZeeBackground: (zeeBackground: ZeeBackground) =>
    set(() => ({ zeeBackground })),

  clearCurrentZeeProfile: () =>
    set(() => ({
      currentZeeProfile: undefined,
      zee: undefined,
      zeeFinancial: undefined,
      zeeOtherFinancial: undefined,
      zeeBackground: undefined,
    })),
}));

export default useZeeStore;
