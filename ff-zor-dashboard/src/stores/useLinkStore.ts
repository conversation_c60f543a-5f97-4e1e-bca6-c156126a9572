import { create } from 'zustand';

import { Link } from '@/types/link.type';

export interface LinkState {
  links: Array<Link> | [];
  getLinks: () => Array<Link> | [];
  setLinks: (links: Array<Link> | []) => void;
  createLink: (link: Link) => void;
  updateLink: (id: string, link: Link) => void;
  deleteLink: (id: string) => void;
}

const useLinkStore = create<LinkState>((set, get) => ({
  links: [],
  getLinks: () => get().links,
  setLinks: (links: Array<Link> | []) => set(() => ({ links })),
  createLink: (link: Link) =>
    set((state) => ({ links: [...state.links, link] })),
  updateLink: (id: string, link: Link) =>
    set((state) => ({
      links: state.links
        ? state.links.map((item) => (item.id === parseInt(id) ? link : item))
        : [],
    })),
  deleteLink: (id: string) =>
    set((state) => ({
      links: state.links.filter((item) => item.id !== parseInt(id)),
    })),
}));

export default useLinkStore;
