import { create } from 'zustand';

import { LinkResponse } from '@/types/link.type';

interface LinksState {
  links: LinkResponse[];
  isLoading: boolean;
  error: string | null;
  setLinks: (links: LinkResponse[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  addLink: (link: LinkResponse) => void;
  updateLink: (linkId: number, updatedLink: LinkResponse) => void;
  removeLink: (linkId: number) => void;
  getVisibleLinks: (userRole: string) => LinkResponse[];
}

const useLinksStore = create<LinksState>((set, get) => ({
  links: [],
  isLoading: false,
  error: null,
  setLinks: (links) => set({ links, error: null }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  addLink: (link) =>
    set((state) => ({
      links: [...state.links, link],
    })),
  updateLink: (linkId, updatedLink) =>
    set((state) => ({
      links: state.links.map((link) =>
        link.id === linkId ? updatedLink : link
      ),
    })),
  removeLink: (linkId) =>
    set((state) => ({
      links: state.links.filter((link) => link.id !== linkId),
    })),
  getVisibleLinks: (userRole: string) => {
    const { links } = get();

    // Filter links based on user role following backend logic
    return links.filter((link) => {
      // Users with partner_consultant and zee roles can see franchisee and consultant links
      if (userRole.includes('partner_consultant') || userRole.includes('zee')) {
        return link.franchisee_dashboard || link.consultant_dashboard;
      }

      // All other users can only see franchisor links
      return link.franchisor_dashboard;
    });
  },
}));

export default useLinksStore;
