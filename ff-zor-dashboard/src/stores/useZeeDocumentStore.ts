import { create } from 'zustand';

import { ZeeDocument } from '@/types/zee.document.type';

export interface ZeeDocumentState {
  zeeDocuments: ZeeDocument[] | undefined;
  setZeeDocuments: (zeeDocuments: ZeeDocument[]) => void;
}

const useZeeDocumentStore = create<ZeeDocumentState>((set) => ({
  zeeDocuments: undefined,
  setZeeDocuments: (zeeDocuments: ZeeDocument[]) =>
    set(() => ({ zeeDocuments })),
}));

export { useZeeDocumentStore };
