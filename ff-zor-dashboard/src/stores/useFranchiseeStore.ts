import { create } from 'zustand';

import { Franchisee } from '@/types/franchisee.type';

export interface FranchiseeState {
  franchisees: Franchisee[] | undefined;
  setFranchisees: (franchisees: Franchisee[]) => void;
}

const useFranchiseeStore = create<FranchiseeState>((set) => ({
  franchisees: undefined,
  setFranchisees: (franchisees: Franchisee[]) => set(() => ({ franchisees })),
}));

export default useFranchiseeStore;
