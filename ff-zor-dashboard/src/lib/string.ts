import logger from '@/services/logger.service';

/**
 * Truncates text to a specified length and adds ellipsis if necessary
 * @param text The string to truncate
 * @param maxLength Maximum length before truncation
 * @returns Truncated string with ellipsis if needed
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';
  if (maxLength < 0) throw new Error('maxLength must be positive');
  return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;
};

/**
 * Converts bytes to human-readable file size in MB
 * @param sizeInBytes File size in bytes
 * @returns Formatted string with MB unit
 */
export const formatFileSize = (sizeInBytes: number): string => {
  if (sizeInBytes <= 0) return '0 MB';
  const sizeInMB = sizeInBytes / (1024 * 1024);
  return `${sizeInMB.toFixed(2)} MB`;
};

/**
 * Formats a number to always have two digits with leading zero if needed
 * @param number Number to format
 * @returns Two-digit string representation
 */
export const formatToTwoDigits = (number: number): string => {
  if (number < 0 || number > 99)
    throw new Error('Number must be between 0 and 99');
  return number.toString().padStart(2, '0');
};

/**
 * Capitalizes the first letter of a string
 * @param str Input string
 * @returns String with first letter capitalized
 */
export const capitalizeFirstLetter = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Formats a number according to the specified locale
 * @param amount Number to format
 * @param locale Locale string (defaults to 'en-US')
 * @returns Localized string representation of the number
 */
export const formatToLocalizedString = (
  amount: number,
  locale: string = 'en-US'
): string => {
  try {
    return amount.toLocaleString(locale);
  } catch (error) {
    logger.warn(`Invalid locale: ${locale}, falling back to en-US`, {
      locale,
      amount,
    });
    logger.error('Failed to format number with locale', error as Error, {
      locale,
      amount,
    });
    return amount.toLocaleString('en-US');
  }
};

/**
 * Gets the file type from a file name or URL
 * @param fileName File name or URL string
 * @returns File type based on file extension
 */
export const getFileType = (
  fileName: string
):
  | 'doc'
  | 'pdf'
  | 'excel'
  | 'image'
  | 'video'
  | 'audio'
  | 'archive'
  | 'code'
  | 'default' => {
  const lowerFileName = fileName.toLowerCase();

  // Image files
  if (lowerFileName.match(/\.(jpg|jpeg|png|gif|svg|webp|bmp|tiff|ico)$/)) {
    return 'image';
  }

  // Document files
  if (lowerFileName.endsWith('.pdf')) {
    return 'pdf';
  }

  if (lowerFileName.match(/\.(doc|docx|txt|rtf)$/)) {
    return 'doc';
  }

  if (lowerFileName.match(/\.(xls|xlsx|csv)$/)) {
    return 'excel';
  }

  // Video files
  if (lowerFileName.match(/\.(mp4|avi|mov|wmv|flv|webm|mkv|m4v)$/)) {
    return 'video';
  }

  // Audio files
  if (lowerFileName.match(/\.(mp3|wav|flac|aac|ogg|wma|m4a)$/)) {
    return 'audio';
  }

  // Archive files
  if (lowerFileName.match(/\.(zip|rar|7z|tar|gz|bz2)$/)) {
    return 'archive';
  }

  // Code files
  if (
    lowerFileName.match(
      /\.(js|ts|jsx|tsx|html|css|scss|sass|json|xml|yml|yaml|py|java|cpp|c|php|rb|go|rs|swift)$/
    )
  ) {
    return 'code';
  }

  // Default fallback
  return 'default';
};

/**
 * Gets the file type label from a file name or URL
 * @param fileName File name or URL string
 * @returns File type label based on file extension
 */
export const getFileTypeLabel = (fileName: string): string => {
  const fileType = getFileType(fileName);
  const lowerFileName = fileName.toLowerCase();

  switch (fileType) {
    case 'image':
      if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg'))
        return 'JPG';
      if (lowerFileName.endsWith('.png')) return 'PNG';
      if (lowerFileName.endsWith('.gif')) return 'GIF';
      if (lowerFileName.endsWith('.svg')) return 'SVG';
      if (lowerFileName.endsWith('.webp')) return 'WEBP';
      return 'IMG';
    case 'pdf':
      return 'PDF';
    case 'doc':
      if (lowerFileName.endsWith('.txt')) return 'TXT';
      if (lowerFileName.endsWith('.rtf')) return 'RTF';
      return 'DOC';
    case 'excel':
      if (lowerFileName.endsWith('.csv')) return 'CSV';
      return 'EXCEL';
    case 'video':
      if (lowerFileName.endsWith('.mp4')) return 'MP4';
      if (lowerFileName.endsWith('.avi')) return 'AVI';
      if (lowerFileName.endsWith('.mov')) return 'MOV';
      return 'VIDEO';
    case 'audio':
      if (lowerFileName.endsWith('.mp3')) return 'MP3';
      if (lowerFileName.endsWith('.wav')) return 'WAV';
      return 'AUDIO';
    case 'archive':
      if (lowerFileName.endsWith('.zip')) return 'ZIP';
      if (lowerFileName.endsWith('.rar')) return 'RAR';
      return 'ARCHIVE';
    case 'code':
      if (lowerFileName.endsWith('.js')) return 'JS';
      if (lowerFileName.endsWith('.ts')) return 'TS';
      if (lowerFileName.endsWith('.jsx')) return 'JSX';
      if (lowerFileName.endsWith('.tsx')) return 'TSX';
      if (lowerFileName.endsWith('.html')) return 'HTML';
      if (lowerFileName.endsWith('.css')) return 'CSS';
      if (lowerFileName.endsWith('.json')) return 'JSON';
      return 'CODE';
    default:
      return 'FILE';
  }
};

/**
 * Formats a number to a string with commas and no decimal places
 * @param value Number to format
 * @returns Formatted string with commas
 */
export const formatValue = (value: number): string => {
  if (value <= 0) return 'Unknown';
  return Math.round(value).toLocaleString();
};

/**
 * Formats a number to a string with two decimal places
 * @param value Number to format
 * @returns Formatted string with two decimal places
 */
export const formatDecimal = (value: number): string => {
  if (value <= 0) return 'Unknown';
  return value.toFixed(2);
};
