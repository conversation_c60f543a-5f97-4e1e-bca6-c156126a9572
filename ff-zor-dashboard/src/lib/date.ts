/**
 * Converts a UTC date string to a Date object in the local timezone
 * @param dateString - UTC date string to convert
 * @returns Date object adjusted to local timezone
 */
export const getLocalDateFromUTC = (dateString: string): Date => {
  const date = new Date(dateString);
  const utcDate = new Date(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds()
  );
  return utcDate;
};

/**
 * Formats a date to a string in the local timezone
 * @param date - Date object to format
 * @returns Formatted date string
 */
export const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

/**
 * Formats last online timestamp into a human-readable relative time
 * @param lastOnlineAt - ISO timestamp string or null
 * @returns Human-readable string like "2 hours ago", "3 days ago", or "Never online"
 */
export const formatLastOnline = (lastOnlineAt: string | null): string => {
  if (!lastOnlineAt) {
    return 'Never online';
  }

  const now = new Date();
  const lastOnline = new Date(lastOnlineAt);
  const diffInMs = now.getTime() - lastOnline.getTime();

  // Convert to different time units
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  } else if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`;
  } else {
    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`;
  }
};
