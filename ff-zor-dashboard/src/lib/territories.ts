import { CensusVariables } from '@/constants/territories/census-variables';
import { POLYGON_COLORS } from '@/constants/territories/colors';
import { US_STATES } from '@/constants/territories/states';
import { AreaInfo } from '@/types/territories/area.info.type';
import { TerritorySettings } from '@/types/territories/territory.settings.type';
import { TerritoryStatus } from '@/types/territories/territory.status.type';

export interface AggregatedStats {
  state: string;
  zctaCodes: string[];
  population: number;
  median_age: number;
  birth_rate: number;
  median_home_value: number;
  median_household_income: number;
  pop_over_40: number;
  extraCensusData: { code: string; value: number; label: string }[];
}

/**
 * Aggregates statistics from an array of AreaInfo objects.
 * @param areaInfos - Array of AreaInfo to aggregate.
 * @returns AggregatedStats object.
 */
export const calculateAggregatedStats = (
  areaInfos: AreaInfo[]
): AggregatedStats => {
  if (!areaInfos.length) {
    throw new Error('No areaInfos provided');
  }

  const zctaCodes = areaInfos.map((area) => area.zcta_code);
  const state = areaInfos[0].state;

  // These will be used for normalization after reduce
  const weightSums = new Map<string, number>();
  const censusSums = new Map<
    string,
    { value: number; label: string; weightBy?: string }
  >();

  const aggregated = areaInfos.reduce(
    (acc, area) => {
      const {
        population,
        median_age,
        birth_rate,
        median_home_value,
        median_household_income,
        pop_over_40,
        extra_census_data,
      } = area;

      acc.population += population;
      acc.median_age += median_age * population;
      acc.birth_rate += birth_rate * population;
      acc.median_home_value += median_home_value * population;
      acc.median_household_income += median_household_income * population;
      acc.pop_over_40 += pop_over_40;

      if (extra_census_data) {
        for (const extraData of extra_census_data) {
          if (!extraData.label) continue;

          const matchingVariable = Object.values(CensusVariables).find(
            (v) => v.code === extraData.code
          );

          const weightCode = matchingVariable?.weightBy;
          let pop = 1;

          if (weightCode) {
            pop =
              extra_census_data.find((item) => item.code === weightCode)
                ?.value || 0;
            weightSums.set(weightCode, (weightSums.get(weightCode) || 0) + pop);
          }

          const prev = censusSums.get(extraData.code);
          const multiplier = matchingVariable?.per
            ? (2.5 * matchingVariable.per) / 5
            : pop;
          censusSums.set(extraData.code, {
            label: extraData.label,
            weightBy: weightCode,
            value: (prev?.value || 0) + extraData.value * multiplier,
          });
        }
      }

      return acc;
    },
    {
      population: 0,
      median_age: 0,
      birth_rate: 0,
      median_home_value: 0,
      median_household_income: 0,
      pop_over_40: 0,
    }
  );

  // Normalize weighted values
  const extraCensusData = Array.from(censusSums.entries()).map(
    ([code, { value, label, weightBy }]) => {
      if (weightBy) {
        const totalWeight = weightSums.get(weightBy) || 1;
        value = value / totalWeight;
      }
      return { code, value, label };
    }
  );

  return {
    state,
    zctaCodes,
    population: aggregated.population,
    median_age: aggregated.median_age / aggregated.population,
    birth_rate: aggregated.birth_rate / aggregated.population,
    median_home_value: aggregated.median_home_value / aggregated.population,
    median_household_income:
      aggregated.median_household_income / aggregated.population,
    pop_over_40: aggregated.pop_over_40,
    extraCensusData,
  };
};

export const isDecimal = (code: string): boolean => {
  return Object.values(CensusVariables).some(
    (variable) =>
      variable.code === code &&
      !variable.label.toLowerCase().includes('income') &&
      !variable.label.toLowerCase().includes('value')
  );
};
export const getTerritoryColor = (
  status: TerritoryStatus,
  territorySettings: TerritorySettings | undefined
): string => {
  const territoryColor = territorySettings?.colors
    ? territorySettings?.colors[
        status.toLowerCase() as Lowercase<TerritoryStatus>
      ]
    : POLYGON_COLORS[status];

  return territoryColor;
};

export const getFirstMatchingStateCode = (value: string): string => {
  if (!value.trim()) return '';

  const lowerSearch = value.toLowerCase();

  const match = Object.entries(US_STATES).find(([, fullName]) =>
    fullName.toLowerCase().includes(lowerSearch)
  );

  return match?.[0] || value;
};
