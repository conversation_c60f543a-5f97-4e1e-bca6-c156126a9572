import Payment from 'payment';

type CardIssuer =
  | 'jcb'
  | 'amex'
  | 'visa'
  | 'unknown'
  | 'discover'
  | 'dinersclub'
  | 'mastercard';

interface CardPatterns {
  [key: string]: RegExp;
}

const CARD_PATTERNS: CardPatterns = {
  JCB: /^(?:2131|1800|35\d{3})\d{11}$/,
  Visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
  Discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
  MasterCard: /^5[1-5][0-9]{14}$/,
  DinersClub: /^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,
  AmericanExpress: /^3[47][0-9]{13}$/,
} as const;

/**
 * Removes all non-digit characters from a string
 */
const clearNumber = (value: string = ''): string => {
  return value.replace(/\D+/g, '');
};

/**
 * Formats a credit card number with proper spacing based on card type
 */
export const formatCreditCardNumber = (value: string): string => {
  if (!value) return '';

  const issuer = Payment.fns.cardType(value) as CardIssuer;
  const clearValue = clearNumber(value);

  const formatters: Record<CardIssuer, (value: string) => string> = {
    amex: (v) => `${v.slice(0, 4)} ${v.slice(4, 10)} ${v.slice(10, 15)}`,
    dinersclub: (v) => `${v.slice(0, 4)} ${v.slice(4, 10)} ${v.slice(10, 14)}`,
    visa: (v) =>
      `${v.slice(0, 4)} ${v.slice(4, 8)} ${v.slice(8, 12)} ${v.slice(12, 19)}`,
    mastercard: (v) =>
      `${v.slice(0, 4)} ${v.slice(4, 8)} ${v.slice(8, 12)} ${v.slice(12, 19)}`,
    discover: (v) =>
      `${v.slice(0, 4)} ${v.slice(4, 8)} ${v.slice(8, 12)} ${v.slice(12, 19)}`,
    jcb: (v) =>
      `${v.slice(0, 4)} ${v.slice(4, 8)} ${v.slice(8, 12)} ${v.slice(12, 19)}`,
    unknown: (v) =>
      `${v.slice(0, 4)} ${v.slice(4, 8)} ${v.slice(8, 12)} ${v.slice(12, 19)}`,
  };

  const formatter = formatters[issuer] || formatters.unknown;
  return formatter(clearValue).trim();
};

/**
 * Formats a card verification code (CVC)
 * @param value The input CVC value
 * @param cardType Optional card type to determine max length
 */
export const formatCVC = (value: string, cardType?: CardIssuer): string => {
  const maxLength = cardType === 'amex' ? 4 : 3;
  return clearNumber(value).slice(0, maxLength);
};

/**
 * Formats an expiration date into MM/YY format
 */
export const formatExpirationDate = (value: string): string => {
  const clearValue = clearNumber(value);

  if (!clearValue) return '';

  if (clearValue.length >= 3) {
    const month = clearValue.slice(0, 2);
    const year = clearValue.slice(2, 4);

    // Validate month
    const monthNum = parseInt(month, 10);
    if (monthNum < 1 || monthNum > 12) return clearValue;

    return `${month}/${year}`;
  }

  return clearValue;
};

/**
 * Determines the card type based on card number
 */
export const getCardType = (cardNumber: string): CardIssuer => {
  if (!cardNumber) return 'unknown';

  const cleanNumber = clearNumber(cardNumber);

  for (const [key, pattern] of Object.entries(CARD_PATTERNS)) {
    if (pattern.test(cleanNumber)) {
      return key.toLowerCase() as CardIssuer;
    }
  }

  return 'unknown';
};
