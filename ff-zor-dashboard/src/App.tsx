import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Navigate, Route, Routes } from 'react-router-dom';

import ErrorBoundary from '@/components/global/ErrorBoundary';
import ProtectedRoute from '@/components/global/ProtectedRoute';
import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import AuthRoutes from '@/routes/AuthRoutes';
import DashboardRoutes from '@/routes/DashboardRoutes';

const queryClient = new QueryClient();

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Routes>
            {/* Public Routes (when not signed in) */}
            <Route path="/auth/*" element={<AuthRoutes />} />

            {/* Protected Routes (when signed in) */}
            <Route
              path="/dashboard/*"
              element={
                <ProtectedRoute>
                  <DashboardRoutes />
                </ProtectedRoute>
              }
            />

            {/* Fallback for unmatched routes */}
            <Route path="*" element={<Navigate to="/auth/sign-in" replace />} />
          </Routes>
          <Toaster />
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
