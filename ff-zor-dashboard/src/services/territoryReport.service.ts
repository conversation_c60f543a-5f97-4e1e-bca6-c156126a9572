import { jsPDF } from 'jspdf';

import { US_STATES } from '@/constants/territories/states';
import { DEFAULT_CENSUS_DATA_FIELDS } from '@/constants/territories/variables';
import { formatDecimal, formatValue } from '@/lib/string';
import { getTerritoryColor, isDecimal } from '@/lib/territories';
import logger from '@/services/logger.service';
import { CensusFieldKey } from '@/types/territories/census.field.type';
import { TerritorySettings } from '@/types/territories/territory.settings.type';
import { Territory } from '@/types/territories/territory.type';

export class TerritoryReportService {
  private static calculateZoomLevel(coordinates: number[][][]): number {
    // Get all points from the polygon
    const points = coordinates.flat();

    // Find the bounds
    let minLat = Infinity;
    let maxLat = -Infinity;
    let minLng = Infinity;
    let maxLng = -Infinity;

    points.forEach(([lng, lat]) => {
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);
    });

    // Calculate the span
    const latSpan = maxLat - minLat;
    const lngSpan = maxLng - minLng;

    // Add 20% padding
    const padding = 1.2;
    const latSpanWithPadding = latSpan * padding;
    const lngSpanWithPadding = lngSpan * padding;

    // Calculate zoom level
    // The formula is based on the relationship between zoom level and the span of the map
    // We use the larger of the two spans to ensure the entire territory fits
    const span = Math.max(latSpanWithPadding, lngSpanWithPadding);
    const zoom = Math.floor(Math.log2(360 / span));

    // Clamp zoom level between 5 and 15
    return Math.min(Math.max(zoom, 5), 15);
  }

  private static async getStaticMapUrl(
    territory: Territory,
    width: number,
    height: number,
    territorySettings: TerritorySettings | undefined
  ): Promise<string> {
    const apiKey = import.meta.env.VITE_PUBLIC_GOOGLE_MAPS_API_KEY;

    // Get the center coordinates from the territory's centroid
    const [centerLng, centerLat] = territory.centroid.coordinates;
    const territoryColor = getTerritoryColor(
      territory.status,
      territorySettings
    );

    // Create path parameter for the polygon
    const pathParam =
      territory.geom.type === 'Polygon'
        ? territory.geom.coordinates[0]
            .map((coord) => `${coord[1]},${coord[0]}`)
            .join('|')
        : territory.geom.coordinates[0][0]
            .map((coord) => `${coord[1]},${coord[0]}`)
            .join('|');

    // Calculate zoom level based on the territory bounds
    const zoom = this.calculateZoomLevel(
      territory.geom.type === 'Polygon'
        ? territory.geom.coordinates
        : territory.geom.coordinates[0]
    );

    // Construct the URL with all parameters
    const url = new URL('https://maps.googleapis.com/maps/api/staticmap');
    url.searchParams.append('center', `${centerLat},${centerLng}`);
    url.searchParams.append('zoom', zoom.toString());
    url.searchParams.append('size', `${width}x${height}`);
    url.searchParams.append('maptype', 'roadmap');
    url.searchParams.append(
      'path',
      `color:0x${territoryColor.slice(1)}|weight:2|fillcolor:0x${territoryColor.slice(1)}80|${pathParam}`
    );
    url.searchParams.append('key', apiKey);

    return url.toString();
  }

  private static createTerritoryInfoTable(
    doc: jsPDF,
    territory: Territory,
    y: number
  ): number {
    const lineHeight = 7;
    let currentY = y;
    const assignedUser =
      territory.status == 'PENDING'
        ? territory.requested_by
        : territory.assigned_to;

    // Add territory information
    doc.setFontSize(12);
    doc.text('Territory Information', 20, currentY);
    currentY += lineHeight * 2;

    doc.setFontSize(10);
    const info = [
      ['Territory Number:', territory.id.toString().padStart(5, '0')],
      ['State:', US_STATES[territory.state as keyof typeof US_STATES]],
      ['Status:', territory.status],
      ['Zip Codes:', territory.zcta_codes.join(', ')],
      [
        'Assigned To:',
        assignedUser && territory.status != 'AVAILABLE'
          ? assignedUser.full_name
          : 'N/A',
      ],
    ];

    info.forEach(([label, value]) => {
      doc.text(`${label} ${value}`, 20, currentY);
      currentY += lineHeight;
    });

    return currentY;
  }

  private static createDemographicTable(
    doc: jsPDF,
    territory: Territory,
    y: number
  ): number {
    const lineHeight = 7;
    let currentY = y;

    // Add demographic information header
    doc.setFontSize(12);
    doc.text('Demographic Information', 20, currentY);
    currentY += lineHeight * 2;

    doc.setFontSize(10);
    const demographics: [string, string][] = [
      ...DEFAULT_CENSUS_DATA_FIELDS.map(({ id, label }) => {
        const rawValue = territory[id as CensusFieldKey];
        const formatted =
          id.includes('age') && id.includes('median')
            ? formatDecimal(rawValue)
            : formatValue(rawValue);
        return [label, formatted ?? 'N/A'] as [string, string];
      }),
      ...(territory.extra_census_data?.map(
        (item) =>
          [
            item.label,
            isDecimal(item.code)
              ? (formatDecimal(item.value) ?? 'N/A')
              : (formatValue(item.value) ?? 'N/A'),
          ] as [string, string]
      ) || []),
    ];

    demographics.forEach(([label, value]) => {
      doc.text(`${label} ${value}`, 20, currentY);
      currentY += lineHeight;
    });

    return currentY;
  }

  public static async generateReport(
    territory: Territory,
    territorySettings: TerritorySettings | undefined
  ): Promise<void> {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const imgWidth = pageWidth - 40;
    const imgHeight = (imgWidth * 400) / 640;

    // Add title
    doc.setFontSize(16);
    doc.text('Territory Report', pageWidth / 2, 20, { align: 'center' });

    try {
      // Get static map URL
      const mapUrl = await this.getStaticMapUrl(
        territory,
        640,
        400,
        territorySettings
      );

      // Add map image
      doc.addImage(mapUrl, 'JPEG', 20, 30, imgWidth, imgHeight);

      // Add territory information
      const infoY = 30 + imgHeight + 20;
      const afterInfoY = this.createTerritoryInfoTable(doc, territory, infoY);

      // Add demographic information
      this.createDemographicTable(doc, territory, afterInfoY + 20);

      // Save the PDF
      doc.save(`territory-${territory.id}-report.pdf`);
    } catch (error) {
      logger.error('Failed to generate territory report PDF', error as Error, {
        territoryId: territory.id,
        territoryState: territory.state,
      });
      throw error;
    }
  }
}
