import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getWorkbook from '@/actions/workbook/getWorkbook';
import { ApiResponse } from '@/types/response.type';
import { Workbook } from '@/types/workbook.type';

export interface UseGetWorkbookProps {
  result: ApiResponse<Workbook> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetWorkbook = (): UseGetWorkbookProps => {
  const query: UseQueryResult<ApiResponse<Workbook>, Error> = useQuery({
    queryKey: ['workbook'],
    queryFn: () => getWorkbook(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
