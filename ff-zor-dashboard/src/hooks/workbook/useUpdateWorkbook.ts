import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateWorkbook, {
  UpdateWorkbookApiPayload,
} from '@/actions/workbook/updateWorkbook';
import { ApiResponse } from '@/types/response.type';
import { Workbook } from '@/types/workbook.type';

export interface UseUpdateWorkbookReturn {
  mutate: (
    payload: UpdateWorkbookApiPayload
  ) => Promise<ApiResponse<Workbook> | undefined>;
  result: ApiResponse<Workbook> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateWorkbook = (): UseUpdateWorkbookReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Workbook> | undefined,
    Error,
    UpdateWorkbookApiPayload
  > = useMutation({
    mutationFn: updateWorkbook,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
