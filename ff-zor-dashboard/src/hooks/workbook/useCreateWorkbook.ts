import { useMutation, UseMutationResult } from '@tanstack/react-query';

import createWorkbook, {
  CreateWorkbookApiPayload,
} from '@/actions/workbook/createWorkbook';
import { ApiResponse } from '@/types/response.type';
import { Workbook } from '@/types/workbook.type';

export interface UseCreateWorkbookReturn {
  mutate: (
    payload: CreateWorkbookApiPayload
  ) => Promise<ApiResponse<Workbook> | undefined>;
  result: ApiResponse<Workbook> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateWorkbook = (): UseCreateWorkbookReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Workbook> | undefined,
    Error,
    CreateWorkbookApiPayload
  > = useMutation({
    mutationFn: createWorkbook,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
