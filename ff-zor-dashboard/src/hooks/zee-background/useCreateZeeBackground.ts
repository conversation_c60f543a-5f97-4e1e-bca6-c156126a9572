import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';

import {
  createZeeBackground,
  CreateZeeBackgroundApiPayload,
} from '@/actions/zee-background/createZeeBackground';
import { ApiResponse } from '@/types/response.type';
import { ZeeBackground } from '@/types/zee.type';

export interface UseCreateZeeBackgroundReturn {
  mutate: (
    payload: CreateZeeBackgroundApiPayload
  ) => Promise<ApiResponse<ZeeBackground> | undefined>;
  result: ApiResponse<ZeeBackground> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateZeeBackground = (): UseCreateZeeBackgroundReturn => {
  const queryClient = useQueryClient();

  const mutation: UseMutationResult<
    ApiResponse<ZeeBackground> | undefined,
    Error,
    CreateZeeBackgroundApiPayload
  > = useMutation({
    mutationFn: createZeeBackground,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['zee-background'] });
    },
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
