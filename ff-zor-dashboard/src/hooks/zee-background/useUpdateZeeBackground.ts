import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';

import { CreateZeeBackgroundApiPayload } from '@/actions/zee-background/createZeeBackground';
import { updateZeeBackground } from '@/actions/zee-background/updateZeeBackground';
import { ApiResponse } from '@/types/response.type';
import { ZeeBackground } from '@/types/zee.type';

export interface UseUpdateZeeBackgroundReturn {
  mutate: (
    payload: CreateZeeBackgroundApiPayload
  ) => Promise<ApiResponse<ZeeBackground> | undefined>;
  result: ApiResponse<ZeeBackground> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateZeeBackground = (
  zeeId: string
): UseUpdateZeeBackgroundReturn => {
  const queryClient = useQueryClient();

  const mutation: UseMutationResult<
    ApiResponse<ZeeBackground> | undefined,
    Error,
    CreateZeeBackgroundApiPayload
  > = useMutation({
    mutationFn: (payload) => updateZeeBackground(zeeId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['zee-background', zeeId] });
    },
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
