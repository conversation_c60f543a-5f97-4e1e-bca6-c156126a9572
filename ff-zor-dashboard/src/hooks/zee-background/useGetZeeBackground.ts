import { useQuery, UseQueryResult } from '@tanstack/react-query';

import { getZeeBackground } from '@/actions/zee-background/getZeeBackground';
import { ApiResponse } from '@/types/response.type';
import { ZeeBackground } from '@/types/zee.type';

export interface UseGetZeeBackgroundReturn {
  result: ApiResponse<ZeeBackground> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetZeeBackground = (
  zeeId: string
): UseGetZeeBackgroundReturn => {
  const query: UseQueryResult<ApiResponse<ZeeBackground> | undefined, Error> =
    useQuery({
      queryKey: ['zee-background', zeeId],
      queryFn: () => getZeeBackground(zeeId),
    });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
