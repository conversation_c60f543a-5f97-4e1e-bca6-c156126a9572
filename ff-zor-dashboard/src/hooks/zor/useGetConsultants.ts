import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getConsultants from '@/actions/zor/getConsultants';
import { ConsultantApiResponse } from '@/types/data-center/consultant.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetConsultantsProps {
  result: ApiResponse<ConsultantApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
}

export const useGetConsultants = (): UseGetConsultantsProps => {
  const query: UseQueryResult<
    ApiResponse<ConsultantApiResponse>,
    Error
  > = useQuery({
    queryKey: ['consultants-data'],
    queryFn: () => getConsultants(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
