import { useMutation, UseMutationResult } from '@tanstack/react-query';

import { ZorMeetingLink } from '@/actions/zor/getZorMeetingLink';
import updateZorMeetingLink, {
  UpdateZorMeetingLinkPayload,
} from '@/actions/zor/updateZorMeetingLink';
import { ApiResponse } from '@/types/response.type';

export interface UseUpdateZorMeetingLinkReturn {
  mutate: (
    payload: UpdateZorMeetingLinkPayload
  ) => Promise<ApiResponse<ZorMeetingLink> | undefined>;
  result: ApiResponse<ZorMeetingLink> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateZorMeetingLink = (): UseUpdateZorMeetingLinkReturn => {
  const mutation: UseMutationResult<
    ApiResponse<ZorMeetingLink> | undefined,
    Error,
    UpdateZorMeetingLinkPayload
  > = useMutation({
    mutationFn: updateZorMeetingLink,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
