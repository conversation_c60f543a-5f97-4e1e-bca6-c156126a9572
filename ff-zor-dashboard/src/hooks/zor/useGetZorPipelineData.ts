import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZorPipelineData from '@/actions/zor/getZorPipelineData';
import { PipelineApiResponse } from '@/types/data-center/pipeline.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZorPipelineDataProps {
  result: ApiResponse<PipelineApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
}

export const useGetZorPipelineData = (): UseGetZorPipelineDataProps => {
  const query: UseQueryResult<
    ApiResponse<PipelineApiResponse>,
    Error
  > = useQuery({
    queryKey: ['pipeline-data'],
    queryFn: () => getZorPipelineData(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
