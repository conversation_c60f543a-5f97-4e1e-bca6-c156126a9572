import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZorMeetingLink, {
  ZorMeetingLink,
} from '@/actions/zor/getZorMeetingLink';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZorMeetingLinkReturn {
  result: ApiResponse<ZorMeetingLink> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
  refetch: () => void;
}

export const useGetZorMeetingLink = (): UseGetZorMeetingLinkReturn => {
  const query: UseQueryResult<ApiResponse<ZorMeetingLink> | undefined, Error> =
    useQuery({
      queryKey: ['zor-meeting-link'],
      queryFn: getZorMeetingLink,
    });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
    refetch: query.refetch,
  };
};
