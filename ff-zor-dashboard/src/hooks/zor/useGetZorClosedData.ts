import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZorClosedData from '@/actions/zor/getZorClosedData';
import { ClosedApiResponse } from '@/types/data-center/closed.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZorClosedDataProps {
  result: ApiResponse<ClosedApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
}

export const useGetZorClosedData = (): UseGetZorClosedDataProps => {
  const query: UseQueryResult<ApiResponse<ClosedApiResponse>, Error> = useQuery(
    {
      queryKey: ['closed-data'],
      queryFn: () => getZorClosedData(),
    }
  );

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
