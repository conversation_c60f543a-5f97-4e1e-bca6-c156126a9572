import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZorDiscoveryData from '@/actions/zor/getZorDiscoveryData';
import { DiscoveryApiResponse } from '@/types/data-center/discovery.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZorDiscoveryDataProps {
  result: ApiResponse<DiscoveryApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
}

export const useGetZorDiscoveryData = (): UseGetZorDiscoveryDataProps => {
  const query: UseQueryResult<
    ApiResponse<DiscoveryApiResponse>,
    Error
  > = useQuery({
    queryKey: ['discovery-data'],
    queryFn: () => getZorDiscoveryData(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
