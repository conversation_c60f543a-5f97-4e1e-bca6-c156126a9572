import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZorLeadsData from '@/actions/zor/getZorLeadsData';
import { LeadsApiResponse } from '@/types/data-center/leads.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZorLeadsDataProps {
  result: ApiResponse<LeadsApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
  refetch: () => void;
}

export const useGetZorLeadsData = (): UseGetZorLeadsDataProps => {
  const query: UseQueryResult<ApiResponse<LeadsApiResponse>, Error> = useQuery({
    queryKey: ['leads-data'],
    queryFn: () => getZorLeadsData(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
    refetch: query.refetch,
  };
};
