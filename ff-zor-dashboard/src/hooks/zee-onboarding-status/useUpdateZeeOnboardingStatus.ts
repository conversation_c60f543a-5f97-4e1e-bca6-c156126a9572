import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateZeeOnboardingStatus from '@/actions/zee-onboarding-status/updateZeeOnboardingStatus';
import { ApiResponse } from '@/types/response.type';
import {
  UpdateZeeOnboardingStatusByZorRequest,
  ZeeOnboardingStatusResponse,
} from '@/types/zee.onboarding.status.type';

export interface UseUpdateZeeOnboardingStatusProps {
  mutate: (data: {
    zee_id: string;
    payload: UpdateZeeOnboardingStatusByZorRequest;
  }) => Promise<ApiResponse<ZeeOnboardingStatusResponse>>;
  result: ApiResponse<ZeeOnboardingStatusResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateZeeOnboardingStatus =
  (): UseUpdateZeeOnboardingStatusProps => {
    const mutation: UseMutationResult<
      ApiResponse<ZeeOnboardingStatusResponse>,
      Error,
      { zee_id: string; payload: UpdateZeeOnboardingStatusByZorRequest }
    > = useMutation({
      mutationFn: ({ zee_id, payload }) =>
        updateZeeOnboardingStatus(zee_id, payload),
    });

    return {
      mutate: mutation.mutateAsync,
      result: mutation.data,
      error: mutation.error,
      isError: mutation.isError,
      isLoading: mutation.isPending,
      isSuccess: mutation.isSuccess,
    };
  };
