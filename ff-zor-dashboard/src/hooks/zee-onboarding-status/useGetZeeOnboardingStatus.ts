import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZeeOnboardingStatus from '@/actions/zee-onboarding-status/getZeeOnboardingStatus';
import { ApiResponse } from '@/types/response.type';
import { ComprehensiveZeeOnboardingResponse } from '@/types/zee.onboarding.status.type';

export interface UseGetZeeOnboardingStatusReturn {
  result: ApiResponse<ComprehensiveZeeOnboardingResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetZeeOnboardingStatus = (
  zee_id: string
): UseGetZeeOnboardingStatusReturn => {
  const query: UseQueryResult<
    ApiResponse<ComprehensiveZeeOnboardingResponse> | undefined,
    Error
  > = useQuery({
    queryKey: ['zee-onboarding-status', zee_id],
    queryFn: () => getZeeOnboardingStatus(zee_id),
    enabled: !!zee_id,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
