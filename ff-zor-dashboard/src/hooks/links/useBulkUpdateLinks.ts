import { useMutation, UseMutationResult } from '@tanstack/react-query';

import bulkUpdateLinks from '@/actions/links/bulkUpdateLinks';
import { BulkUpdateLinksRequest, LinkResponse } from '@/types/link.type';
import { ApiResponse } from '@/types/response.type';

export interface UseBulkUpdateLinksReturn {
  mutate: (
    payload: BulkUpdateLinksRequest
  ) => Promise<ApiResponse<LinkResponse[]>>;
  result: ApiResponse<LinkResponse[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useBulkUpdateLinks = (): UseBulkUpdateLinksReturn => {
  const mutation: UseMutationResult<
    ApiResponse<LinkResponse[]>,
    Error,
    BulkUpdateLinksRequest
  > = useMutation({
    mutationFn: bulkUpdateLinks,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
