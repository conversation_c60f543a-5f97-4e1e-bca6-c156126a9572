import { useMutation, UseMutationResult } from '@tanstack/react-query';

import bulkOperations from '@/actions/links/bulkOperations';
import { BulkOperationsRequest, LinkResponse } from '@/types/link.type';
import { ApiResponse } from '@/types/response.type';

export interface UseBulkOperationsReturn {
  mutate: (
    payload: BulkOperationsRequest
  ) => Promise<ApiResponse<LinkResponse[]>>;
  result: ApiResponse<LinkResponse[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useBulkOperations = (): UseBulkOperationsReturn => {
  const mutation: UseMutationResult<
    ApiResponse<LinkResponse[]>,
    Error,
    BulkOperationsRequest
  > = useMutation({
    mutationFn: bulkOperations,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
