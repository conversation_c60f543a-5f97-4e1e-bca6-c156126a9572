import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getLinks from '@/actions/links/getLinks';
import { LinkResponse } from '@/types/link.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetLinksReturn {
  result: ApiResponse<LinkResponse[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetLinks = (): UseGetLinksReturn => {
  const query: UseQueryResult<ApiResponse<LinkResponse[]>, Error> = useQuery({
    queryKey: ['links'],
    queryFn: getLinks,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
