import { useMutation, UseMutationResult } from '@tanstack/react-query';

import deleteLink from '@/actions/links/deleteLink';
import { ApiResponse } from '@/types/response.type';

export interface UseDeleteLinkReturn {
  mutate: (linkId: number) => Promise<ApiResponse<{ id: number }>>;
  result: ApiResponse<{ id: number }> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useDeleteLink = (): UseDeleteLinkReturn => {
  const mutation: UseMutationResult<
    ApiResponse<{ id: number }>,
    Error,
    number
  > = useMutation({
    mutationFn: deleteLink,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
