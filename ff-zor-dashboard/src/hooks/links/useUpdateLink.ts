import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateLink from '@/actions/links/updateLink';
import { LinkResponse, UpdateLinkRequest } from '@/types/link.type';
import { ApiResponse } from '@/types/response.type';

export interface UseUpdateLinkReturn {
  mutate: (params: {
    linkId: number;
    payload: UpdateLinkRequest;
  }) => Promise<ApiResponse<LinkResponse>>;
  result: ApiResponse<LinkResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateLink = (): UseUpdateLinkReturn => {
  const mutation: UseMutationResult<
    ApiResponse<LinkResponse>,
    Error,
    { linkId: number; payload: UpdateLinkRequest }
  > = useMutation({
    mutationFn: ({ linkId, payload }) => updateLink(linkId, payload),
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
