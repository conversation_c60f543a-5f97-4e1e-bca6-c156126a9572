import { useMutation, UseMutationResult } from '@tanstack/react-query';

import createLink from '@/actions/links/createLink';
import { CreateLinkRequest, LinkResponse } from '@/types/link.type';
import { ApiResponse } from '@/types/response.type';

export interface UseCreateLinkReturn {
  mutate: (payload: CreateLinkRequest) => Promise<ApiResponse<LinkResponse>>;
  result: ApiResponse<LinkResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateLink = (): UseCreateLinkReturn => {
  const mutation: UseMutationResult<
    ApiResponse<LinkResponse>,
    Error,
    CreateLinkRequest
  > = useMutation({
    mutationFn: createLink,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
