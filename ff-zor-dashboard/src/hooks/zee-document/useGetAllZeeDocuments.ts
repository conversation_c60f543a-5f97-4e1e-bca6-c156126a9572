import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZeeDocuments from '@/actions/zee/getZeeDocuments';
import { ApiResponse } from '@/types/response.type';
import { ZeeDocument } from '@/types/zee.document.type';

export interface UseGetAllZeeDocumentsReturn {
  result: ApiResponse<ZeeDocument[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetAllZeeDocuments = (
  zeeId: string
): UseGetAllZeeDocumentsReturn => {
  const query: UseQueryResult<ApiResponse<ZeeDocument[]> | undefined, Error> =
    useQuery({
      queryKey: ['zee-documents'],
      queryFn: () => getZeeDocuments(zeeId),
      enabled: true,
    });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
