import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateEmailTemplate, {
  UpdateEmailTemplateApiPayload,
} from '@/actions/email-templates/updateEmailTemplate';
import { EmailTemplate } from '@/types/email-templates/email.template.type';
import { ApiResponse } from '@/types/response.type';

export interface UseUpdateEmailTemplateReturn {
  mutate: ({
    id,
    payload,
  }: {
    id: string;
    payload: UpdateEmailTemplateApiPayload;
  }) => Promise<ApiResponse<EmailTemplate> | undefined>;
  result: ApiResponse<EmailTemplate> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateEmailTemplate = (): UseUpdateEmailTemplateReturn => {
  const mutation: UseMutationResult<
    ApiResponse<EmailTemplate> | undefined,
    Error,
    {
      id: string;
      payload: UpdateEmailTemplateApiPayload;
    }
  > = useMutation({
    mutationFn: updateEmailTemplate,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
