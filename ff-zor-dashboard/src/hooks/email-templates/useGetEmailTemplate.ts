import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getEmailTemplate from '@/actions/email-templates/getEmailTemplate';
import { EmailTemplate } from '@/types/email-templates/email.template.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetEmailTemplateProps {
  result: ApiResponse<EmailTemplate> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetEmailTemplate = (
  id: string | undefined
): UseGetEmailTemplateProps => {
  const query: UseQueryResult<ApiResponse<EmailTemplate>, Error> = useQuery({
    queryKey: ['email-template', id],
    queryFn: () => getEmailTemplate(id!),
    staleTime: 0,
    gcTime: 0,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
