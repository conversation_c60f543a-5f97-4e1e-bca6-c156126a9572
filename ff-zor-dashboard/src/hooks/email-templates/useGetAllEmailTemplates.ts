import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getEmailTemplates from '@/actions/email-templates/getEmailTemplates';
import { EmailTemplate } from '@/types/email-templates/email.template.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetEmailTemplatesProps {
  result: ApiResponse<EmailTemplate[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
  refetch: () => void;
}

export const useGetEmailTemplates = (): UseGetEmailTemplatesProps => {
  const query: UseQueryResult<ApiResponse<EmailTemplate[]>, Error> = useQuery({
    queryKey: ['email-templates'],
    queryFn: getEmailTemplates,
    staleTime: 0,
    gcTime: 0,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
    refetch: query.refetch,
  };
};
