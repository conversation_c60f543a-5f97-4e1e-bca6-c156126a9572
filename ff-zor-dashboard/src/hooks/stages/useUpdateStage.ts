import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateStage, {
  UpdateStageApiPayload,
} from '@/actions/stages/updateStage';
import { ApiResponse } from '@/types/response.type';
import { Stage } from '@/types/stage.type';

export interface UseUpdateStageProps {
  mutate: (data: UpdateStageApiPayload) => Promise<ApiResponse<Stage>>;
  result: ApiResponse<Stage> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateStage = (): UseUpdateStageProps => {
  const mutation: UseMutationResult<
    ApiResponse<Stage>,
    Error,
    UpdateStageApiPayload
  > = useMutation({
    mutationFn: updateStage,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
