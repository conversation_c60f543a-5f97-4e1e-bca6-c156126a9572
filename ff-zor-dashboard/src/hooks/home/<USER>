import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getActiveCandidates from '@/actions/home/<USER>';
import { ActiveCandidatesData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetActiveCandidatesProps {
  result: ApiResponse<ActiveCandidatesData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetActiveCandidates = (): UseGetActiveCandidatesProps => {
  const query: UseQueryResult<
    ApiResponse<ActiveCandidatesData>,
    Error
  > = useQuery({
    queryKey: ['active-candidates'],
    queryFn: () => getActiveCandidates(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
