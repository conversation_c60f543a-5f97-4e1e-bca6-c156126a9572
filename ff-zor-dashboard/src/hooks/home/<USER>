import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getTopEngagedCandidates from '@/actions/home/<USER>';
import { TopEngagedCandidatesData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetTopEngagedCandidatesProps {
  result: ApiResponse<TopEngagedCandidatesData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetTopEngagedCandidates =
  (): UseGetTopEngagedCandidatesProps => {
    const query: UseQueryResult<
      ApiResponse<TopEngagedCandidatesData>,
      Error
    > = useQuery({
      queryKey: ['top-engaged-candidates'],
      queryFn: () => getTopEngagedCandidates(),
    });

    return {
      result: query.data,
      error: query.error,
      isError: query.isError,
      isSuccess: query.isSuccess,
      isLoading: query.isPending,
      isRefetching: query.isRefetching,
    };
  };
