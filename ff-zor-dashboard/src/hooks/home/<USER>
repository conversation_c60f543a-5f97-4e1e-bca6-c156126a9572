import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getTopConsultantRate from '@/actions/home/<USER>';
import { TopConsultantRateData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetTopConsultantRateProps {
  result: ApiResponse<TopConsultantRateData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetTopConsultantRate = (): UseGetTopConsultantRateProps => {
  const query: UseQueryResult<
    ApiResponse<TopConsultantRateData>,
    Error
  > = useQuery({
    queryKey: ['top-consultant-rate'],
    queryFn: () => getTopConsultantRate(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
