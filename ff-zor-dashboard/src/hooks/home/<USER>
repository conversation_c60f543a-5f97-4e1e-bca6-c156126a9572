import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getGeographicDistribution from '@/actions/home/<USER>';
import { GeographicDistributionData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetGeographicDistributionProps {
  result: ApiResponse<GeographicDistributionData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetGeographicDistribution =
  (): UseGetGeographicDistributionProps => {
    const query: UseQueryResult<
      ApiResponse<GeographicDistributionData>,
      Error
    > = useQuery({
      queryKey: ['geographic-distribution'],
      queryFn: () => getGeographicDistribution(),
    });

    return {
      result: query.data,
      error: query.error,
      isError: query.isError,
      isSuccess: query.isSuccess,
      isLoading: query.isPending,
      isRefetching: query.isRefetching,
    };
  };
