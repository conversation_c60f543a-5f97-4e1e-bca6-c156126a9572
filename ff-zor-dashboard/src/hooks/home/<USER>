import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getDiscoveryFunnel from '@/actions/home/<USER>';
import { DiscoveryFunnelData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetDiscoveryFunnelProps {
  result: ApiResponse<DiscoveryFunnelData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetDiscoveryFunnel = (): UseGetDiscoveryFunnelProps => {
  const query: UseQueryResult<
    ApiResponse<DiscoveryFunnelData>,
    Error
  > = useQuery({
    queryKey: ['discovery-funnel'],
    queryFn: () => getDiscoveryFunnel(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
