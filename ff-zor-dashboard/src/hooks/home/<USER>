import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getFunnelCloseRate from '@/actions/home/<USER>';
import { FunnelCloseRateData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetFunnelCloseRateProps {
  result: ApiResponse<FunnelCloseRateData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetFunnelCloseRate = (): UseGetFunnelCloseRateProps => {
  const query: UseQueryResult<
    ApiResponse<FunnelCloseRateData>,
    Error
  > = useQuery({
    queryKey: ['funnel-close-rate'],
    queryFn: () => getFunnelCloseRate(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
