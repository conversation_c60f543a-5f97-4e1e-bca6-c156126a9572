import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getAvgTimeline from '@/actions/home/<USER>';
import { AvgTimelineData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetAvgTimelineProps {
  result: ApiResponse<AvgTimelineData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetAvgTimeline = (): UseGetAvgTimelineProps => {
  const query: UseQueryResult<ApiResponse<AvgTimelineData>, Error> = useQuery({
    queryKey: ['avg-timeline'],
    queryFn: () => getAvgTimeline(),
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
