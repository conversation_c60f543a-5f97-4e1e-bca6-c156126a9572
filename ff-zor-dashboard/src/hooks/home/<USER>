import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getConsultantsPerformance from '@/actions/home/<USER>';
import { ConsultantsPerformanceData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetConsultantsPerformanceProps {
  result: ApiResponse<ConsultantsPerformanceData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetConsultantsPerformance =
  (): UseGetConsultantsPerformanceProps => {
    const query: UseQueryResult<
      ApiResponse<ConsultantsPerformanceData>,
      Error
    > = useQuery({
      queryKey: ['consultants-performance'],
      queryFn: () => getConsultantsPerformance(),
    });

    return {
      result: query.data,
      error: query.error,
      isError: query.isError,
      isSuccess: query.isSuccess,
      isLoading: query.isPending,
      isRefetching: query.isRefetching,
    };
  };
