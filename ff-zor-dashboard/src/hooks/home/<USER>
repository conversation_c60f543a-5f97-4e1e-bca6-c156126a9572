import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getCandidateStageMetrics from '@/actions/home/<USER>';
import { CandidateStageMetricsData } from '@/types/home.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetCandidateStageMetricsProps {
  result: ApiResponse<CandidateStageMetricsData> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetCandidateStageMetrics =
  (): UseGetCandidateStageMetricsProps => {
    const query: UseQueryResult<
      ApiResponse<CandidateStageMetricsData>,
      Error
    > = useQuery({
      queryKey: ['candidate-stage-metrics'],
      queryFn: () => getCandidateStageMetrics(),
    });

    return {
      result: query.data,
      error: query.error,
      isError: query.isError,
      isSuccess: query.isSuccess,
      isLoading: query.isPending,
      isRefetching: query.isRefetching,
    };
  };
