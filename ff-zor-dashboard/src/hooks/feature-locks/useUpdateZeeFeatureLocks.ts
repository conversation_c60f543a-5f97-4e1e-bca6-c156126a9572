import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateZeeFeatureLocks, {
  UpdateZeeFeatureLocksApiPayload,
} from '@/actions/feature-locks/updateZeeFeatureLocks';
import { ZeeFeatureLocks } from '@/types/feature.locks.type';
import { ApiResponse } from '@/types/response.type';

export interface UseUpdateZeeFeatureLocksProps {
  mutate: (
    data: UpdateZeeFeatureLocksApiPayload
  ) => Promise<ApiResponse<ZeeFeatureLocks>>;
  result: ApiResponse<ZeeFeatureLocks> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateZeeFeatureLocks = (
  zeeId: string
): UseUpdateZeeFeatureLocksProps => {
  const mutation: UseMutationResult<
    ApiResponse<ZeeFeatureLocks>,
    Error,
    UpdateZeeFeatureLocksApiPayload
  > = useMutation({
    mutationFn: (payload: UpdateZeeFeatureLocksApiPayload) =>
      updateZeeFeatureLocks(zeeId, payload),
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
