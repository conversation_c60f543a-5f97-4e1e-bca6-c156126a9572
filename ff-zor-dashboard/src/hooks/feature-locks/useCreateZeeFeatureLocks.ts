import { useMutation, UseMutationResult } from '@tanstack/react-query';

import createZeeFeatureLocks, {
  CreateZeeFeatureLocksApiPayload,
} from '@/actions/feature-locks/createZeeFeatureLocks';
import { ZeeFeatureLocks } from '@/types/feature.locks.type';
import { ApiResponse } from '@/types/response.type';

export interface UseCreateZeeFeatureLocksProps {
  mutate: (
    data: CreateZeeFeatureLocksApiPayload
  ) => Promise<ApiResponse<ZeeFeatureLocks>>;
  result: ApiResponse<ZeeFeatureLocks> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateZeeFeatureLocks = (
  zeeId: string
): UseCreateZeeFeatureLocksProps => {
  const mutation: UseMutationResult<
    ApiResponse<ZeeFeatureLocks>,
    Error,
    CreateZeeFeatureLocksApiPayload
  > = useMutation({
    mutationFn: (payload: CreateZeeFeatureLocksApiPayload) =>
      createZeeFeatureLocks(zeeId, payload),
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
