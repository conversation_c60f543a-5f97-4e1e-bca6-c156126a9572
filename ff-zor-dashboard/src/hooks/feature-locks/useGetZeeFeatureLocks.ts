import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZeeFeatureLocks from '@/actions/feature-locks/getZeeFeatureLocks';
import { ZeeFeatureLocks } from '@/types/feature.locks.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZeeFeatureLocksProps {
  result: ApiResponse<ZeeFeatureLocks> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetZeeFeatureLocks = (
  zeeId: string
): UseGetZeeFeatureLocksProps => {
  const query: UseQueryResult<ApiResponse<ZeeFeatureLocks>, Error> = useQuery({
    queryKey: ['zee-feature-locks', zeeId],
    queryFn: () => getZeeFeatureLocks(zeeId),
    enabled: !!zeeId,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
