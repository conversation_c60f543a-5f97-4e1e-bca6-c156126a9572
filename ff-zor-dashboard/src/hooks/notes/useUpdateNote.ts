import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateNote from '@/actions/notes/updateNote';
import { Note, UpdateNoteRequest } from '@/types/note.type';
import { ApiResponse } from '@/types/response.type';

export interface UseUpdateNoteProps {
  mutate: (data: {
    noteId: string;
    payload: UpdateNoteRequest;
  }) => Promise<ApiResponse<Note>>;
  result: ApiResponse<Note> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateNote = (): UseUpdateNoteProps => {
  const mutation: UseMutationResult<
    ApiResponse<Note>,
    Error,
    { noteId: string; payload: UpdateNoteRequest }
  > = useMutation({
    mutationFn: ({ noteId, payload }) => updateNote(noteId, payload),
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
