import { useMutation, UseMutationResult } from '@tanstack/react-query';

import deleteNote from '@/actions/notes/deleteNote';
import { ApiResponse } from '@/types/response.type';

export interface UseDeleteNoteProps {
  mutate: (noteId: string) => Promise<ApiResponse<boolean>>;
  result: ApiResponse<boolean> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useDeleteNote = (): UseDeleteNoteProps => {
  const mutation: UseMutationResult<
    ApiResponse<boolean>,
    Error,
    string
  > = useMutation({
    mutationFn: deleteNote,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
