import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getNotesAttachments from '@/actions/notes/getNotesAttachments';
import { NoteAttachment } from '@/types/note.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetNotesAttachmentsProps {
  data: ApiResponse<NoteAttachment[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  refetch: () => void;
}

export const useGetNotesAttachments = (
  zee_id: string
): UseGetNotesAttachmentsProps => {
  const query: UseQueryResult<ApiResponse<NoteAttachment[]>, Error> = useQuery({
    queryKey: ['notes-attachments', zee_id],
    queryFn: () => getNotesAttachments(zee_id),
    enabled: !!zee_id,
  });

  return {
    data: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isLoading,
    refetch: query.refetch,
  };
};
