import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getNotes from '@/actions/notes/getNotes';
import { Note } from '@/types/note.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetNotesProps {
  data: ApiResponse<Note[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  refetch: () => void;
}

export const useGetNotes = (zee_id: string): UseGetNotesProps => {
  const query: UseQueryResult<ApiResponse<Note[]>, Error> = useQuery({
    queryKey: ['notes', zee_id],
    queryFn: () => getNotes(zee_id),
    enabled: !!zee_id,
  });

  return {
    data: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isLoading,
    refetch: query.refetch,
  };
};
