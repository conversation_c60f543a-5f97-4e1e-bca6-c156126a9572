import { useMutation, UseMutationResult } from '@tanstack/react-query';

import createNote from '@/actions/notes/createNote';
import { CreateNoteRequest, Note } from '@/types/note.type';
import { ApiResponse } from '@/types/response.type';

export interface UseCreateNoteProps {
  mutate: (data: CreateNoteRequest) => Promise<ApiResponse<Note>>;
  result: ApiResponse<Note> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateNote = (): UseCreateNoteProps => {
  const mutation: UseMutationResult<
    ApiResponse<Note>,
    Error,
    CreateNoteRequest
  > = useMutation({
    mutationFn: createNote,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
