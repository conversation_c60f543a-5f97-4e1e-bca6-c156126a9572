import { useMutation, UseMutationResult } from '@tanstack/react-query';

import sendOrgInvitation, {
  revokeOrgInvitation,
  RevokeOrgInvitationApiPayload,
  SendOrgInvitationApiPayload,
} from '@/actions/auth/sendOrgInvitation';
import { ApiResponse } from '@/types/response.type';

export interface UseSendOrgInvitationReturn {
  mutate: (
    payload: SendOrgInvitationApiPayload
  ) => Promise<ApiResponse<object> | undefined>;
  result: ApiResponse<object> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useSendOrgInvitation = (): UseSendOrgInvitationReturn => {
  const mutation: UseMutationResult<
    ApiResponse<object> | undefined,
    Error,
    SendOrgInvitationApiPayload
  > = useMutation({
    mutationFn: sendOrgInvitation,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};

export interface UseRevokeOrgInvitationReturn {
  mutate: (
    payload: RevokeOrgInvitationApiPayload
  ) => Promise<ApiResponse<object> | undefined>;
  result: ApiResponse<object> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useRevokeOrgInvitation = (): UseRevokeOrgInvitationReturn => {
  const mutation: UseMutationResult<
    ApiResponse<object> | undefined,
    Error,
    RevokeOrgInvitationApiPayload
  > = useMutation({
    mutationFn: revokeOrgInvitation,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
