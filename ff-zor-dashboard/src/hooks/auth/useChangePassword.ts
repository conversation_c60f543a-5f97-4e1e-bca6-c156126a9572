import { useMutation, UseMutationResult } from '@tanstack/react-query';

import changePassword, {
  ChangePasswordApiPayload,
  ChangePasswordApiResponse,
} from '@/actions/auth/changePassword';
import { ApiResponse } from '@/types/response.type';

export interface UseChangePasswordReturn {
  mutate: (
    payload: ChangePasswordApiPayload
  ) => Promise<ApiResponse<ChangePasswordApiResponse> | undefined>;
  result: ApiResponse<ChangePasswordApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useChangePassword = (): UseChangePasswordReturn => {
  const mutation: UseMutationResult<
    ApiResponse<ChangePasswordApiResponse> | undefined,
    Error,
    ChangePasswordApiPayload
  > = useMutation({
    mutationFn: changePassword,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
