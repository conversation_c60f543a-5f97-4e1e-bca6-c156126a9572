import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZeeFinancial, {
  GetZeeFinancialApiResponse,
} from '@/actions/zee/getZeeFinancial';
import { ApiResponse } from '@/types/response.type';

export interface UseGetZeeFinancialReturn {
  result: ApiResponse<GetZeeFinancialApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetZeeFinancial = (zeeId: string): UseGetZeeFinancialReturn => {
  const query: UseQueryResult<
    ApiResponse<GetZeeFinancialApiResponse> | undefined,
    Error
  > = useQuery({
    queryKey: ['zee-financial', zeeId],
    queryFn: () => getZeeFinancial(zeeId),
    enabled: !!zeeId,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
