import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';
import { z } from 'zod';

import { updateZeeProfile } from '@/actions/zee/updateZeeProfile';
import ZeeProfileFormSchema from '@/components/forms/schemas/ZeeProfileFormSchema';
import { ApiResponse } from '@/types/response.type';
import { Zee } from '@/types/zee.type';

export interface UseUpdateZeeReturn {
  mutate: (
    payload: z.infer<typeof ZeeProfileFormSchema>
  ) => Promise<ApiResponse<Zee> | undefined>;
  result: ApiResponse<Zee> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateZeeProfile = (): UseUpdateZeeReturn => {
  const queryClient = useQueryClient();

  const mutation: UseMutationResult<
    ApiResponse<Zee> | undefined,
    Error,
    z.infer<typeof ZeeProfileFormSchema>
  > = useMutation({
    mutationFn: updateZeeProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['zee'] });
    },
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
