import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getZeeProfile from '@/actions/zee/getZee';
import { ApiResponse } from '@/types/response.type';
import { Zee } from '@/types/zee.type';

export interface UseGetZeeProfileReturn {
  result: ApiResponse<Zee> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetZeeProfile = (zeeId: string): UseGetZeeProfileReturn => {
  const query: UseQueryResult<ApiResponse<Zee> | undefined, Error> = useQuery({
    queryKey: ['zee-profile', zeeId],
    queryFn: () => getZeeProfile(zeeId),
    enabled: !!zeeId,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
