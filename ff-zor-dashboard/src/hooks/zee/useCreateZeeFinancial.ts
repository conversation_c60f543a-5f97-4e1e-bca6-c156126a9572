import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';

import { createZeeFinancial } from '@/actions/zee/createZeeFinancial';
import { GetZeeFinancialApiResponse } from '@/actions/zee/getZeeFinancial';
import { UpdateZeeFinancialApiPayload } from '@/actions/zee/updateZeeFinancial';
import { ApiResponse } from '@/types/response.type';

export interface UseCreateZeeFinancialReturn {
  mutate: (
    payload: UpdateZeeFinancialApiPayload
  ) => Promise<ApiResponse<GetZeeFinancialApiResponse> | undefined>;
  result: ApiResponse<GetZeeFinancialApiResponse> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateZeeFinancial = (): UseCreateZeeFinancialReturn => {
  const queryClient = useQueryClient();

  const mutation: UseMutationResult<
    ApiResponse<GetZeeFinancialApiResponse> | undefined,
    Error,
    UpdateZeeFinancialApiPayload
  > = useMutation({
    mutationFn: createZeeFinancial,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['zee-financial'] });
    },
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
