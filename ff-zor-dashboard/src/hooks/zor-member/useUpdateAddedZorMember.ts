import { useMutation, UseMutationResult } from '@tanstack/react-query';

import { AddZorMemberApiPayload } from '@/actions/zor-member/addZorMember';
import updateAddedZorMember from '@/actions/zor-member/updateAddedZorMember';
import { ApiResponse } from '@/types/response.type';
import { ZorMember } from '@/types/zor.member.type';

export interface UseUpdateAddedZorMemberReturn {
  mutate: (
    payload: AddZorMemberApiPayload
  ) => Promise<ApiResponse<ZorMember> | undefined>;
  result: ApiResponse<ZorMember> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateAddedZorMember = (): UseUpdateAddedZorMemberReturn => {
  const mutation: UseMutationResult<
    ApiResponse<ZorMember> | undefined,
    Error,
    AddZorMemberApiPayload
  > = useMutation({
    mutationFn: updateAddedZorMember,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
