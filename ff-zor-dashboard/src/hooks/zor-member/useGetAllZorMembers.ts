import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getAllZorMembers from '@/actions/zor-member/getAllZorMembers';
import { ApiResponse } from '@/types/response.type';
import { ZorMember } from '@/types/zor.member.type';

export interface UseGetAllZorMembersReturn {
  result: ApiResponse<ZorMember[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
  refetch: () => void;
}

export const useGetAllZorMembers = (): UseGetAllZorMembersReturn => {
  const query: UseQueryResult<ApiResponse<ZorMember[]> | undefined, Error> =
    useQuery({
      queryKey: ['zor-members'],
      queryFn: getAllZorMembers,
    });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
    refetch: query.refetch,
  };
};
