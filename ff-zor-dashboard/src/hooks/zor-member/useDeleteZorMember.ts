import { useMutation, UseMutationResult } from '@tanstack/react-query';

import deleteZorMember from '@/actions/zor-member/deleteZorMember';
import { ApiResponse } from '@/types/response.type';

export interface UseDeleteZorMemberReturn {
  mutate: (memberId: string) => Promise<ApiResponse<boolean> | undefined>;
  result: ApiResponse<boolean> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useDeleteZorMember = (): UseDeleteZorMemberReturn => {
  const mutation: UseMutationResult<
    ApiResponse<boolean> | undefined,
    Error,
    string
  > = useMutation({
    mutationFn: deleteZorMember,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
