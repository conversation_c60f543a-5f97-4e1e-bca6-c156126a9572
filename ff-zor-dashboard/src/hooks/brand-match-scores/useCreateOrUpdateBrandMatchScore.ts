import { useMutation, useQueryClient } from '@tanstack/react-query';

import createOrUpdateBrandMatchScore from '@/actions/brand-match-scores/createOrUpdateBrandMatchScore';
import { BrandMatchScoreRequest } from '@/types/brand-match-score.type';

export const useCreateOrUpdateBrandMatchScore = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (data: BrandMatchScoreRequest) =>
      createOrUpdateBrandMatchScore(data),
    onSuccess: (_data, variables) => {
      // Invalidate and refetch the brand match score for this zee
      queryClient.invalidateQueries({
        queryKey: ['brand-match-score', variables.zee_id],
      });
    },
  });

  return {
    mutate: mutation.mutate,
    mutateAsync: mutation.mutateAsync,
    isPending: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
    isSuccess: mutation.isSuccess,
    data: mutation.data,
  };
};
