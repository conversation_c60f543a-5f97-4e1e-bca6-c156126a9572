import { useMutation, useQueryClient } from '@tanstack/react-query';

import deleteBrandMatchScore from '@/actions/brand-match-scores/deleteBrandMatchScore';

export const useDeleteBrandMatchScore = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (zeeId: string) => deleteBrandMatchScore(zeeId),
    onSuccess: (_data, zeeId) => {
      // Invalidate and refetch the brand match score for this zee
      queryClient.invalidateQueries({
        queryKey: ['brand-match-score', zeeId],
      });
    },
  });

  return {
    mutate: mutation.mutate,
    mutateAsync: mutation.mutateAsync,
    isPending: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
    isSuccess: mutation.isSuccess,
    data: mutation.data,
  };
};
