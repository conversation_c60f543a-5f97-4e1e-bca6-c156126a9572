import { useQuery } from '@tanstack/react-query';

import getBrandMatchScore from '@/actions/brand-match-scores/getBrandMatchScore';

export const useGetBrandMatchScore = (zeeId: string, enabled = true) => {
  const {
    data: result,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['brand-match-score', zeeId],
    queryFn: () => getBrandMatchScore(zeeId),
    enabled: enabled && !!zeeId,
    retry: false,
  });

  return {
    result,
    isLoading,
    isError,
    error,
    refetch,
  };
};
