import { useMutation, UseMutationResult } from '@tanstack/react-query';

import deleteUser from '@/actions/user/deleteUser';
import { ApiResponse } from '@/types/response.type';

export interface UseDeleteUserReturn {
  mutate: (userId: string) => Promise<ApiResponse<boolean> | undefined>;
  result: ApiResponse<boolean> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useDeleteUser = (): UseDeleteUserReturn => {
  const mutation: UseMutationResult<
    ApiResponse<boolean> | undefined,
    Error,
    string
  > = useMutation({
    mutationFn: deleteUser,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
