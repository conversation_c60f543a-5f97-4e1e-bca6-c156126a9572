import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getConsultantIndustries from '@/actions/consultant/getConsultantIndustries';
import { ConsultantIndustries } from '@/types/consultant.industries.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetConsultantIndustriesProps {
  result: ApiResponse<ConsultantIndustries> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
}

export const useGetConsultantIndustries = (
  consultantId: string
): UseGetConsultantIndustriesProps => {
  const query: UseQueryResult<
    ApiResponse<ConsultantIndustries>,
    Error
  > = useQuery({
    queryKey: ['consultant-industries', consultantId],
    queryFn: () => getConsultantIndustries(consultantId),
    enabled: !!consultantId,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
