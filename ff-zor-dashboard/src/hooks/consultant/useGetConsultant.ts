import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getConsultant from '@/actions/consultant/getConsultant';
import { Consultant } from '@/types/data-center/consultant.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetConsultantProps {
  result: ApiResponse<Consultant> | undefined;
  error: Error | null;
  isError: boolean;
  isSuccess: boolean;
  isLoading: boolean;
  isRefetching: boolean;
}

export const useGetConsultant = (
  consultantId: string
): UseGetConsultantProps => {
  const query: UseQueryResult<ApiResponse<Consultant>, Error> = useQuery({
    queryKey: ['consultant', consultantId],
    queryFn: () => getConsultant(consultantId),
    enabled: !!consultantId,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
