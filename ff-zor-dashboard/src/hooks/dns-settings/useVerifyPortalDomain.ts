import { useMutation, UseMutationResult } from '@tanstack/react-query';

import verifyPortalDomain, {
  VerifyPortalDomainApiPayload,
} from '@/actions/dns-settings/verifyPortalDomain';
import { Domain } from '@/types/dns-settings/domain.type';
import { ApiResponse } from '@/types/response.type';

export interface UseVerifyPortalDomainReturn {
  mutate: (
    payload: VerifyPortalDomainApiPayload
  ) => Promise<ApiResponse<Domain> | undefined>;
  result: ApiResponse<Domain> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useVerifyPortalDomain = (): UseVerifyPortalDomainReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Domain> | undefined,
    Error,
    VerifyPortalDomainApiPayload
  > = useMutation({
    mutationFn: verifyPortalDomain,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
