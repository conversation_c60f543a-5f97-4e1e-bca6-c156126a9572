import { useMutation, UseMutationResult } from '@tanstack/react-query';

import verifyMailDomains, {
  VerifyMailDomainsApiPayload,
} from '@/actions/dns-settings/verifyMailDomains';
import { Domain } from '@/types/dns-settings/domain.type';
import { ApiResponse } from '@/types/response.type';

export interface UseVerifyMailDomainsReturn {
  mutate: (
    payload: VerifyMailDomainsApiPayload
  ) => Promise<ApiResponse<Domain[]> | undefined>;
  result: ApiResponse<Domain[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useVerifyMailDomains = (): UseVerifyMailDomainsReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Domain[]> | undefined,
    Error,
    VerifyMailDomainsApiPayload
  > = useMutation({
    mutationFn: verifyMailDomains,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
