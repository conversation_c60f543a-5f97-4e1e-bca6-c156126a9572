import { useMutation, UseMutationResult } from '@tanstack/react-query';

import updateDomain, {
  UpdateDomainApiPayload,
} from '@/actions/dns-settings/updateDomain';
import { Domain } from '@/types/dns-settings/domain.type';
import { ApiResponse } from '@/types/response.type';

export interface UseUpdateDomainReturn {
  mutate: (
    payload: UpdateDomainApiPayload
  ) => Promise<ApiResponse<Domain> | undefined>;
  result: ApiResponse<Domain> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useUpdateDomain = (): UseUpdateDomainReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Domain> | undefined,
    Error,
    UpdateDomainApiPayload
  > = useMutation({
    mutationFn: updateDomain,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
