import { useMutation, UseMutationResult } from '@tanstack/react-query';

import createDomain, {
  CreateDomainApiPayload,
} from '@/actions/dns-settings/createDomain';
import { Domain } from '@/types/dns-settings/domain.type';
import { ApiResponse } from '@/types/response.type';

export interface UseCreateDomainReturn {
  mutate: (
    payload: CreateDomainApiPayload
  ) => Promise<ApiResponse<Domain> | undefined>;
  result: ApiResponse<Domain> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useCreateDomain = (): UseCreateDomainReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Domain> | undefined,
    Error,
    CreateDomainApiPayload
  > = useMutation({
    mutationFn: createDomain,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
