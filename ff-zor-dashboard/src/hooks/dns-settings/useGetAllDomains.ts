import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getAllDomains from '@/actions/dns-settings/getAllDomains';
import { Domain } from '@/types/dns-settings/domain.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetAllDomainsProps {
  result: ApiResponse<Domain[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
  refetch: () => void;
}

export const useGetAllDomains = (): UseGetAllDomainsProps => {
  const query: UseQueryResult<ApiResponse<Domain[]>, Error> = useQuery({
    queryKey: ['domains'],
    queryFn: getAllDomains,
    staleTime: 0,
    gcTime: 0,
  });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
    refetch: query.refetch,
  };
};
