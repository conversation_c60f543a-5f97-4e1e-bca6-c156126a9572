import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';

import updateFranchisee, {
  UpdateFranchiseeApiPayload,
  UpdateFranchiseeApiResponse,
} from '@/actions/franchisee/updateFranchisee';
import { ApiResponse } from '@/types/response.type';

export const useUpdateFranchisee = (): UseMutationResult<
  ApiResponse<UpdateFranchiseeApiResponse>,
  Error,
  { id: number; payload: UpdateFranchiseeApiPayload }
> => {
  const queryClient = useQueryClient();

  return useMutation<
    ApiResponse<UpdateFranchiseeApiResponse>,
    Error,
    { id: number; payload: UpdateFranchiseeApiPayload }
  >({
    mutationFn: ({ id, payload }) => updateFranchisee(id, payload),
    onSuccess: () => {
      // Invalidate and refetch the franchisees list after successful update
      queryClient.invalidateQueries({ queryKey: ['franchisees'] });
    },
  });
};
