import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from '@tanstack/react-query';

import createFranchisee, {
  CreateFranchiseeApiPayload,
  CreateFranchiseeApiResponse,
} from '@/actions/franchisee/createFranchisee';
import { ApiResponse } from '@/types/response.type';

export const useCreateFranchisee = (): UseMutationResult<
  ApiResponse<CreateFranchiseeApiResponse>,
  Error,
  CreateFranchiseeApiPayload
> => {
  const queryClient = useQueryClient();

  return useMutation<
    ApiResponse<CreateFranchiseeApiResponse>,
    Error,
    CreateFranchiseeApiPayload
  >({
    mutationFn: (payload: CreateFranchiseeApiPayload) =>
      createFranchisee(payload),
    onSuccess: () => {
      // Invalidate and refetch the franchisees list after successful creation
      queryClient.invalidateQueries({ queryKey: ['franchisees'] });
    },
  });
};
