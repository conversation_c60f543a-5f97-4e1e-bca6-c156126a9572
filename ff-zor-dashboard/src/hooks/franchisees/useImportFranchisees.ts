import { useMutation, UseMutationResult } from '@tanstack/react-query';

import importFranchisees, {
  ImportFranchiseesPayload,
} from '@/actions/franchisee/importFranchisees';
import { Franchisee } from '@/types/franchisee.type';
import { ApiResponse } from '@/types/response.type';

export interface UseImportFranchiseesReturn {
  mutate: (
    payload: ImportFranchiseesPayload
  ) => Promise<ApiResponse<Franchisee[]> | undefined>;
  result: ApiResponse<Franchisee[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
}

export const useImportFranchisees = (): UseImportFranchiseesReturn => {
  const mutation: UseMutationResult<
    ApiResponse<Franchisee[]> | undefined,
    Error,
    ImportFranchiseesPayload
  > = useMutation({
    mutationFn: importFranchisees,
  });

  return {
    mutate: mutation.mutateAsync,
    result: mutation.data,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    isLoading: mutation.isPending,
  };
};
