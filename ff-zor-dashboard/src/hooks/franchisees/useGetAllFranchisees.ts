import { useQuery, UseQueryResult } from '@tanstack/react-query';

import getAllFranchisees from '@/actions/franchisee/getAllFranchisees';
import { Franchisee } from '@/types/franchisee.type';
import { ApiResponse } from '@/types/response.type';

export interface UseGetAllFranchiseesReturn {
  result: ApiResponse<Franchisee[]> | undefined;
  error: Error | null;
  isError: boolean;
  isLoading: boolean;
  isSuccess: boolean;
  isRefetching: boolean;
}

export const useGetAllFranchisees = (): UseGetAllFranchiseesReturn => {
  const query: UseQueryResult<ApiResponse<Franchisee[]> | undefined, Error> =
    useQuery({
      queryKey: ['franchisees'],
      queryFn: getAllFranchisees,
    });

  return {
    result: query.data,
    error: query.error,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isLoading: query.isPending,
    isRefetching: query.isRefetching,
  };
};
