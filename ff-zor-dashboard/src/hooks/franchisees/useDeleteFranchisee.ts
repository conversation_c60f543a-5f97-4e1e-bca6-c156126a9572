import { useMutation, useQueryClient } from '@tanstack/react-query';

import deleteFranchisee from '@/actions/franchisee/deleteFranchisee';
import { ApiResponse } from '@/types/response.type';

export const useDeleteFranchisee = () => {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<boolean>, Error, number>({
    mutationFn: (id: number) => deleteFranchisee(id),
    onSuccess: () => {
      // Invalidate and refetch the franchisees list after successful deletion
      queryClient.invalidateQueries({ queryKey: ['franchisees'] });
    },
  });
};
