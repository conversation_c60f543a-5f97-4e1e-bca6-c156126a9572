import { useClerk, useOrganization, useUser } from '@clerk/clerk-react';
import { CSSProperties, ReactNode } from 'react';
import { Link } from 'react-router-dom';

import DefaultCompanyLogo from '@/assets/icons/default-company-logo.svg?react';
import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import AppSidebar from '@/components/global/AppSidebar';
import { buttonVariants } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { SIDEBAR_WIDTH } from '@/constants/ui/sidebar';
import { cn } from '@/lib/ui/utils';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const { user } = useUser();
  const { signOut } = useClerk();
  const { organization } = useOrganization();

  const handleLogOut = () => {
    window.localStorage.removeItem('access-token');
    signOut({ redirectUrl: '/auth/sign-in' });
  };

  const organizationWebsite = organization?.publicMetadata?.website as string;
  const organizationName = organization?.name || 'Company name';
  const userName = user?.firstName ?? 'Anonymous User';

  return (
    <SidebarProvider
      style={{ '--sidebar-width': `${SIDEBAR_WIDTH}px` } as CSSProperties}
    >
      <AppSidebar />
      <main className="flex-1 overflow-x-auto">
        <header className="sticky top-0 z-10 flex items-center justify-between border-b border-alpha-dark-5 bg-white px-6 py-[19px]">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="xl:hidden" />
            <Link
              to={organizationWebsite || '#'}
              rel="noopener noreferrer"
              target="_blank"
              className="flex items-center gap-3"
            >
              {organization?.imageUrl ? (
                <img
                  src={organization.imageUrl}
                  alt={organizationName}
                  className="h-9 w-9 rounded-full"
                />
              ) : (
                <DefaultCompanyLogo className="h-9 w-9" />
              )}
              <p className="as-title-02 as-strong text-typo-gray-tertiary">
                {organizationName}
              </p>
            </Link>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger
              asChild
              className={cn(
                buttonVariants({
                  size: 'icon',
                  variant: 'ghost',
                  className:
                    'cursor-pointer overflow-hidden rounded-full shadow-md',
                })
              )}
            >
              {user ? (
                <img src={user.imageUrl} alt={userName} />
              ) : (
                <EmptyAvatar className="!h-full !w-full" />
              )}
            </DropdownMenuTrigger>
            <DropdownMenuContent className="mr-6 mt-2">
              <DropdownMenuItem>
                <Link to="/dashboard/profile?tab=information">
                  <p className="w-[200px] p-0.5">Franchise Details</p>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link to="/dashboard/profile?tab=financial">
                  <p className="w-[200px] p-0.5">Billing Details</p>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link to="/dashboard/profile?tab=user-profiles">
                  <p className="w-[200px] p-0.5">Users</p>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link to="/dashboard/profile?tab=my-profile">
                  <p className="w-[200px] p-0.5">My Profile</p>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleLogOut}>
                <p className="w-[200px] p-0.5">Sign Out</p>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </header>
        <div className="px-6 py-6">{children}</div>
      </main>
    </SidebarProvider>
  );
};

export default MainLayout;
