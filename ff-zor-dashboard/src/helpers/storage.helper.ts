import { toast } from '@/hooks/ui/use-toast';
import getSupabaseClient from '@/lib/supabase';
import logger from '@/services/logger.service';

export interface UploadedDocument {
  file_size: number;
  file_url: string;
}

export interface UploadToStorageOptions {
  file: File;
  bucketName: string;
  folderName: string;
  setIsUploading?: (isUploading: boolean) => void;
}

/**
 * Uploads a file to Supabase storage bucket
 * @param options - Upload configuration options
 * @returns Promise that resolves to uploaded document info or null if failed
 */
export const uploadToStorage = async ({
  file,
  bucketName,
  folderName,
  setIsUploading,
}: UploadToStorageOptions): Promise<UploadedDocument | null> => {
  setIsUploading?.(true);

  try {
    const supabase = getSupabaseClient();
    const filePath = `${folderName}/${new Date().getTime()}-${file.name}`;

    const { data: uploadedDocument, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        upsert: true,
      });

    if (error) {
      logger.error(
        `Failed to upload file to Supabase storage bucket: ${bucketName}`,
        error as Error,
        {
          fileName: file.name,
          folderName,
          bucketName,
        }
      );

      toast({
        title: 'Something went wrong',
        description: error.message,
        variant: 'destructive',
      });

      return null;
    }

    if (uploadedDocument) {
      toast({
        title: 'File uploaded successfully',
        variant: 'default',
      });

      const { data: response } = supabase.storage
        .from(bucketName)
        .getPublicUrl(uploadedDocument.path);

      return {
        file_size: file.size,
        file_url: response.publicUrl,
      };
    }

    return null;
  } catch (error) {
    logger.error(
      `Unexpected error during file upload to bucket: ${bucketName}`,
      error as Error,
      {
        fileName: file.name,
        folderName,
        bucketName,
      }
    );

    toast({
      title: 'Something went wrong',
      description: 'Please try again',
      variant: 'destructive',
    });

    return null;
  } finally {
    setIsUploading?.(false);
  }
};
