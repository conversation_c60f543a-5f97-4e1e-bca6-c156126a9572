import axios, { InternalAxiosRequestConfig } from 'axios';

import { PUBLIC_API_URLs } from '@/constants/apis';
import { configuration } from '@/constants/config';
import logger from '@/services/logger.service';

const apiManager = axios.create({
  baseURL: configuration.API_URL,
});

apiManager.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const requestUrl = config.url ?? '';
    if (!PUBLIC_API_URLs.find((url: string) => url.includes(requestUrl))) {
      config.headers['Authorization'] =
        `Bearer ${window.localStorage.getItem('access-token')}`;
    }
    return config;
  },
  (err) => err
);

apiManager.interceptors.response.use(
  (res) => {
    return res;
  },
  (err) => {
    if (!err.response) {
      logger.error('Network error occurred', err, { type: 'HTTP_ERROR' });
      return err;
    }
    logger.error('HTTP request failed', err, {
      type: 'HTTP_ERROR',
      status: err.response?.status,
      data: err.response?.data,
    });
    return err;
  }
);

export default apiManager;
export { apiManager };
