/**
 * Transform raw role values to human-readable labels
 */
export const transformRole = (role: string): string => {
  const roleMap: Record<string, string> = {
    'org:leadership_owner': 'Leadership Owner',
    'org:leadership_administrator': 'Leadership Administrator',
    'org:partner_administrator': 'Partner Administrator',
    'org:partner_consultant': 'Partner Consultant',
    'org:zee': 'Franchisee',
    'org:zor_member': 'ZOR Member', // fallback for any zor_member roles
  };

  return roleMap[role] || role;
};
