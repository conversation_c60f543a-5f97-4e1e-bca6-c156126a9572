@import 'fonts.css';
@import 'colors.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'DMSans', sans-serif;
  }
}

@layer base {
  :root {
    --background: #ffffff;
    --foreground: #18181b;
    --card: #ffffff;
    --card-foreground: #0a0a0b;
    --popover: #ffffff;
    --popover-foreground: #0a0a0b;
    --primary: var(--brand-500);
    --primary-foreground: #ffffff;
    --secondary: #f4f4f5;
    --secondary-foreground: #191919;
    --muted: #f4f4f5;
    --muted-foreground: #71717a;
    --accent: #f4f4f5;
    --accent-foreground: #191919;
    --destructive: #e11d48;
    --destructive-foreground: #fafafa;
    --border: #e4e4e7;
    --input: #e4e4e7;
    --ring: #191919;
    --radius: 0.5rem;
    --chart-1: #e85d3d;
    --chart-2: #2d8a84;
    --chart-3: #233d4d;
    --chart-4: #ecc94b;
    --chart-5: #f26d3d;
  }
}

@layer base {
  :root {
    --sidebar-background: #f4f4f5;
    --sidebar-foreground: #27272a;
    --sidebar-primary: blue;
    --sidebar-primary-foreground: #fafafa;
    --sidebar-accent: var(--primary);
    --sidebar-accent-foreground: #ffffff;
    --sidebar-border: var(--alpha-dark-5);
    --sidebar-ring: #4d9fff;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background font-primary text-foreground;
  }

  /* Custom Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* For Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
  }
}

/* typographies */
@layer base {
  .as-strong {
    @apply font-medium;
  }

  h1 {
    @apply font-primary text-4xl leading-[42px];
  }

  h2 {
    @apply font-primary text-[30px] leading-[36px];
  }

  h3 {
    @apply font-primary text-[26px] leading-[32px];
  }

  h4 {
    @apply font-primary text-[24px] leading-[28px];
  }

  h5 {
    @apply font-primary text-[20px] leading-[24px];
  }

  .as-title-01 {
    @apply font-primary text-[18px] leading-[22px];
  }

  .as-title-02 {
    @apply font-primary text-[16px] leading-[20px];
  }

  .as-title-03 {
    @apply font-primary text-[14px] leading-[20px];
  }

  .as-title-04 {
    @apply font-primary text-[12px] leading-[20px];
  }

  .as-body-01 {
    @apply font-secondary text-[18px] font-light leading-[28px];
  }

  .as-body-02 {
    @apply font-secondary text-[16px] font-light leading-[24px];
  }

  .as-body-03 {
    @apply font-secondary text-[14px] font-light leading-[24px];
  }

  .as-body-04 {
    @apply font-secondary text-[12px] font-light leading-[18px];
  }
  .as-body-05 {
    @apply font-secondary text-[10px] font-light leading-[18px];
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.reactEasyCrop_CropAreaGrid::after {
  border-top: 1px solid var(--alpha-dark-5) !important;
  border-bottom: 1px solid var(--alpha-dark-5) !important;
}

.reactEasyCrop_CropAreaGrid::before {
  border-left: 1px solid var(--alpha-dark-5) !important;
  border-right: 1px solid var(--alpha-dark-5) !important;
}
