import { ConsultantInviter } from '@/types/consultant.inviter.type';

export type Zee = {
  id: string;
  photo_url: string;
  full_name: string;
  birth_date: string;
  city: string;
  state: string;
  zip_code: string;
  address: string;
  phone: string;
  email: string;
  meeting_link: string;
  linkedin: string;
  preferred_markets: string[];
  consultant_inviter?: ConsultantInviter | null;
};

export type ZeeFinancial = {
  zee_id: string;
  employment: string;
  annual_compensation: number;
  spouse_significant_other_name: string;
  spouse_significant_other_employment: string;
  spouse_significant_other_annual_compensation: number;
  spouse_significant_other_phone: string;
  is_veteran: string;
  is_first_responder_and_medical_professional: string;
  credit_score_range: string;
  is_sba_eligible: string;
  owner_type: string[];
  plan_to_have_investor_or_partner: string;
  investor_partner_description: string;
};

export type ZeeOtherFinancial = {
  id: string;
  cash_or_equivalent_assets: number;
  savings_or_certificates: number;
  stocks_or_bonds_or_securities: number;
  retirement_plan_or_ira: number;
  home_market_value: number;
  other_real_estate: number;
  autos: number;
  other_assets: number;
  other_assets_description: string | null;
  home_mortgage_balance: number;
  other_real_estate_debt: number;
  auto_loans: number;
  other_debt: number;
  other_debt_description: string | null;
  total_assets: number;
  total_liabilities: number;
  net_worth: number;
};

export type ZeeBackground = {
  zee_id: string;
  industry_background: string[];
  bankruptcy_involved: boolean;
  criminal_conviction: boolean;
  pending_lawsuits: boolean;
  civil_judgments: boolean;
  affirmative_answers_explanation: string;
  industry_interest: Record<string, number>;
};

export type ZeeProfileData = {
  zee: Zee;
  zee_financial?: ZeeFinancial;
  zee_other_financial?: ZeeOtherFinancial;
  zee_background?: ZeeBackground;
};
