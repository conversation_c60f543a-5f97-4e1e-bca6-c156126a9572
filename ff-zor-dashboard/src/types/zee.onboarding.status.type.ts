export interface StageItemResponse {
  id: string;
  stage_id: string;
  type: string;
  title: string;
  description: string;
  document_id: string | null;
  aimei_prompt_id: string | null;
  external_link: string | null;
  internal_tab: string | null;
  is_pipeline: boolean;
  is_additional_stage: boolean;
  previous_stage_item_id: string | null;
}

export interface DiscoveryStageResponse {
  id: string;
  zor_id: string;
  index: number;
  name: string;
  stage_items: StageItemResponse[];
}

export interface ZeeOnboardingStatusResponse {
  id: string;
  completed_stage_ids: string[];
  completed_stage_item_ids: string[];
}

export interface ComprehensiveZeeOnboardingResponse {
  zee_id: string;
  zee_onboarding_status: ZeeOnboardingStatusResponse | null;
  discovery_stages: DiscoveryStageResponse[];
}

export interface UpdateZeeOnboardingStatusByZorRequest {
  completed_stage_ids: string[];
  completed_stage_item_ids: string[];
}

// Internal types for UI state management
export interface ZeeDiscoveryStage {
  id: string;
  stage_name: string;
  status: 'completed' | 'in-progress' | 'locked';
  locked: boolean;
  stage_items: ZeeStageItem[];
}

export interface ZeeStageItem {
  id: string;
  stage_id: string;
  title: string;
  description: string;
  type: string;
  status: 'completed' | 'locked';
  locked: boolean;
}
