export type ZeeUser = {
  zee_id: string;
  full_name: string;
  email: string;
  photo_url?: string;
  city: string;
  state: string;
  phone: string;
  completed_stage_ids: string[];
  completed_stage_item_ids: string[];
  current_stage: {
    stage_id: string;
    stage_name: string;
    stage_index: number;
    stage_display: string; // NEW formatted display
  };
  current_stage_item: {
    item_id: string;
    item_title: string;
    item_description: string;
    item_type: string;
    item_index: number; // NEW item index
    item_display: string; // NEW formatted display
  };
  total_stages: number;
  completed_stages_count: number;
  total_stage_items: number;
  completed_stage_items_count: number;
  completion_rate: number;
  brand_match_score: number | null; // Compatibility rating
  lead_source: {
    inviter_name: string;
    inviter_email: string;
    inviter_photo_url: string;
    inviter_role: string;
  } | null; // null if zee wasn't invited
};

export type DiscoveryApiResponse = {
  zor_id: string;
  zee_users: ZeeUser[];
  total_zee_count: number;
};

// Keep the old Discovery type for backward compatibility and add compatibility_rating for mock data
export type Discovery = {
  id?: string;
  email?: string;
  photo_url?: string;
  full_name?: string;
  current_stage?: number;
  compatibility_rating?: number;
};
