export interface InviterInfo {
  name: string;
  photo_url: string;
  email: string;
  role: string;
}

export interface InviteeProfile {
  name: string;
  email: string;
  photo_url: string;
}

export interface Lead {
  id: number;
  invitee_email: string;
  status: 'SENT' | 'ACCEPTED';
  invitation_id: string;
  preferred_markets: string[];
  created_at: string;
  invitee_name: string;
  inviter_info: InviterInfo | null;
  invitee_profile: InviteeProfile | null;
}

export interface StatusBreakdown {
  SENT: number;
  ACCEPTED: number;
}

export interface LeadsApiResponse {
  zor_id: string;
  leads: Lead[];
  total_leads_count: number;
  status_breakdown: StatusBreakdown;
}
