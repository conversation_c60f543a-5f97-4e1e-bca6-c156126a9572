export type ZeeMember = {
  zee_id: string;
  full_name: string;
  email: string;
  photo_url?: string;
  city: string;
  state: string;
  phone: string;
  last_online_at: string | null; // ISO timestamp or null if never online
};

export type PipelineStageItem = {
  stage_item_id: string;
  stage_item_title: string;
  stage_item_description: string;
  stage_item_type: string;
  stage_id: string;
  stage_name: string;
  zee_members: ZeeMember[];
  zee_count: number;
};

export type PipelineApiResponse = {
  zor_id: string;
  pipeline_stage_items: PipelineStageItem[];
  total_pipeline_items: number;
  total_zee_members_in_pipeline: number;
};
