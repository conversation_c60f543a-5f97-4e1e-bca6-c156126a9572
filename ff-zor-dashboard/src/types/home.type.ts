// Home Dashboard KPI Types based on /v1/zor-home API documentation

export interface FunnelCloseRateData {
  zor_id: string;
  close_rate_percentage: number;
  closed_count: number;
  started_count: number;
  description: string;
}

export interface AvgTimelineData {
  zor_id: string;
  avg_timeline_days: number;
  completed_count: number;
  description: string;
}

export interface ActiveCandidatesData {
  zor_id: string;
  active_candidates_count: number;
  description: string;
}

export interface TopConsultantRateData {
  zor_id: string;
  top_conversion_rate: number;
  consultant_name: string;
  consultant_id?: string;
  closed_count?: number;
  total_count?: number;
  description: string;
}

export interface GeographicDistributionData {
  zor_id: string;
  distribution: [
    {
      state: string;
      count: number;
    },
  ];
}

export interface TopEngagedCandidateItem {
  zee_id: string;
  full_name: string;
  time_on_platform: number;
  actions: number;
  stage: string;
}

export interface TopEngagedCandidatesData {
  zor_id: string;
  candidates: TopEngagedCandidateItem[];
  count: number;
}

export interface ConsultantsPerformanceData {
  zor_id: string;
  consultants: {
    consultant_id: string;
    consultant_name: string;
    consultant_email: string;
    consultant_firm_affiliated: string[];
    conversion_rate: number;
    closed_count: number;
    total_count: number;
  }[];
  total_consultants: number;
}

export interface FunnelMetric {
  stage_id: string;
  stage_name: string;
  stage_index: number;
  completed_count: number;
  completion_percentage: number;
  total_zees: number;
}

export type DiscoveryFunnelData = FunnelMetric[];

// Candidate stage metrics types for CandidateMetricsChart
export interface CandidateStageMetric {
  stage_id: string;
  stage_name: string;
  candidates: number;
  avg_days: number;
  completion_rate: number; // percent (0-100)
}

export interface CandidateStageMetricsData {
  zor_id: string;
  metrics: CandidateStageMetric[];
}
