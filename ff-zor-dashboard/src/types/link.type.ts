export type Link = {
  id: number;
  title: string;
  link: string;
  franchisor_dashboard: boolean;
  consultant_dashboard: boolean;
  franchisee_dashboard: boolean;
};

export type CreateLinkRequest = {
  title: string;
  link: string;
  franchisor_dashboard: boolean;
  consultant_dashboard: boolean;
  franchisee_dashboard: boolean;
};

export type UpdateLinkRequest = {
  title?: string;
  link?: string;
  franchisor_dashboard?: boolean;
  consultant_dashboard?: boolean;
  franchisee_dashboard?: boolean;
};

export type BulkUpdateLinksRequest = {
  links: (UpdateLinkRequest & { id: number })[];
};

export type BulkOperationsRequest = {
  updates?: (UpdateLinkRequest & { id: number })[];
  deletes?: number[];
};

export type LinkResponse = {
  id: number;
  title: string;
  link: string;
  franchisor_dashboard: boolean;
  consultant_dashboard: boolean;
  franchisee_dashboard: boolean;
};

export type BulkOperationsResponse = {
  updated_links: LinkResponse[];
  deleted_link_ids: number[];
};
