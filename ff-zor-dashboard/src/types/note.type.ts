export type NoteCategory =
  | 'calls'
  | 'meeting'
  | 'manual-email'
  | 'system-emails'
  | 'firefiles'
  | 'general';

export interface Attachment {
  id: string;
  file_name: string;
  file_path: string;
  file_url: string;
  file_size: number;
  file_type: string;
  created_at: string;
  updated_at: string;
}

export interface Note {
  id: string;
  zee_id: string;
  title: string;
  content: string;
  category: NoteCategory;
  author_id?: string;
  author_name?: string;
  author_avatar?: string;
  author_role?: string;
  is_automated: boolean;
  created_at: string;
  updated_at: string;
  attachments: Attachment[];
}

export interface CreateNoteRequest {
  zee_id: string;
  title: string;
  content: string;
  category: NoteCategory;
  author_id?: string;
  author_name?: string;
  author_avatar?: string;
  author_role?: string;
  is_automated?: boolean;
  attachments?: {
    file_name: string;
    file_path: string;
    file_url: string;
    file_size: number;
    file_type: string;
  }[];
}

export interface UpdateNoteRequest {
  title?: string;
  content?: string;
  category?: NoteCategory;
  author_id?: string;
  author_name?: string;
  author_avatar?: string;
  author_role?: string;
  is_automated?: boolean;
}

export interface AddNoteData {
  title: string;
  content: string;
  category: NoteCategory;
  file?: File;
}

export interface NoteAttachment {
  id: string;
  note_id: string;
  file_name: string;
  file_path: string;
  file_url: string;
  file_size: number;
  file_type: string;
  created_at: string;
  updated_at: string;
  note_type: string;
  note_title: string;
  detailed_notes: string;
  note_author_name: string;
  note_created_at: string;
  is_automated: boolean;
}
