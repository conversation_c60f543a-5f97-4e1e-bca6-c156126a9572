export type AreaInfo = {
  state: string;
  zcta_code: string;
  geom:
    | {
        type: 'Polygon';
        coordinates: number[][][];
      }
    | {
        type: 'MultiPolygon';
        coordinates: number[][][][];
      };
  centroid: {
    type: string;
    coordinates: number[];
  };
  population: number;
  birth_rate: number;
  median_age: number;
  pop_over_40: number;
  median_home_value: number;
  median_household_income: number;
  extra_census_data?: { code: string; value: number; label: string }[];
};
