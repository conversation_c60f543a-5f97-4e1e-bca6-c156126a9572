import { TerritoryRequestStatus } from './territory.request.status.type';
import { TerritoryStatus } from './territory.status.type';

export type Territory = {
  id: number;
  zor_id: string;
  state: string;
  zcta_codes: string[];
  geom:
    | {
        type: 'Polygon';
        coordinates: number[][][];
      }
    | {
        type: 'MultiPolygon';
        coordinates: number[][][][];
      };
  centroid: {
    type: string;
    coordinates: number[];
  };
  population: number;
  birth_rate: number;
  median_age: number;
  median_home_value: number;
  median_household_income: number;
  pop_over_40: number;
  status: TerritoryStatus;
  assigned_to: {
    id: string;
    full_name: string;
    photo_url: string;
  } | null;
  likes_count: number;
  is_liked: boolean;
  liked_on: string;
  request_status: TerritoryRequestStatus;
  extra_census_data?: { code: string; value: number; label: string }[];
  state_preference_count?: number;
  state_preference_photos?: string[];
  territory_like_photos?: string[];
  requested_by?: {
    id: string;
    full_name: string;
    photo_url: string;
  } | null;
  is_deleted: boolean;
  territory_likers: { id: string; full_name: string; photo_url: string }[];
};
