export type TerritorySettings = {
  zor_id?: string | null;
  min_population_size?: number | null;
  min_median_household_income?: number | null;
  is_heatmap_active?: boolean | null;
  population?: { is_heatmap_active: boolean; heatmap_weight: number } | null;
  birth_rate?: { is_heatmap_active: boolean; heatmap_weight: number } | null;
  median_age?: { is_heatmap_active: boolean; heatmap_weight: number } | null;
  pop_over_40?: { is_heatmap_active: boolean; heatmap_weight: number } | null;
  median_home_value?: {
    is_heatmap_active: boolean;
    heatmap_weight: number;
  } | null;
  median_household_income?: {
    is_heatmap_active: boolean;
    heatmap_weight: number;
  } | null;
  colors?: {
    sold: string;
    liked: string;
    reserved: string;
    available: string;
    unavailable: string;
    pending: string;
  } | null;
  iframe_links?: object | null;
  extra_acs_variables?: Array<{
    id: string;
    acs_code: string;
    label: string;
    heatmap_weight: number;
    is_heatmap_active: boolean;
  }> | null;
};
