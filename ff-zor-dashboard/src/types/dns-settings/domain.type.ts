export type Domain = {
  id: string;
  zor_id: string;
  domain: string;
  is_verified: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  meta: Record<string, any>;
  type:
    | 'root'
    | 'portal-consultant'
    | 'portal-franchisee'
    | 'mail-root'
    | 'mail-dkim'
    | 'mail-dkim2';
};

export type DomainVerificationRecord = {
  type: string;
  domain: string;
  value: string;
  reason: string;
};
