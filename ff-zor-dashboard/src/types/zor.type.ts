export type Zor = {
  id: string;
  // Company Details
  name: string;
  slug: string;
  logo_url: string;
  headquarter_location: string;
  franchise_since: number;
  industry: string;
  number_of_units: number;
  website: string;
  fdd_renewal_date: string;
};

export type ZorFinancial = {
  id: string;
  // Financials
  startup_cost_min: number;
  startup_cost_max: number;
  min_franchisee_net_worth: number;
  franchise_fee: number;
  veteran_franchise_fee: number;
  is_sba_eligible: boolean;
  is_veteran_discount: boolean;
};

export type ZorBillingDetails = {
  id: string;
  // Billing Details
  legal_business_name: string;
  email: string;
  phone: string;
  postal_code: string;
};

export type ZorPaymentDetails = {
  id: number;
  // Payment Details
  zor_id: string;
  card_number: string;
  security_code: string;
  expiration_date: string;
};
