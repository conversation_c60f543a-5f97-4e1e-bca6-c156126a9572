export interface OptionInterface {
  value: string;
  label: string;
  color?: string;
  description?: string;
}

export const tabOptions: OptionInterface[] = [
  { value: 'discovery', label: 'Discovery' },
  { value: 'territories', label: 'Territories' },
  { value: 'profile?tab=general', label: 'View Profile' },
  {
    value: 'financial-overview',
    label: 'Financial Overview',
  },
  { value: 'library', label: 'Library' },
  { value: 'ask-aimei', label: 'Ask Aimei' },
];

export const linkOptions: OptionInterface[] = [
  { value: 'document-sign', label: 'Document Sign', color: '#05E500' },
  { value: 'video-link', label: 'Video Link', color: '#E92215' },
  { value: 'meeting-link', label: 'Meeting Link', color: '#FF9838' },
  { value: 'view-document', label: 'View Document', color: '#279DD4' },
  { value: 'upload', label: 'Upload', color: '#9631F5' },
  { value: 'ask-aimei', label: '<PERSON> Aimei', color: '#E9538D' },
  {
    value: 'link-to-tab',
    label: 'Link to platform tab',
    color: '#1F7EAA',
  },
  {
    value: 'external-web-link',
    label: 'External Web Link',
    color: '#2E7D32',
  },
];

export const UserTypeOptions: OptionInterface[] = [
  { value: 'leadership', label: 'Leadership' },
  { value: 'partner', label: 'Partner' },
];

export const UserPermissionOptions: OptionInterface[] = [
  { value: 'owner', label: 'Owner' },
  { value: 'administrator', label: 'Administrator' },
  { value: 'consultant', label: 'Consultant' },
];

export const PreferredContactOptions: OptionInterface[] = [
  { value: 'email', label: 'Email' },
  { value: 'meeting_link', label: 'Meeting Link' },
  { value: 'phone', label: 'Phone' },
];

export const GenderOptions: OptionInterface[] = [
  { value: 'male', label: 'Male' },
  { value: 'femail', label: 'Female' },
];

export const IndustryOptions: OptionInterface[] = [
  {
    value: 'quick_service_restaurants',
    label: 'Quick-Service Restaurants',
    description: '(QSR)',
  },
  {
    value: 'health_and_wellness',
    label: 'Health & Wellness',
    description: '(e.g., fitness studios, spas)',
  },
  {
    value: 'home_services',
    label: 'Home Services',
    description: '(e.g., cleaning, landscaping)',
  },
  {
    value: 'medical',
    label: 'Medical',
    description: ' (e.g, health clinics, natal care, joint care)',
  },
  {
    value: 'childcare_and_education',
    label: 'Childcare & Education',
    description: '(e.g., tutoring centers, daycare)',
  },
  {
    value: 'senior_care_services',
    label: 'Senior Care Services',
  },
  {
    value: 'retail',
    label: 'Retail',
    description: '(e.g., specialty shops, convenience stores)',
  },
  {
    value: 'automotive_services',
    label: 'Automotive Services ',
    description: '(e.g., car repair, oil change)',
  },
  {
    value: 'food_and_beverage',
    label: 'Food & Beverage',
    description: '(e.g., smoothie bars, bakeries)',
  },
  {
    value: 'logistics_and_delivery_services',
    label: 'Logistics & Delivery Services',
  },
  { value: 'other', label: 'Other' },
];

export const MinimumPopulationSizeOptions: OptionInterface[] = [
  { value: '0', label: '0' },
  { value: '10000', label: '10,000' },
  { value: '20000', label: '20,000' },
  { value: '30000', label: '30,000' },
  { value: '40000', label: '40,000' },
  { value: '50000', label: '50,000' },
];

export const MinimumAverageHouseholdIncomeOptions: OptionInterface[] = [
  { value: '0', label: '0' },
  { value: '45000', label: '45,000' },
  { value: '60000', label: '60,000' },
  { value: '75000', label: '75,000' },
  { value: '90000', label: '90,000' },
  { value: '105000', label: '105,000' },
  { value: '120000', label: '120,000' },
];

export const currentWorkingBrandsOptions = [
  { label: '1-10 brands', value: '1-10_brands' },
  { label: '10-25 brands', value: '10-25_brands' },
  { label: '25-50 brands', value: '25-50_brands' },
  { label: '50+ brands', value: '50_plus_brands' },
];

export const yesNoOptions = [
  { label: 'Yes', value: 'yes' },
  { label: 'No', value: 'no' },
];

export const sbaEligibilityOptions = [
  { label: 'Yes', value: 'yes' },
  { label: 'No', value: 'no' },
  { label: 'Not Sure', value: 'not_sure' },
];

export const creditScoreOptions = [
  { label: 'Under 680', value: 'under_680' },
  { label: '681-750', value: '681_750' },
  { label: '751-799', value: '751_799' },
  { label: '800+', value: '800_plus' },
  { label: 'Not Sure', value: 'not_sure' },
];

export const ownerTypeOptions = [
  { label: 'Owner-Operator', value: 'owner_operator' },
  { label: 'Investor', value: 'investor' },
  { label: 'Multi-Unit', value: 'multi_unit' },
];
