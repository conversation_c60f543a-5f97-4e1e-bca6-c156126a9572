type CensusVariable = {
  code: string;
  weightBy: string;
  group: string;
  label: string;
  description: string;
  per?: number;
  unit?: string;
};

export const CensusVariables: Record<string, CensusVariable> = {
  medianAge: {
    code: 'B01002_001E',
    weightBy: 'B01001_001E',
    group: 'B01002',
    label: 'Median Age',
    description: 'Median age ',
  },
  medianAgeMale: {
    code: 'B01002_002E',
    weightBy: 'B01001_002E',
    group: 'B01002',
    label: 'Median Age (Male)',
    description: 'Median age of male population',
  },
  medianAgeFemale: {
    code: 'B01002_003E',
    weightBy: 'B01001_026E',
    group: 'B01002',
    label: 'Median Age (Female)',
    description: 'Median age of female population',
  },
  medianHomeValue: {
    code: 'B25077_001E',
    weightBy: 'B25001_001E',
    group: 'B25077',
    label: 'Median home value',
    description: 'Median home value',
  },
  birthRate: {
    code: 'B01001A_003E', // Male under 5 as a proxy
    weightBy: 'B03002_003E', // Total population
    group: 'B01001',
    label: 'Birth Rate (approx.)',
    description: 'Estimated birth rate based on male under 5 population',
    per: 1000, // <- This is your scaling factor
    unit: 'per 1,000 people',
  },
};
