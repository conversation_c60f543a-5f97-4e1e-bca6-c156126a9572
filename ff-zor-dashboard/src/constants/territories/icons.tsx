const generateMarkerSvg = (color: string = '#22C55E') => {
  const svg = `<svg width="30" height="37" viewBox="0 0 30 37" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.3934 4.3934C10.2513 -1.46447 19.7487 -1.46447 25.6066 4.3934C31.4645 10.2513 31.4645 19.7487 25.6066 25.6066L15 36.2132L4.3934 25.6066C-1.46447 19.7487 -1.46447 10.2513 4.3934 4.3934Z" fill="#3CAAFA"/>
    <rect x="4" y="4" width="22" height="22" rx="11" fill="#F4F4F5"/>
    <rect x="9" y="9" width="12" height="12" rx="6" fill="${color}" opacity="0.2" />
    <rect x="11" y="11" width="8" height="8" rx="4" fill="${color}"/>
    </svg>`;
  return `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svg)}`;
};

export default generateMarkerSvg;
