import { TerritoryStatus } from '@/types/territories/territory.status.type';

export const getNextStatusOptions = (
  current: TerritoryStatus
): TerritoryStatus[] => {
  const statusMap: Record<TerritoryStatus, TerritoryStatus[]> = {
    RESERVED: ['SOLD'],
    SOLD: [],
    AVAILABLE: [],
    UNAVAILABLE: [],
    LIKED: [],
    PENDING: ['RESERVED', 'SOLD'],
  };

  return statusMap[current] ?? [];
};
