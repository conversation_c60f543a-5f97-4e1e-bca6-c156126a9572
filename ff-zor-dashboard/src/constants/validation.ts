// Form validation constants
export const VALIDATION_LIMITS = {
  // Character limits for text fields
  SHORT_TEXT: 100,
  MEDIUM_TEXT: 200,
  LONG_TEXT: 500,
  DESCRIPTION: 100000,
  URL: 255,

  // Financial limits
  MAX_FINANCIAL_AMOUNT: 10000000,
  MAX_INVESTMENT_AMOUNT: 1000000,

  // Date limits
  MIN_YEAR: 1900,
  MAX_YEAR: new Date().getFullYear(),

  // Brand score limits
  MIN_SCORE: 1,
  MAX_SCORE: 100,
  SCORE_STEP: 1,

  // Calendar/scheduling
  MAX_DAYS: 365,
} as const;

// Common validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  EMAIL_INVALID: 'Please enter a valid email address',
  EMAIL_REQUIRED: 'Email is required',
  URL_INVALID: 'Please enter a valid URL',
  URL_REQUIRED: 'URL is required',
  TOO_SHORT: (min: number) => `Must be at least ${min} characters`,
  TOO_LONG: (max: number) => `Must not exceed ${max} characters`,
  OUT_OF_RANGE: (min: number, max: number) =>
    `Must be between ${min} and ${max}`,
  INVALID_FORMAT: 'Invalid format',
  NETWORK_ERROR: 'Network error. Please try again later.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',

  // Field-specific required messages
  NAME_REQUIRED: 'Name is required',
  PHONE_REQUIRED: 'Phone number is required',
  LOCATION_REQUIRED: 'Location is required',
  COMPANY_NAME_REQUIRED: 'Company name is required',
  COMPANY_LOGO_REQUIRED: 'Company logo is required',
  HEADQUARTER_LOCATION_REQUIRED: 'Headquarter location is required',
  FRANCHISE_SINCE_REQUIRED: 'Franchise since year is required',
  INDUSTRY_REQUIRED: 'Industry is required',
  WEBSITE_REQUIRED: 'Website is required',
  FDD_RENEWAL_DATE_REQUIRED: 'FDD renewal date is required',

  // Field-specific validation messages
  WEBSITE_INVALID: 'Please provide a valid website URL',
  YEAR_INVALID: (minYear: number, maxYear: number) =>
    `Please provide a valid year between ${minYear} and ${maxYear}`,
  COMPANY_NAME_TOO_LONG: `Company name must be less than ${VALIDATION_LIMITS.SHORT_TEXT} characters`,
  HEADQUARTER_LOCATION_TOO_LONG: `Headquarter location must be less than ${VALIDATION_LIMITS.MEDIUM_TEXT} characters`,
  UNITS_MIN: 'Number of franchise units must be at least 1',
  UNITS_MAX: 'Number of franchise units must be less than 100,000',
  DATE_INVALID: 'Please provide a valid date',

  // Financial form messages
  STARTUP_COST_REQUIRED: 'Baseline Franchisee Start-up Cost is required',
  STARTUP_COST_MAX: `Start-up cost must be less than $${VALIDATION_LIMITS.MAX_FINANCIAL_AMOUNT.toLocaleString()}`,
  STARTUP_COST_RANGE_INVALID:
    'Maximum startup cost must be greater than or equal to minimum startup cost',
  MIN_NETWORTH_REQUIRED: 'Minimum Franchisee Networth is required',
  MIN_NETWORTH_MAX: `Networth must be less than $${VALIDATION_LIMITS.MAX_FINANCIAL_AMOUNT.toLocaleString()}`,
  FRANCHISE_FEE_REQUIRED: 'Franchisee Fee is required',
  FRANCHISE_FEE_MAX: `Franchise fee must be less than $${VALIDATION_LIMITS.MAX_INVESTMENT_AMOUNT.toLocaleString()}`,
  VETERAN_FEE_REQUIRED: 'Veteran/First Responder Franchise Fee is required',
  VETERAN_FEE_MAX: `Veteran franchise fee must be less than $${VALIDATION_LIMITS.MAX_INVESTMENT_AMOUNT.toLocaleString()}`,
  SBA_ELIGIBLE_REQUIRED: 'SBA eligibility status is required',
  VETERAN_DISCOUNT_REQUIRED: 'Veteran discount status is required',

  // Auth form messages
  PASSWORD_REQUIRED: 'Password is required',
  PASSWORD_TOO_SHORT: 'Password must be at least 8 characters',
  TERMS_REQUIRED: 'You must agree to the Terms of Use and Privacy Policy',

  // General settings form messages
  COMPANY_LOGO_URL_REQUIRED: 'Company logo is required',
  LOGO_URL_INVALID: 'Please provide a valid logo URL',
  LOGO_URL_TOO_LONG: `Logo URL must be less than ${VALIDATION_LIMITS.URL} characters`,
  LOGIN_H1_REQUIRED: 'Login page greeting (H1) is required',
  LOGIN_H1_TOO_LONG: `Login page greeting (H1) must be less than ${VALIDATION_LIMITS.SHORT_TEXT} characters`,
  LOGIN_H2_REQUIRED: 'Login page greeting (H2) is required',
  LOGIN_H2_TOO_LONG: `Login page greeting (H2) must be less than ${VALIDATION_LIMITS.MEDIUM_TEXT} characters`,
  HOMEPAGE_GREETING_REQUIRED: 'Homepage greeting text is required',
  HOMEPAGE_GREETING_TOO_LONG: `Homepage greeting text must be less than ${VALIDATION_LIMITS.LONG_TEXT} characters`,
  ONBOARDING_COUNTDOWN_MIN: 'Onboarding countdown must be at least 1 day',
  ONBOARDING_COUNTDOWN_MAX: `Onboarding countdown must be less than ${VALIDATION_LIMITS.MAX_DAYS} days`,

  // Brand score form messages
  BRAND_SCORE_REQUIRED: 'Brand score is required',
  BRAND_SCORE_MIN: `Score must be at least ${VALIDATION_LIMITS.MIN_SCORE}`,
  BRAND_SCORE_MAX: `Score cannot be greater than ${VALIDATION_LIMITS.MAX_SCORE}`,

  // Profile form messages
  PHOTO_REQUIRED: 'Photo is required',
  FULL_NAME_REQUIRED: 'Full name is required',
  POSITION_REQUIRED: 'Position is required',
  ORGANIZATION_ROLE_REQUIRED: 'Organization role is required',
  CITY_REQUIRED: 'City is required',
  MEETING_LINK_REQUIRED: 'Meeting link is required',
  LINKEDIN_REQUIRED: 'LinkedIn profile is required',
  PREFERRED_CONTACT_REQUIRED: 'Preferred contact method is required',

  // Billing form messages
  BUSINESS_NAME_REQUIRED: 'Business name is required',
  POSTAL_CODE_REQUIRED: 'Postal code is required',

  // Payment form messages
  CARD_NUMBER_REQUIRED: 'Card number is required',
  EXPIRATION_DATE_REQUIRED: 'Expiration date is required',
  EXPIRATION_DATE_INVALID: 'Please enter a valid expiration date (MM/YY)',
  SECURITY_CODE_REQUIRED: 'Security code is required',
  SECURITY_CODE_INVALID: 'Security code must be exactly 3 digits',

  // Reset password form messages
  OLD_PASSWORD_REQUIRED: 'Current password is required',
  OLD_PASSWORD_TOO_SHORT: 'Current password must be at least 8 characters',
  NEW_PASSWORD_REQUIRED: 'New password is required',
  NEW_PASSWORD_TOO_SHORT: 'New password must be at least 8 characters',
  CONFIRM_PASSWORD_REQUIRED: 'Please confirm your new password',
  CONFIRM_PASSWORD_TOO_SHORT: 'Confirm password must be at least 6 characters',
  PASSWORDS_MUST_MATCH: 'New password and confirm password must match',

  // Upload form messages
  FILE_REQUIRED: 'File is required',
  TITLE_REQUIRED: 'Title is required',
  DESCRIPTION_REQUIRED: 'Description is required',
  FDD_FILE_REQUIRED: 'Please upload your FDD file',
  WORKBOOK_FILE_REQUIRED: 'Please upload your workbook file',
  WORKBOOK_FILE_TYPE_INVALID: 'Please upload an Excel file (.xlsx or .xls)',
  DOCUMENT_FILE_REQUIRED: 'Please upload your file',
  FILE_SIZE_TOO_LARGE: 'File size must be less than 25MB',
  FILE_TYPE_INVALID:
    'Only PDF, Excel (XLS, XLSX), and image files (PNG, JPG, JPEG, GIF, WebP) are allowed',

  // Leadership form messages
  PERMISSIONS_REQUIRED: 'Permission level is required',

  // Import form messages
  CSV_FILE_REQUIRED: 'Please upload a CSV file',
  CSV_FILE_TYPE_INVALID: 'Please upload a CSV file',

  // Territory form messages
  STATE_REQUIRED: 'State is required',
  TERRITORY_NAME_REQUIRED: 'Territory name is required',
  TERRITORY_TYPE_REQUIRED: 'Territory type is required',
  JOB_TITLE_REQUIRED: 'Job title is required',
  NET_WORTH_REQUIRED: 'Net worth is required',
  AGE_RANGE_START_REQUIRED: 'Age range start is required',
  AGE_RANGE_END_REQUIRED: 'Age range end is required',
  INCOME_START_REQUIRED: 'Income range start is required',
  INCOME_END_REQUIRED: 'Income range end is required',
  POPULATION_REQUIRED: 'Population is required',
  GENDER_REQUIRED: 'Gender is required',
  DATA_REQUIRED: 'Data is required',

  // Territory color messages
  SOLD_COLOR_REQUIRED: 'Sold color is required',
  LIKED_COLOR_REQUIRED: 'Liked color is required',
  RESERVED_COLOR_REQUIRED: 'Reserved color is required',
  AVAILABLE_COLOR_REQUIRED: 'Available color is required',
  UNAVAILABLE_COLOR_REQUIRED: 'Unavailable color is required',
  PENDING_COLOR_REQUIRED: 'Pending color is required',
  COLOR_FORMAT_INVALID:
    'Invalid color format. Please use hex color code (e.g. #FF0000)',

  // Settings form messages
  PROMPT_REQUIRED: 'Prompt text is required',
  PROMPT_TOO_LONG: 'Prompt text must be less than 1000 characters',
  STAGE_REQUIRED: 'Stage is required',
  AT_LEAST_ONE_PROMPT: 'At least one prompt is required',
  MAX_PROMPTS_EXCEEDED: 'Maximum 50 prompts allowed',
  TITLE_TOO_LONG: `Title must be less than ${VALIDATION_LIMITS.SHORT_TEXT} characters`,
} as const;
