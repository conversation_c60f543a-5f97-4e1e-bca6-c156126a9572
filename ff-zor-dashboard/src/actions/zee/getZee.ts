import { AxiosError } from 'axios';

import { ZEE_API_URL } from '@/constants/apis';
import apiManager from '@/helpers/api.manager';
import { ApiResponse } from '@/types/response.type';
import { Zee } from '@/types/zee.type';

const getZee = async (zeeId: string): Promise<ApiResponse<Zee>> => {
  try {
    const response = await apiManager.get(`${ZEE_API_URL}/${zeeId}`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export default getZee;
