import { AxiosError } from 'axios';

import { ZOR_API_URL } from '@/constants/apis';
import apiManager from '@/helpers/api.manager';
import { ApiResponse } from '@/types/response.type';
import { Zor, ZorFinancial } from '@/types/zor.type';

export type CreateZorApiPayload = {
  // Company Details
  name: string;
  slug: string;
  logo_url: string;
  headquarter_location: string;
  franchise_since: number;
  industry: string;
  number_of_units: number;
  website: string;
  fdd_renewal_date: Date;
  // Financials
  startup_cost_min: number;
  startup_cost_max: number;
  min_franchisee_net_worth: number;
  franchise_fee: number;
  veteran_franchise_fee: number;
  is_sba_eligible: boolean;
  is_veteran_discount: boolean;
};

export type CreateZorApiResponse = {
  zor_profile: Zor;
  zor_financial: ZorFinancial;
};

const createZor = async (
  payload: CreateZorApiPayload
): Promise<ApiResponse<CreateZorApiResponse>> => {
  try {
    const response = await apiManager.post(ZOR_API_URL, payload, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export default createZor;
