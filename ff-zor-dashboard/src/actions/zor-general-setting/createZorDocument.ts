import { AxiosError } from 'axios';

import { ZOR_GENERAL_SETTING_API_URL } from '@/constants/apis';
import apiManager from '@/helpers/api.manager';
import { ApiResponse } from '@/types/response.type';
import { ZorGeneralSetting } from '@/types/zor.general-setting.type';

export type CreateZorGeneralSettingApiPayload = {
  website_url?: string;
  company_logo_url: string;
  login_greeting_text_h1: string;
  login_greeting_text_h2: string;
  homepage_greeting_text: string;
  onboarding_countdown: number;
};

const createZorGeneralSetting = async (
  payload: CreateZorGeneralSettingApiPayload
): Promise<ApiResponse<ZorGeneralSetting>> => {
  try {
    const response = await apiManager.post(
      ZOR_GENERAL_SETTING_API_URL,
      payload,
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export default createZorGeneralSetting;
