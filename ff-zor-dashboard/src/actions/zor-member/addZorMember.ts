import { AxiosError } from 'axios';

import { ZOR_MEMBER_API_URL } from '@/constants/apis';
import apiManager from '@/helpers/api.manager';
import { ApiResponse } from '@/types/response.type';
import { ZorMember } from '@/types/zor.member.type';

export type AddZorMemberApiPayload = {
  id?: string;
  full_name: string;
  photo_url?: string;
  position: string;
  city: string;
  phone: string;
  email: string;
  meeting_link: string;
  linkedin: string;
  preferred_contact: string;
  is_public: boolean;
  is_added?: boolean;
};

const addZorMember = async (
  payload: AddZorMemberApiPayload
): Promise<ApiResponse<ZorMember>> => {
  try {
    const response = await apiManager.post(
      `${ZOR_MEMBER_API_URL}/add`,
      payload,
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export default addZorMember;
