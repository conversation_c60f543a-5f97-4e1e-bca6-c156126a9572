import { AxiosError } from 'axios';

import { ZOR_MEMBER_API_URL } from '@/constants/apis';
import apiManager from '@/helpers/api.manager';
import { ApiResponse } from '@/types/response.type';
import { ZorMember } from '@/types/zor.member.type';

export type CreateZorMemberApiPayload = {
  full_name: string;
  position: string;
  email: string;
  // optional fields
  photo_url?: string;
  city?: string;
  phone?: string;
  meeting_link?: string;
  linkedin?: string;
  preferred_contact?: string;
};

const createZorMember = async (
  payload: CreateZorMemberApiPayload
): Promise<ApiResponse<ZorMember>> => {
  try {
    const response = await apiManager.post(ZOR_MEMBER_API_URL, payload, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export default createZorMember;
