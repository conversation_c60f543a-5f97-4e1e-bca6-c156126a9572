import { AxiosError } from 'axios';

import {
  ORG_INVITE_API_URL,
  REVOKE_ORG_INVITATION_API_URL,
} from '@/constants/apis';
import { configuration } from '@/constants/config';
import apiManager from '@/helpers/api.manager';
import { ApiResponse } from '@/types/response.type';
import { User } from '@/types/user.type';

export type SendOrgInvitationApiPayload = {
  role: string;
  name?: string;
  email: string;
  orgId: string;
  inviterId: string;
  preferredMarkets?: string[];
};

const sendOrgInvitation = async (
  payload: SendOrgInvitationApiPayload
): Promise<ApiResponse<User>> => {
  try {
    const response = await apiManager.post(ORG_INVITE_API_URL, payload, {
      baseURL: configuration.CLERK_API_URL,
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export default sendOrgInvitation;

export type RevokeOrgInvitationApiPayload = {
  invitationId: string;
  orgId: string;
  requestingUserId: string;
};

const revokeOrgInvitation = async (
  payload: RevokeOrgInvitationApiPayload
): Promise<ApiResponse<User>> => {
  try {
    const response = await apiManager.post(
      REVOKE_ORG_INVITATION_API_URL,
      payload,
      {
        baseURL: configuration.CLERK_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status !== 200) {
      const error = response as unknown as AxiosError;
      return Promise.reject(error.response?.data);
    }

    return Promise.resolve(response.data);
  } catch (e) {
    return Promise.reject(e);
  }
};

export { revokeOrgInvitation };
