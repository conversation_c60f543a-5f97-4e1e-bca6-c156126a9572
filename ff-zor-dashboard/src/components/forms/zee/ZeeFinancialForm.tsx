import { forwardRef, useImperativeHandle } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { ZeeFinancialFormSchema } from '@/components/forms/schemas/ZeeFinancialFormSchema';
import { FormCheckboxGroup } from '@/components/global/form-inputs/FormCheckboxGroup';
import FormInput from '@/components/global/form-inputs/FormInput';
import { FormRadioGroupString } from '@/components/global/form-inputs/FormRadioGroupString';
import { Form } from '@/components/ui/form';
import {
  creditScoreOptions,
  ownerTypeOptions,
  sbaEligibilityOptions,
  yesNoOptions,
} from '@/constants/options';

interface ZeeFinancialFormProps {
  form: UseFormReturn<z.infer<typeof ZeeFinancialFormSchema>>;
}

export const ZeeFinancialForm = forwardRef(
  ({ form }: ZeeFinancialFormProps, ref) => {
    useImperativeHandle(ref, () => ({
      submitForm: () => {
        form.handleSubmit(async () => {})();
      },
    }));

    const showInvestorDescription =
      form.watch('plan_to_have_investor_or_partner') === 'yes';

    return (
      <Form {...form}>
        <form className="w-full space-y-1.5">
          <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
            <p className="as-title-03 as-strong w-[280px] text-zinc-700">
              Financial
            </p>
            <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1">
              <FormInput
                control={form.control}
                name="employment"
                type="text"
                label="Employment"
              />
              <FormInput
                preIcon
                control={form.control}
                name="annual_compensation"
                type="number"
                label="Annual Compensation"
              />
              <FormInput
                control={form.control}
                name="spouse_significant_other_name"
                type="text"
                label="Spouse/Significant Other Name"
              />
              <FormInput
                control={form.control}
                name="spouse_significant_other_employment"
                type="text"
                label="Spouse/Significant Other Employment"
              />
              <FormInput
                preIcon
                control={form.control}
                name="spouse_significant_other_annual_compensation"
                type="number"
                label="Spouse/Significant Other Annual Compensation"
              />
              <FormInput
                control={form.control}
                name="spouse_significant_other_phone"
                label="Spouse/Significant Other Phone"
              />
              <FormRadioGroupString
                control={form.control}
                name="is_veteran"
                label="Are you or your spouse a veteran?"
                options={yesNoOptions}
              />
              <FormRadioGroupString
                control={form.control}
                name="is_first_responder_and_medical_professional"
                label="Are you or your spouse a First Responder & Medical Professional?"
                options={yesNoOptions}
              />
              <FormRadioGroupString
                control={form.control}
                name="credit_score_range"
                label="Credit Score Range"
                options={creditScoreOptions}
                className="sm:col-span-2"
              />
              <FormRadioGroupString
                control={form.control}
                name="is_sba_eligible"
                label="SBA Eligibility"
                options={sbaEligibilityOptions}
                className="sm:col-span-2"
              />
              <FormCheckboxGroup
                control={form.control}
                name="owner_type"
                label="Owner Type (Select all that apply)"
                options={ownerTypeOptions}
                className="sm:col-span-2"
              />
              <FormRadioGroupString
                control={form.control}
                name="plan_to_have_investor_or_partner"
                label="Plan to have investors or partners?"
                options={yesNoOptions}
                className="sm:col-span-2"
              />
              {showInvestorDescription && (
                <FormInput
                  control={form.control}
                  name="investor_partner_description"
                  type="text"
                  label="Please describe your investors or partners"
                  placeholder="Enter details about your investors or partners"
                  className="sm:col-span-2"
                />
              )}
            </div>
          </div>
        </form>
      </Form>
    );
  }
);
