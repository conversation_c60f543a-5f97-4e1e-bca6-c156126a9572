import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { IndustryOptions } from '@/constants/options';

interface IndustryBackgroundFormProps {
  selectedIndustries: string[];
  setSelectedIndustries: (industries: string[]) => void;
}

export const IndustryBackgroundForm = ({
  selectedIndustries,
  setSelectedIndustries,
}: IndustryBackgroundFormProps) => {
  const handleIndustryChange = (value: string, checked: boolean) => {
    setSelectedIndustries(
      checked
        ? [...selectedIndustries, value]
        : selectedIndustries.filter((industry) => industry !== value)
    );
  };

  return (
    <div className="w-full space-y-1.5">
      <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
        <p className="as-title-03 as-strong w-[280px] text-zinc-700">
          Industry Background
        </p>
        <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1">
          {IndustryOptions.map(({ value, label, description }) => (
            <div key={value} className="flex items-center space-x-2">
              <Checkbox
                id={value}
                checked={selectedIndustries.includes(value)}
                onCheckedChange={(checked) =>
                  handleIndustryChange(value, checked as boolean)
                }
              />
              <Label htmlFor={value} className="text-sm font-normal">
                {label}
                {description && (
                  <span className="text-gray-500">{description}</span>
                )}
              </Label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
