import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { IndustryOptions } from '@/constants/options';
import { cn } from '@/lib/ui/utils';

type IndustryInterests = Record<string, number>;

interface IndustryInterestFormProps {
  industryInterests: IndustryInterests;
  setIndustryInterests: (industries: IndustryInterests) => void;
}

export const IndustryInterestForm = ({
  industryInterests,
  setIndustryInterests,
}: IndustryInterestFormProps) => {
  const handleIndustryChange = (value: string, newValue: number[]) => {
    setIndustryInterests({
      ...industryInterests,
      [value]: newValue[0],
    });
  };

  const renderIndustryOption = ({
    value,
    label,
    description,
  }: (typeof IndustryOptions)[number]) => {
    const currentValue = industryInterests[value] || 0;

    return (
      <div key={value} className={cn('flex items-center justify-between')}>
        <Label htmlFor={value} className="text-sm font-normal">
          {label}
          {description && <span className="text-gray-500">{description}</span>}
        </Label>
        <div className="flex items-center gap-4">
          <Slider
            id={value}
            min={0}
            max={5}
            step={1}
            value={[currentValue]}
            onValueChange={(newValue) => handleIndustryChange(value, newValue)}
            className="w-[200px] rounded-full bg-muted"
          />
          <span className="text-sm font-medium text-gray-600">
            {currentValue}/5
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full space-y-1.5">
      <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
        <p className="as-title-03 as-strong w-[280px] text-zinc-700">
          Industry Interest
        </p>
        <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1">
          {IndustryOptions.map(renderIndustryOption)}
        </div>
      </div>
    </div>
  );
};
