import { forwardRef, useImperativeHandle } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { PersonalBackgroundFormSchema } from '@/components/forms/schemas/zee/PersonalBackgroundFormSchema';
import { FormRadioGroupString } from '@/components/global/form-inputs/FormRadioGroupString';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Form } from '@/components/ui/form';
import { yesNoOptions } from '@/constants/options';

interface PersonalBackgroundFormProps {
  form: UseFormReturn<z.infer<typeof PersonalBackgroundFormSchema>>;
}

export const PersonalBackgroundForm = forwardRef<
  { submitForm: () => void },
  PersonalBackgroundFormProps
>(({ form }, ref) => {
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      form.handleSubmit(() => Promise.resolve())();
    },
  }));

  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Personal Background
          </p>
          <div className="w-full">
            <div className="grid flex-1 grid-cols-2 gap-6">
              <FormRadioGroupString
                control={form.control}
                name="has_bankruptcy"
                label="Have you ever been involved in a personal or business bankruptcy?"
                options={yesNoOptions}
                className="col-span-2"
              />
              <FormRadioGroupString
                control={form.control}
                name="has_criminal_conviction"
                label="Have you or your spouse ever been convicted of a crime?"
                options={yesNoOptions}
                className="col-span-2"
              />
              <FormRadioGroupString
                control={form.control}
                name="has_pending_lawsuits"
                label="Are you or your spouse currently involved in any pending lawsuits?"
                options={yesNoOptions}
                className="col-span-2"
              />
              <FormRadioGroupString
                control={form.control}
                name="has_civil_judgments"
                label="Have you or your spouse ever been subject to a civil judgment?"
                options={yesNoOptions}
                className="col-span-2"
              />
              <FormTextArea
                control={form.control}
                name="affirmative_answers_explanation"
                type="textarea"
                label="Please explain any affirmative answers to the previous 5 questions."
                className="col-span-2"
              />
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
});

PersonalBackgroundForm.displayName = 'PersonalBackgroundForm';
