import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import ZeeProfileFormSchema from '@/components/forms/schemas/ZeeProfileFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import { FormMultiSelect } from '@/components/global/form-inputs/FormMultiSelect';
import { Form } from '@/components/ui/form';
import { US_STATES } from '@/constants/territories/states';

interface ZeeProfileFormProps {
  form: UseFormReturn<z.infer<typeof ZeeProfileFormSchema>>;
}

export const ZeeProfileForm = ({ form }: ZeeProfileFormProps) => {
  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
          <p className="as-title-02 as-strong w-[280px] font-medium text-zinc-700">
            Contact Details
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <FormInput
              name="full_name"
              label="Full Name"
              control={form.control}
            />
            <FormInput
              name="birth_date"
              label="DOB"
              type="date"
              control={form.control}
            />
            <FormInput name="city" label="City" control={form.control} />
            <FormInput name="state" label="State" control={form.control} />
            <FormInput name="zip_code" label="Zip" control={form.control} />
            <FormInput name="address" label="Address" control={form.control} />
            <FormInput
              name="phone"
              label="Phone"
              type="phone"
              control={form.control}
            />
            <FormInput name="email" label="Email" control={form.control} />
            <FormInput
              name="meeting_link"
              label="Meeting Link"
              control={form.control}
            />
            <FormInput
              name="linkedin"
              label="LinkedIn"
              control={form.control}
            />
          </div>
        </div>

        <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
          <p className="as-title-02 as-strong w-[280px] font-medium text-zinc-700">
            Preferred Markets
          </p>
          <FormMultiSelect
            name="preferred_markets"
            control={form.control}
            options={Object.entries(US_STATES).map(([key, value]) => ({
              label: value,
              value: key,
            }))}
            maxSelections={5}
            placeholder="Select up to 5 states"
          />
        </div>
      </form>
    </Form>
  );
};
