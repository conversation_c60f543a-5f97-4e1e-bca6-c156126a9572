import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';
import { z } from 'zod';

import { ZeeOtherFinancialFormSchema } from '@/components/forms/schemas/ZeeOtherFinancialFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Form } from '@/components/ui/form';

interface ZeeOtherFinancialFormProps {
  form: UseFormReturn<z.infer<typeof ZeeOtherFinancialFormSchema>>;
}

export const ZeeOtherFinancialForm = forwardRef(
  ({ form }: ZeeOtherFinancialFormProps, ref) => {
    const watchedAssets = useWatch({
      control: form.control,
      name: [
        'cash_or_equivalent_assets',
        'savings_or_certificates',
        'stocks_or_bonds_or_securities',
        'retirement_plan_or_ira',
        'home_market_value',
        'other_real_estate',
        'autos',
        'other_assets',
      ],
    });

    const watchedLiabilities = useWatch({
      control: form.control,
      name: [
        'home_mortgage_balance',
        'other_real_estate_debt',
        'auto_loans',
        'other_debt',
      ],
    });

    const totalAssets = watchedAssets.reduce(
      (total: number, value: number | undefined) => total + (value || 0),
      0
    );

    const totalLiabilities = watchedLiabilities.reduce(
      (total: number, value: number | undefined) => total + (value || 0),
      0
    );

    const netWorth = totalAssets - totalLiabilities;

    useEffect(() => {
      form.setValue('total_assets', totalAssets);
      form.setValue('total_liabilities', totalLiabilities);
      form.setValue('net_worth', netWorth);
    }, [form, totalAssets, totalLiabilities, netWorth]);

    useImperativeHandle(ref, () => ({
      submitForm: () => {
        form.handleSubmit(async () => {})();
      },
    }));

    return (
      <Form {...form}>
        <form className="w-full space-y-1.5">
          <div className="flex flex-col gap-6 rounded-xl border border-border bg-white p-6">
            <p className="as-title-03 as-strong w-[280px] text-zinc-700">
              Other Financial
            </p>
            <div className="grid flex-1 grid-cols-2 gap-6 max-sm:grid-cols-1">
              <FormInput
                preIcon
                control={form.control}
                name="cash_or_equivalent_assets"
                type="number"
                label="Cash or Equivalent Assets"
              />
              <FormInput
                preIcon
                control={form.control}
                name="savings_or_certificates"
                type="number"
                label="Savings or Certificates"
              />
              <FormInput
                preIcon
                control={form.control}
                name="stocks_or_bonds_or_securities"
                type="number"
                label="Stocks, Bonds, or Securities"
              />
              <FormInput
                preIcon
                control={form.control}
                name="retirement_plan_or_ira"
                type="number"
                label="Retirement Plan or IRA"
              />
              <FormInput
                preIcon
                control={form.control}
                name="home_market_value"
                type="number"
                label="Home Market Value"
              />
              <FormInput
                preIcon
                control={form.control}
                name="other_real_estate"
                type="number"
                label="Other Real Estate"
              />
              <FormInput
                preIcon
                control={form.control}
                name="autos"
                type="number"
                label="Autos"
              />
              <FormInput
                preIcon
                control={form.control}
                name="other_assets"
                type="number"
                label="Other Assets"
              />
              <FormTextArea
                control={form.control}
                name="other_assets_description"
                label="Other Assets Description"
                className="col-span-2"
              />
              <div className="col-span-2">
                <label className="mb-1 block text-sm font-medium text-typo-gray-quaternary">
                  Total Assets
                </label>
                <div
                  className={`cursor-pointer border-none text-typo-gray-secondary ${
                    totalAssets > 0 ? 'font-semibold' : ''
                  }`}
                >
                  {totalAssets > 0 ? `$${totalAssets.toLocaleString()}` : '-'}
                </div>
              </div>
              <FormInput
                preIcon
                control={form.control}
                name="home_mortgage_balance"
                type="number"
                label="Home Mortgage Balance"
              />
              <FormInput
                preIcon
                control={form.control}
                name="other_real_estate_debt"
                type="number"
                label="Other Real Estate Debt"
              />
              <FormInput
                preIcon
                control={form.control}
                name="auto_loans"
                type="number"
                label="Auto Loans"
              />
              <FormInput
                preIcon
                control={form.control}
                name="other_debt"
                type="number"
                label="Other Debt"
              />
              <FormTextArea
                control={form.control}
                name="other_debt_description"
                label="Other Debt Description"
                className="col-span-2"
              />
              <div className="col-span-2">
                <label className="mb-1 block text-sm font-medium text-typo-gray-quaternary">
                  Total Liabilities
                </label>
                <div
                  className={`cursor-pointer border-none text-typo-gray-secondary ${
                    totalLiabilities > 0 ? 'font-semibold' : ''
                  }`}
                >
                  {totalLiabilities > 0
                    ? `$${totalLiabilities.toLocaleString()}`
                    : '-'}
                </div>
              </div>
              <div className="col-span-2">
                <label className="mb-1 block text-sm font-medium text-typo-gray-quaternary">
                  Net Worth
                </label>
                <div
                  className={`cursor-pointer border-none text-typo-gray-secondary ${
                    netWorth !== 0 ? 'font-semibold' : ''
                  }`}
                >
                  {netWorth !== 0 ? `$${netWorth.toLocaleString()}` : '-'}
                </div>
              </div>
            </div>
          </div>
        </form>
      </Form>
    );
  }
);
