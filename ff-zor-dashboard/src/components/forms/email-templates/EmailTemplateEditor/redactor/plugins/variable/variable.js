/*jshint esversion: 6 */
Redactor.add('plugin', 'variable', {
  translations: {
    en: {
      variable: {
        title: 'Variable',
      },
    },
  },
  defaults: {
    context: false,
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.29289 6.29289C5.48043 6.10536 5.73478 6 6 6C6.55228 6 7 5.55228 7 5C7 4.44772 6.55228 4 6 4C5.20435 4 4.44129 4.31607 3.87868 4.87868C3.31607 5.44129 3 6.20435 3 7V10.5858L2.29289 11.2929C1.90237 11.6834 1.90237 12.3166 2.29289 12.7071L3 13.4142V17C3 17.7956 3.31607 18.5587 3.87868 19.1213C4.44129 19.6839 5.20435 20 6 20C6.55228 20 7 19.5523 7 19C7 18.4477 6.55228 18 6 18C5.73478 18 5.48043 17.8946 5.29289 17.7071C5.10536 17.5196 5 17.2652 5 17V13C5 12.7348 4.89464 12.4804 4.70711 12.2929L4.41421 12L4.70711 11.7071C4.89464 11.5196 5 11.2652 5 11V7C5 6.73478 5.10536 6.48043 5.29289 6.29289ZM18 4C17.4477 4 17 4.44772 17 5C17 5.55228 17.4477 6 18 6C18.2652 6 18.5196 6.10536 18.7071 6.29289C18.8946 6.48043 19 6.73478 19 7V11C19 11.2652 19.1054 11.5196 19.2929 11.7071L19.5858 12L19.2929 12.2929C19.1054 12.4804 19 12.7348 19 13V17C19 17.2652 18.8946 17.5196 18.7071 17.7071C18.5196 17.8946 18.2652 18 18 18C17.4477 18 17 18.4477 17 19C17 19.5523 17.4477 20 18 20C18.7957 20 19.5587 19.6839 20.1213 19.1213C20.6839 18.5587 21 17.7957 21 17V13.4142L21.7071 12.7071C22.0976 12.3166 22.0976 11.6834 21.7071 11.2929L21 10.5858V7C21 6.20435 20.6839 5.44129 20.1213 4.87868C19.5587 4.31607 18.7957 4 18 4ZM9 8C8.44772 8 8 8.44772 8 9C8 9.55228 8.44772 10 9 10H9.97437C9.99895 10.0392 10.0374 10.108 10.0885 10.2227C10.1924 10.4561 10.3015 10.7653 10.4614 11.2183L10.47 11.2426C10.5841 11.5659 10.7173 11.9416 10.8802 12.3689C10.455 12.9819 9.97893 13.6069 9.48039 14.1054C8.82903 14.7568 8.32834 15 8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17C9.17166 17 10.171 16.2432 10.8946 15.5196C11.1812 15.233 11.4549 14.9205 11.7118 14.6015C11.8394 14.9672 11.9615 15.3113 12.0879 15.5963C12.2301 15.9169 12.4288 16.287 12.7594 16.5676C13.1308 16.8827 13.5627 17 14 17H15C15.5523 17 16 16.5523 16 16C16 15.4477 15.5523 15 15 15H14.0255C14.0017 14.9616 13.9648 14.8951 13.9161 14.7854C13.8132 14.5535 13.7057 14.2451 13.5471 13.7904L13.5471 13.7904L13.5447 13.7836C13.4291 13.4521 13.2934 13.0645 13.1258 12.6225C13.5495 12.0122 14.0234 11.3908 14.5196 10.8946C15.171 10.2432 15.6717 10 16 10C16.5523 10 17 9.55228 17 9C17 8.44772 16.5523 8 16 8C14.8283 8 13.829 8.75675 13.1054 9.48039C12.8201 9.76573 12.5475 10.0768 12.2915 10.3943C12.1631 10.0307 12.0409 9.69068 11.9155 9.40906C11.772 9.08671 11.5725 8.71546 11.2422 8.4343C10.8703 8.11763 10.4378 8 10 8H9Z"/></svg>',
    items: ['Name', 'Lastname', 'Email'],
    start: '[%',
    end: '%]',
  },
  start() {
    if (!this.opts.is('variable.items')) return;

    let button = {
      title: this.lang.get('variable.title'),
      icon: this.defaults.icon,
      command: 'variable.popup',
    };

    this.app.toolbar.add('variable', button);
    this.app.addbar.add('variable', button);

    if (this.opts.is('variable.context')) {
      this.app.context.add('variable', button);
    }
  },
  popup(e, button) {
    let buttons = [...this.opts.get('variable.items')],
      buttonsObj = {};

    for (let i = 0; i < buttons.length; i++) {
      buttonsObj[buttons[i]] = {
        title: buttons[i],
        command: 'variable.add',
      };
    }

    this.app.dropdown.create('variable', { items: buttonsObj });
    this.app.dropdown.open(e, button);
  },
  add(e, button, name) {
    this.app.dropdown.close();

    const insertion = this.app.create('insertion');
    const variable =
      this.opts.get('variable.start') + name + this.opts.get('variable.end');

    insertion.insert({ html: variable, caret: 'after' });
  },
});
