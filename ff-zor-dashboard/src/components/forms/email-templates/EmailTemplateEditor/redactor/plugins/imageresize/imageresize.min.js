Redactor.add('plugin', 'imageresize', {
  translations: { en: { imageresize: { 'image-resize': 'Image resize' } } },
  defaults: { minHeight: 10, minWidth: 80 },
  subscribe: {
    'editor.blur, editor.select': function () {
      this.stop();
    },
    'image.position, image.outset, image.wrap, fullscreen.open, fullscreen.close':
      function () {
        this._setResizerPosition();
      },
    'image.change': function () {
      setTimeout(() => {
        this._setResizerPosition();
      }, 100);
    },
    'block.set': function (i) {
      this._load();
    },
    'ui.close': function () {
      this._hide();
    },
  },
  stop() {
    (this._remove(), this._stopEvents());
  },
  updatePosition() {
    this._setResizerPosition();
  },
  _load() {
    var i = this.app.block.get();
    (this._remove(), i && i.isType('image') && this._build(i));
  },
  _build(i) {
    ((this.$block = i.getBlock()),
      (this.$image = i.getImage()),
      (this.resizerWidth = 10),
      (this.resizerHeight = 20),
      0 === this.$block.find('#rx-image-resizer').length &&
        ((i = this.$image.position()),
        (this.$resizer = this.dom('<span>')),
        this.$resizer.attr('id', 'rx-image-resizer'),
        this.$resizer.css({
          position: 'absolute',
          top: this.$image.height() / 2 - this.resizerHeight / 2 + 'px',
          left: i.left + (this.$image.width() - this.resizerWidth / 2) + 'px',
          'min-width': '10px',
          'min-height': '20px',
          background: '#046BFB',
          border: '2px solid #fff',
          padding: '3px 4px',
          'font-size': '12px',
          'line-height': '1',
          'border-radius': '4px',
          color: '#fff',
          cursor: 'ew-resize',
        }),
        this._buildWidth(),
        this._buildEvents(),
        (this.originalWidth = this.$image.width()),
        (this.originalHeight = this.$image.height()),
        (this.originalMouseX = 0),
        (this.originalMouseY = 0),
        (this.aspectRatio = this.originalWidth / this.originalHeight),
        this.$block.append(this.$resizer),
        this.$resizer.on('mousedown touchstart', this._press.bind(this))));
  },
  _buildEvents() {
    this.app.scroll
      .getTarget()
      .on('resize.rx-image-resize', this.updatePosition.bind(this));
  },
  _buildWidth() {
    var i = this.app.create('utils').cssToObject(this.$image.attr('style'));
    (i.width || i.height) && (this._width = !0);
  },
  _press(i) {
    i.preventDefault();
    var t = i.touches ? i.touches[0] : i;
    ((this.originalWidth = this.$image.width()),
      (this.originalHeight = this.$image.height()),
      (this.originalMouseX = t.clientX),
      (this.originalMouseY = t.clientY),
      this.app.event.pause(),
      this.app.page
        .getDoc()
        .on(
          'mousemove.rx-image-resize touchmove.rx-image-resize',
          this._move.bind(this)
        ),
      this.app.page
        .getDoc()
        .on(
          'mouseup.rx-image-resize touchend.rx-image-resize',
          this._release.bind(this)
        ),
      this.app.broadcast('image.resize.start', {
        e: i,
        block: this.$block,
        image: this.$image,
      }));
  },
  _move(i) {
    var i = (i.touches ? i.touches[0] : i).clientX - this.originalMouseX,
      t = this.$block.attr('data-rx-cont-width') || this.$block.width(),
      e = 'none' !== this.$block.css('float');
    let s = this.originalWidth + i,
      h = s / this.aspectRatio;
    (s > t &&
      !e &&
      (this.$block.attr('data-rx-cont-width', t),
      (s = t),
      (h = s / this.aspectRatio)),
      s <= this.defaults.minWidth &&
        ((s = this.defaults.minWidth), (h = s / this.aspectRatio)),
      h <= this.defaults.minHeight &&
        ((h = this.defaults.minHeight), (s = h * this.aspectRatio)),
      (s = Math.floor(s)),
      (h = Math.floor(h)),
      this.$image.attr({ width: s, height: h }),
      this._width && this.$image.css({ width: s + 'px', height: h + 'px' }),
      e && this.$block.css('max-width', s + 'px'));
    i = this.$image.position();
    (this.$resizer.css({
      top: h / 2 - this.resizerHeight / 2 + 'px',
      left: i.left + (s - this.resizerWidth / 2) + 'px',
    }),
      this.$resizer.text(s + 'px'),
      this.app.control.updatePosition());
  },
  _release(i) {
    var t = this.app.create('cleaner-cache');
    (t.cacheElementStyle(this.$image),
      t.cacheElementStyle(this.$block),
      this.$block.removeAttr('data-rx-cont-width'),
      this.app.page.getDoc().off('.rx-image-resize'),
      this.app.block.set(this.$block),
      setTimeout(
        function () {
          this.app.event.run();
        }.bind(this),
        10
      ),
      this.$resizer.text(''),
      this.app.broadcast('image.resize.stop', {
        e: i,
        block: this.$block,
        image: this.$image,
      }));
  },
  _remove() {
    this._find().remove();
  },
  _hide() {
    this._find().hide();
  },
  _show() {
    this._find().show();
  },
  _find() {
    return this.app.editor.getLayout().find('#rx-image-resizer');
  },
  _stopEvents() {
    this.app.scroll.getTarget().off('.rx-image-resize');
  },
  _setResizerPosition() {
    var i;
    this.$image &&
      ((i = this.$image.position()),
      this.$resizer.css({
        top: this.$image.height() / 2 - this.resizerHeight / 2 + 'px',
        left: i.left + (this.$image.width() - this.resizerWidth / 2) + 'px',
      }),
      this.$resizer.show());
  },
});
