Redactor.add('plugin', 'email', {
  translations: {
    en: {
      email: {
        button: 'Button',
        'mobile-view': 'Mobile view',
        'style-panel': 'Style panel',
        spacing: 'Spacing',
        padding: 'Padding',
        'margin-bottom': 'Margin bottom',
        alignment: 'Alignment',
        'size-and-color': 'Size & color',
        body: 'Body',
        background: 'Background',
        content: 'Content',
        'border-width': 'Border width',
        'border-color': 'Border color',
        radius: 'Radius',
        size: 'Size',
        color: 'Color',
        'align-left': 'Align left',
        'align-center': 'Align center',
        'align-right': 'Align right',
      },
    },
  },
  defaults: {
    preheader: !1,
    options: !1,
    style: !1,
    doctype: '<!DOCTYPE html>',
    mobileWidth: '420px',
    linkFont: !1,
    lang: 'en',
    title: '',
    spacing: '32px',
    gutter: '24px',
    font: 'Helvetica, Arial, sans-serif',
    dark: !0,
    body: { background: '#ffffff', padding: '32px 20px 64px 20px' },
    content: {
      width: '600px',
      background: '#ffffff',
      padding: '0',
      borderRadius: !1,
      border: !1,
    },
    text: { fontSize: '16px', lineHeight: '1.618', color: '#333333' },
    h1: {
      fontSize: '36px',
      fontWeight: 'bold',
      lineHeight: '1.3',
      letterSpacing: '0',
      textTransform: 'none',
      color: '#000000',
    },
    h2: {
      fontSize: '24px',
      fontWeight: 'bold',
      lineHeight: '1.3',
      letterSpacing: '0',
      textTransform: 'none',
      color: '#000000',
    },
    h3: {
      fontSize: '20px',
      fontWeight: 'bold',
      lineHeight: '1.4',
      letterSpacing: '0',
      textTransform: 'none',
      color: '#000000',
    },
    h4: {
      fontSize: '16px',
      fontWeight: 'bold',
      lineHeight: '1.4',
      letterSpacing: '0',
      textTransform: 'none',
      color: '#000000',
    },
    quote: {
      borderLeft: '2px solid #ccc',
      background: !1,
      padding: '0 0 0 24px',
      color: '#000000',
      fontWeight: 'bold',
      fontStyle: 'italic',
    },
    pre: {
      fontFamily: 'monospace',
      fontSize: '14px',
      lineHeight: 1.618,
      padding: '20px',
      borderRadius: !1,
      border: !1,
      color: '#333333',
      background: '#f5f5f5',
    },
    link: { color: '#046BFB' },
    button: {
      padding: '16px 0px',
      fontSize: '16px',
      fontWeight: '500',
      background: '#000000',
      color: '#ffffff',
      borderRadius: '8px',
    },
    divider: { size: '1px', background: '#000000' },
    _button: {
      title: '## email.button ##',
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 6C4.73478 6 4.48043 6.10536 4.29289 6.29289C4.10536 6.48043 4 6.73478 4 7V17C4 17.2652 4.10536 17.5196 4.29289 17.7071C4.48043 17.8946 4.73478 18 5 18H19C19.2652 18 19.5196 17.8946 19.7071 17.7071C19.8946 17.5196 20 17.2652 20 17V7C20 6.73478 19.8946 6.48043 19.7071 6.29289C19.5196 6.10536 19.2652 6 19 6H5ZM2.87868 4.87868C3.44129 4.31607 4.20435 4 5 4H19C19.7957 4 20.5587 4.31607 21.1213 4.87868C21.6839 5.44129 22 6.20435 22 7V17C22 17.7957 21.6839 18.5587 21.1213 19.1213C20.5587 19.6839 19.7957 20 19 20H5C4.20435 20 3.44129 19.6839 2.87868 19.1213C2.31607 18.5587 2 17.7956 2 17V7C2 6.20435 2.31607 5.44129 2.87868 4.87868Z"/></svg>',
      position: { after: 'heading' },
      command: 'email.addButton',
    },
    _buttonPreview: {
      title: '## email.mobile-view ##',
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 4C7.73478 4 7.48043 4.10536 7.29289 4.29289C7.10536 4.48043 7 4.73478 7 5V19C7 19.2652 7.10536 19.5196 7.29289 19.7071C7.48043 19.8946 7.73478 20 8 20H16C16.2652 20 16.5196 19.8946 16.7071 19.7071C16.8946 19.5196 17 19.2652 17 19V5C17 4.73478 16.8946 4.48043 16.7071 4.29289C16.5196 4.10536 16.2652 4 16 4H14C14 4.55228 13.5523 5 13 5H11C10.4477 5 10 4.55228 10 4H8ZM5.87868 2.87868C6.44129 2.31607 7.20435 2 8 2H16C16.7956 2 17.5587 2.31607 18.1213 2.87868C18.6839 3.44129 19 4.20435 19 5V19C19 19.7957 18.6839 20.5587 18.1213 21.1213C17.5587 21.6839 16.7957 22 16 22H8C7.20435 22 6.44129 21.6839 5.87868 21.1213C5.31607 20.5587 5 19.7957 5 19V5C5 4.20435 5.31607 3.44129 5.87868 2.87868ZM12 16C12.5523 16 13 16.4477 13 17V17.01C13 17.5623 12.5523 18.01 12 18.01C11.4477 18.01 11 17.5623 11 17.01V17C11 16.4477 11.4477 16 12 16Z"/></svg>',
      position: { after: 'add' },
      command: 'email.showPreview',
    },
    _buttonStyle: {
      title: '## email.style-panel ##',
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M21.7071 2.2929C21.9282 2.51399 22.0332 2.82575 21.9908 3.13553C21.576 6.1681 20.3501 9.03243 18.4426 11.4262C16.7174 13.5911 14.4924 15.2985 11.9645 16.4051C12.0663 17.2548 11.9485 18.1188 11.6194 18.9134C11.241 19.8271 10.6001 20.608 9.77785 21.1574C8.95561 21.7068 7.98891 22 7 22H3C2.44772 22 2 21.5523 2 21V17C2 16.0111 2.29325 15.0444 2.84265 14.2222C3.39206 13.3999 4.17295 12.759 5.08658 12.3806C5.88121 12.0515 6.74521 11.9337 7.59494 12.0355C8.70148 9.50756 10.4089 7.28262 12.5739 5.55739C14.9676 3.64989 17.8319 2.42403 20.8645 2.00923C21.1743 1.96686 21.486 2.07182 21.7071 2.2929ZM7.7591 14.0976C7.78742 14.1115 7.81664 14.1242 7.84674 14.1355C7.91251 14.1604 7.97926 14.1778 8.04607 14.1883C8.44817 14.3379 8.81533 14.5727 9.12132 14.8787C9.4273 15.1847 9.66209 15.5518 9.81169 15.9539C9.82217 16.0207 9.83962 16.0875 9.86447 16.1533C9.87584 16.1834 9.88851 16.2127 9.90239 16.241C9.9174 16.2984 9.93074 16.3563 9.94236 16.4147C10.0581 16.9967 9.9987 17.5999 9.77164 18.1481C9.54458 18.6962 9.16006 19.1648 8.66671 19.4944C8.17336 19.8241 7.59334 20 7 20H4V17C4 16.4067 4.17595 15.8266 4.50559 15.3333C4.83524 14.84 5.30377 14.4554 5.85195 14.2284C6.40013 14.0013 7.00333 13.9419 7.58527 14.0577C7.64372 14.0693 7.70168 14.0826 7.7591 14.0976ZM11.3293 14.4987C11.1122 14.1228 10.8458 13.7748 10.5355 13.4645C10.2253 13.1542 9.87725 12.8878 9.50135 12.6707C9.88304 11.8268 10.3411 11.0223 10.8685 10.2667C12.0533 10.9599 13.0401 11.9467 13.7333 13.1315C12.9777 13.6589 12.1732 14.117 11.3293 14.4987ZM15.3062 11.8681C15.8708 11.3484 16.3967 10.7843 16.8785 10.1798C18.2688 8.43509 19.2487 6.40712 19.7538 4.24619C17.5929 4.75135 15.5649 5.73124 13.8203 7.12151C13.2157 7.60328 12.6516 8.12921 12.1319 8.6938C13.4167 9.49756 14.5024 10.5833 15.3062 11.8681Z"/></svg>',
      position: { after: 'add' },
      command: 'email.popupStyle',
    },
    _buttonSpacing: {
      title: '## email.spacing ##',
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M4 3C4.55228 3 5 3.44772 5 4V4.01C5 4.56229 4.55228 5.01 4 5.01C3.44772 5.01 3 4.56229 3 4.01V4C3 3.44772 3.44772 3 4 3ZM8 3C8.55228 3 9 3.44772 9 4V4.01C9 4.56229 8.55228 5.01 8 5.01C7.44772 5.01 7 4.56229 7 4.01V4C7 3.44772 7.44772 3 8 3ZM12 3C12.5523 3 13 3.44772 13 4V4.01C13 4.56229 12.5523 5.01 12 5.01C11.4477 5.01 11 4.56229 11 4.01V4C11 3.44772 11.4477 3 12 3ZM16 3C16.5523 3 17 3.44772 17 4V4.01C17 4.56229 16.5523 5.01 16 5.01C15.4477 5.01 15 4.56229 15 4.01V4C15 3.44772 15.4477 3 16 3ZM20 3C20.5523 3 21 3.44772 21 4V4.01C21 4.56229 20.5523 5.01 20 5.01C19.4477 5.01 19 4.56229 19 4.01V4C19 3.44772 19.4477 3 20 3ZM4 7C4.55228 7 5 7.44772 5 8V8.01C5 8.56228 4.55228 9.01 4 9.01C3.44772 9.01 3 8.56228 3 8.01V8C3 7.44772 3.44772 7 4 7ZM7 8C7 7.44772 7.44772 7 8 7H16C16.5523 7 17 7.44772 17 8V16C17 16.5523 16.5523 17 16 17H8C7.44772 17 7 16.5523 7 16V8ZM9 9V15H15V9H9ZM20 7C20.5523 7 21 7.44772 21 8V8.01C21 8.56228 20.5523 9.01 20 9.01C19.4477 9.01 19 8.56228 19 8.01V8C19 7.44772 19.4477 7 20 7ZM4 11C4.55228 11 5 11.4477 5 12V12.01C5 12.5623 4.55228 13.01 4 13.01C3.44772 13.01 3 12.5623 3 12.01V12C3 11.4477 3.44772 11 4 11ZM20 11C20.5523 11 21 11.4477 21 12V12.01C21 12.5623 20.5523 13.01 20 13.01C19.4477 13.01 19 12.5623 19 12.01V12C19 11.4477 19.4477 11 20 11ZM4 15C4.55228 15 5 15.4477 5 16V16.01C5 16.5623 4.55228 17.01 4 17.01C3.44772 17.01 3 16.5623 3 16.01V16C3 15.4477 3.44772 15 4 15ZM20 15C20.5523 15 21 15.4477 21 16V16.01C21 16.5623 20.5523 17.01 20 17.01C19.4477 17.01 19 16.5623 19 16.01V16C19 15.4477 19.4477 15 20 15ZM4 19C4.55228 19 5 19.4477 5 20V20.01C5 20.5623 4.55228 21.01 4 21.01C3.44772 21.01 3 20.5623 3 20.01V20C3 19.4477 3.44772 19 4 19ZM8 19C8.55228 19 9 19.4477 9 20V20.01C9 20.5623 8.55228 21.01 8 21.01C7.44772 21.01 7 20.5623 7 20.01V20C7 19.4477 7.44772 19 8 19ZM12 19C12.5523 19 13 19.4477 13 20V20.01C13 20.5623 12.5523 21.01 12 21.01C11.4477 21.01 11 20.5623 11 20.01V20C11 19.4477 11.4477 19 12 19ZM16 19C16.5523 19 17 19.4477 17 20V20.01C17 20.5623 16.5523 21.01 16 21.01C15.4477 21.01 15 20.5623 15 20.01V20C15 19.4477 15.4477 19 16 19ZM20 19C20.5523 19 21 19.4477 21 20V20.01C21 20.5623 20.5523 21.01 20 21.01C19.4477 21.01 19 20.5623 19 20.01V20C19 19.4477 19.4477 19 20 19Z"/></svg>',
      position: { before: ['duplicate', 'trash'] },
      command: 'email.popupSpacing',
    },
    _buttonImageAlign: {
      title: '## email.alignment ##',
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.58579 4.58579C8.96086 4.21071 9.46957 4 10 4H14C14.5304 4 15.0391 4.21071 15.4142 4.58579C15.7893 4.96086 16 5.46957 16 6V10C16 10.5304 15.7893 11.0391 15.4142 11.4142C15.0391 11.7893 14.5304 12 14 12H10C9.46957 12 8.96086 11.7893 8.58579 11.4142C8.21071 11.0391 8 10.5304 8 10V6C8 5.46957 8.21071 4.96086 8.58579 4.58579ZM14 6L10 6V10H14V6ZM3 7C3 6.44772 3.44772 6 4 6H5C5.55228 6 6 6.44772 6 7C6 7.55228 5.55228 8 5 8H4C3.44772 8 3 7.55228 3 7ZM18 7C18 6.44772 18.4477 6 19 6H20C20.5523 6 21 6.44772 21 7C21 7.55228 20.5523 8 20 8H19C18.4477 8 18 7.55228 18 7ZM3 11C3 10.4477 3.44772 10 4 10H5C5.55228 10 6 10.4477 6 11C6 11.5523 5.55228 12 5 12H4C3.44772 12 3 11.5523 3 11ZM18 11C18 10.4477 18.4477 10 19 10H20C20.5523 10 21 10.4477 21 11C21 11.5523 20.5523 12 20 12H19C18.4477 12 18 11.5523 18 11ZM3 15C3 14.4477 3.44772 14 4 14H20C20.5523 14 21 14.4477 21 15C21 15.5523 20.5523 16 20 16H4C3.44772 16 3 15.5523 3 15ZM3 19C3 18.4477 3.44772 18 4 18H20C20.5523 18 21 18.4477 21 19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19Z"/></svg>',
      position: { before: ['duplicate', 'trash'] },
      command: 'email.popupImageAlign',
      blocks: { types: ['image'] },
    },
    _buttonLine: {
      title: '## email.size-and-color ##',
      icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6 3C6.55228 3 7 3.44772 7 4V7.17157C7.4179 7.31933 7.80192 7.55928 8.12132 7.87868C8.68393 8.44129 9 9.20435 9 10C9 10.7956 8.68393 11.5587 8.12132 12.1213C7.80192 12.4407 7.4179 12.6807 7 12.8284V20C7 20.5523 6.55228 21 6 21C5.44772 21 5 20.5523 5 20V12.8284C4.5821 12.6807 4.19808 12.4407 3.87868 12.1213C3.31607 11.5587 3 10.7956 3 10C3 9.20435 3.31607 8.44129 3.87868 7.87868C4.19808 7.55928 4.5821 7.31933 5 7.17157V4C5 3.44772 5.44772 3 6 3ZM12 3C12.5523 3 13 3.44772 13 4V13.1716C13.4179 13.3193 13.8019 13.5593 14.1213 13.8787C14.6839 14.4413 15 15.2043 15 16C15 16.7957 14.6839 17.5587 14.1213 18.1213C13.8019 18.4407 13.4179 18.6807 13 18.8284V20C13 20.5523 12.5523 21 12 21C11.4477 21 11 20.5523 11 20V18.8284C10.5821 18.6807 10.1981 18.4407 9.87868 18.1213C9.31607 17.5587 9 16.7957 9 16C9 15.2043 9.31607 14.4413 9.87868 13.8787C10.1981 13.5593 10.5821 13.3193 11 13.1716V4C11 3.44772 11.4477 3 12 3ZM18 3C18.5523 3 19 3.44772 19 4V4.17157C19.4179 4.31933 19.8019 4.55927 20.1213 4.87868C20.6839 5.44129 21 6.20435 21 7C21 7.79565 20.6839 8.55871 20.1213 9.12132C19.8019 9.44072 19.4179 9.68067 19 9.82843V20C19 20.5523 18.5523 21 18 21C17.4477 21 17 20.5523 17 20V9.82843C16.5821 9.68067 16.1981 9.44072 15.8787 9.12132C15.3161 8.55871 15 7.79565 15 7C15 6.20435 15.3161 5.44129 15.8787 4.87868C16.1981 4.55927 16.5821 4.31933 17 4.17157V4C17 3.44772 17.4477 3 18 3ZM18 6C17.7348 6 17.4804 6.10536 17.2929 6.29289C17.1054 6.48043 17 6.73478 17 7C17 7.26522 17.1054 7.51957 17.2929 7.70711C17.4804 7.89464 17.7348 8 18 8C18.2652 8 18.5196 7.89464 18.7071 7.70711C18.8946 7.51957 19 7.26522 19 7C19 6.73478 18.8946 6.48043 18.7071 6.29289C18.5196 6.10536 18.2652 6 18 6ZM6 9C5.73478 9 5.48043 9.10536 5.29289 9.29289C5.10536 9.48043 5 9.73478 5 10C5 10.2652 5.10536 10.5196 5.29289 10.7071C5.48043 10.8946 5.73478 11 6 11C6.26522 11 6.51957 10.8946 6.70711 10.7071C6.89464 10.5196 7 10.2652 7 10C7 9.73478 6.89464 9.48043 6.70711 9.29289C6.51957 9.10536 6.26522 9 6 9ZM12 15C11.7348 15 11.4804 15.1054 11.2929 15.2929C11.1054 15.4804 11 15.7348 11 16C11 16.2652 11.1054 16.5196 11.2929 16.7071C11.4804 16.8946 11.7348 17 12 17C12.2652 17 12.5196 16.8946 12.7071 16.7071C12.8946 16.5196 13 16.2652 13 16C13 15.7348 12.8946 15.4804 12.7071 15.2929C12.5196 15.1054 12.2652 15 12 15Z"/></svg>',
      position: { before: ['duplicate', 'trash'] },
      command: 'email.popupLine',
      blocks: { types: ['line'] },
    },
    _headStyle:
      '* { margin: 0; } table, td{mso-table-lspace:0pt; mso-table-rspace:0pt;} pre { white-space: pre-wrap; word-wrap: break-word; } .email-column-container { table-layout: fixed; width: 100%; } .email-column { vertical-align: top; } @media screen and (max-width: 500px) { .email-container { width: 100% !important; } .email-column, .email-column-spacer { width: 100% !important; display: block; } .email-column-spacer { height: ===gutter===; } .mobile-center {text-align: center !important; } .mobile-right { text-align: right !important; } .mobile-left { text-align: left !important; } .mobile-hidden { max-height: 0; display: none !important; mso-hide: all; overflow: hidden; } }',
    _darkStyle:
      '@media screen and (prefers-color-scheme: dark) { body, table, .email-content { background: #0F1721 !important; } .email-wrapper td { background: #1B232C !important; } .email-link { color: #66BFFF !important; text-decoration-color: #66BFFF !important; } h1, h2, h3, h4, h5, h6 { color: #E7E8E9 !important; } p, ul, ol, span { color: #CFD1D3 !important; } quote, pre { color: #CFD1D3 !important; } }',
  },
  _defaults: !1,
  subscribe: {
    'editor.insert, format.set': function (t) {
      this.parseLayout(t);
    },
    'blockbackground.set, blockcolor.set': function (t) {
      return this.setColor(t);
    },
    'blockfontsize.set': function (t) {
      return this.setFontSize(t);
    },
    'blockbackground.get, blockcolor.get': function (t) {
      return this.getColor(t);
    },
    'blockfontsize.get': function (t) {
      return this.getFontSize(t);
    },
    'blockborder.set': function (t) {
      return this.setBorder(t);
    },
    'blockborder.get': function (t) {
      return this.getBorder(t);
    },
    'format.before.set': function (t) {
      return this._beforeFormat(t);
    },
  },
  init() {
    ((this.utils = this.app.create('utils')),
      (this.cache = this.app.create('cleaner-cache')),
      (this.insertion = this.app.create('insertion')));
  },
  start() {
    (this.app.addbar.add('button', this.defaults._button),
      this.app.toolbar.add('style-panel', this.defaults._buttonStyle),
      this.app.toolbar.add('preview', this.defaults._buttonPreview),
      this.app.control.add('imagealign', this.defaults._buttonImageAlign),
      this.app.control.add('spacing', this.defaults._buttonSpacing),
      this.app.control.add('line-setting', this.defaults._buttonLine),
      this.app.toolbar.remove(['todo', 'embed']),
      this.app.addbar.remove(['todo', 'embed']),
      this.app.control.remove(['outset', 'wrap']),
      this.app.format.remove(['todo']),
      (this.$main = this.app.editor.getEditor()),
      (this.$cont = this.app.container.get('editor')),
      this.$main.addClass('rx-editor-email'),
      (this._defaults = { ...this.defaults }),
      this._build());
  },
  stop() {
    this.app.$body.find('#rx-email-style-' + this.uuid).remove();
  },
  load() {
    this.app.editor
      .getEditor()
      .find('img')
      .each(function (t) {
        t.one('load', function () {
          t.attr({ width: t.width(), height: t.height() });
        });
      });
  },
  popupSpacing(t, e) {
    let i = this.app.block.get(),
      s = i.getBlock(),
      a = s.children().first(),
      o = {
        padding: a.hasClass('email-button')
          ? a.css('padding')
          : s.css('padding'),
        margin: parseInt(s.css('margin-bottom')),
      },
      l = this.app.create('form'),
      n = {
        padding: { type: 'input', label: '## email.padding ##' },
        margin: {
          type: 'number',
          width: '80px',
          label: '## email.margin-bottom ##',
        },
      };
    (i.isType('line') && delete n.padding,
      l.create({
        data: o,
        setter: 'email.setSpacing',
        command: 'email.saveSpacing',
        items: n,
      }),
      this.app.dropdown.create('spacing', { form: l, focus: !0 }),
      this.app.dropdown.open(t, e));
  },
  popupStyle(t, e) {
    let i = this.utils.cssToObject(this.$cont.attr('style')),
      s = this.utils.cssToObject(this.$main.attr('style')),
      a = !1,
      o = '';
    if (s.border) {
      let t = s.border.split(' ');
      ((a = parseInt(t[0])), (o = t[2]));
    }
    let l = {
        bodypadding: this.$cont.css('padding'),
        bodycolor: i.background,
        mainpadding: this.$main.css('padding'),
        maincolor: s.background,
        mainborderwidth: a,
        mainborderradius: parseInt(this.$main.css('border-radius')),
        mainbordercolor: o,
      },
      n = this.app.create('form');
    (n.create({
      data: l,
      setter: 'email.setStyle',
      command: 'email.saveStyle',
      items: {
        sectionbody: { title: '## email.body ##' },
        bodypadding: { type: 'input', label: '## email.padding ##' },
        bodycolor: { type: 'color', label: '## email.background ##' },
        sectionmain: { title: '## email.content ##' },
        mainpadding: { type: 'input', label: '## email.padding ##' },
        maincolor: { type: 'color', label: '## email.background ##' },
        flex: {
          mainborderwidth: {
            type: 'number',
            label: '## email.border-width ##',
          },
          mainborderradius: { type: 'number', label: '## email.radius ##' },
        },
        mainbordercolor: { type: 'color', label: '## email.border-color ##' },
      },
    }),
      this.app.dropdown.create('email', { form: n, focus: 'bodypadding' }),
      this.app.dropdown.open(t, e));
  },
  popupLine(t, e) {
    let i = this.app.block.get().getBlock(),
      s = this.utils.cssToObject(i.attr('style')),
      a = { size: parseInt(s.height), color: s.background },
      o = this.app.create('form');
    (o.create({
      data: a,
      setter: 'email.setLine',
      command: 'email.saveLine',
      items: {
        size: { type: 'number', label: '## email.size ##' },
        color: { type: 'color', label: '## email.color ##' },
      },
    }),
      this.app.dropdown.create('line-setting', { form: o, focus: 'size' }),
      this.app.dropdown.open(t, e));
  },
  popupImageAlign(t, e) {
    let i = this.app.block.get().getStyle(),
      s = {
        left: {
          title: '## email.align-left ##',
          command: 'email.saveImageAlign',
        },
        center: {
          title: '## email.align-center ##',
          command: 'email.saveImageAlign',
        },
        right: {
          title: '## email.align-right ##',
          command: 'email.saveImageAlign',
        },
      };
    (i && i['text-align'] && (s[i['text-align']].active = !0),
      this.app.dropdown.create('imagealign', { items: s }),
      this.app.dropdown.open(t, e));
  },
  reset() {
    ((this.defaults = { ...this._defaults }),
      this.opts.set('email', this._defaults));
  },
  setSpacing(t) {
    this._saveSpacing(t);
  },
  saveSpacing(t) {
    (this.app.dropdown.close(), this._saveSpacing(t));
  },
  _saveSpacing(t) {
    let e = this.app.block.get(),
      i = t.getData(),
      s = e.getBlock().children().first();
    s.hasClass('email-button')
      ? (e.setStyle({ 'margin-bottom': i.margin + 'px' }),
        s.css({ padding: i.padding }),
        this.cache.cacheElementStyle(s))
      : e.setStyle({ 'margin-bottom': i.margin + 'px', padding: i.padding });
  },
  setStyle(t) {
    this._saveStyle(t);
  },
  saveStyle(t) {
    (this.app.dropdown.close(), this._saveStyle(t));
  },
  _saveStyle(t) {
    let e = t.getData(),
      i =
        '0' === e.mainborderradius || '' === e.mainborderradius
          ? ''
          : e.mainborderradius + 'px',
      s =
        '0' === e.mainborderwidth || '' === e.mainborderwidth
          ? ''
          : e.mainborderwidth + 'px solid ' + e.mainbordercolor;
    (this.$main.css({
      border: s,
      'border-radius': i,
      padding: e.mainpadding,
      background: e.maincolor,
    }),
      this.$cont.css({ padding: e.bodypadding, background: e.bodycolor }));
    let a = this.opts.get('email.options');
    if (a) {
      let t = JSON.parse(a),
        o = {
          body: { background: e.bodycolor, padding: e.bodypadding },
          content: {
            padding: e.mainpadding,
            border: s,
            borderRadius: i,
            background: e.maincolor,
          },
        };
      a = t = { ...t, ...o };
    } else
      ((a = `{\n                "body": {\n                    "background": "${e.bodycolor}",\n                    "padding": "${e.bodypadding}"\n                },\n                "content": {\n                    "padding": "${e.mainpadding}",\n                    "border": "${s}",\n                    "borderRadius": "${i}",\n                    "background": "${e.maincolor}"\n                }\n            }`),
        (a = JSON.parse(a)));
    this.opts.set('email.options', JSON.stringify(a, null, 4));
  },
  setLine(t) {
    this._saveLine(t);
  },
  saveLine(t) {
    (this.app.dropdown.close(), this._saveLine(t));
  },
  _saveLine(t) {
    let e = this.app.block.get(),
      i = t.getData();
    e.setStyle({ height: i.size + 'px', background: i.color });
  },
  saveImageAlign(t, e, i) {
    (this.app.dropdown.close(),
      this.app.block.get().setStyle({ 'text-align': i }),
      this.app.broadcast('image.position'));
  },
  setFontSize(t) {
    let e = t.get('data'),
      i = this.app.block.get().getBlock().children().first();
    i.hasClass('email-button') &&
      (t.stop(),
      i.css({ 'font-size': e.size + 'px', 'line-height': e.line }),
      this.cache.cacheElementStyle(i));
  },
  setColor(t) {
    let e = t.get('style'),
      i = this.app.block.get().getBlock().children().first();
    i.hasClass('email-button') &&
      (t.stop(), i.css(e), this.cache.cacheElementStyle(i));
  },
  setBorder(t) {
    let e = t.get('data'),
      i = this.app.block.get().getBlock().children().first();
    i.hasClass('email-button') &&
      (t.stop(),
      i.css({ 'border-radius': e.radius, border: e.border }),
      this.cache.cacheElementStyle(i));
  },
  setContent(t) {
    (!1 !== t.reset && this.reset(),
      this.insertion.set(t),
      this.parse(this.$main, !1),
      this._buildSpacing(),
      this._build(!0));
  },
  setJson(t) {
    (this.reset(), this.app.editor.content.set(t, 'json'));
    let e = ['preheader', 'options', 'style'];
    for (let i = 0; i < e.length; i++)
      t[e[i]] &&
        ('options' === e[i]
          ? (this.opts.set(
              'email',
              Redactor.extend(!0, this.opts.get('email'), t[e[i]])
            ),
            this.opts.set('email.options', JSON.stringify(t[e[i]], null, 4)))
          : this.opts.set('email.' + e[i], t[e[i]]));
    this._build(!0);
  },
  setOptions(t) {
    t && ((this.defaults = t), this.opts.set('email', t));
  },
  getFontSize(t) {
    let e = this.app.block.get().getBlock(),
      i = e.children().first(),
      s = i.hasClass('email-button') ? i : e,
      a = this.utils.cssToObject(s.attr('style'));
    return (
      t.set('data', {
        size: a['font-size']
          ? parseInt(a['font-size'])
          : parseInt(this.opts.get('email.text.fontSize')),
        line: a['line-height']
          ? a['line-height']
          : this.opts.get('email.text.lineHeight'),
      }),
      t
    );
  },
  getBorder(t) {
    let e = this.app.block.get().getBlock(),
      i = e.children().first(),
      s = i.hasClass('email-button'),
      a = this.utils.cssToObject(e.attr('style')),
      o = !1,
      l = '';
    if ((s && (a = this.utils.cssToObject(i.attr('style'))), a.border)) {
      let t = a.border.split(' ');
      ((o = parseInt(t[0])), (l = t[2]));
    }
    return (
      t.set('data', {
        width: o,
        color: l,
        radius: parseInt(a['border-radius']),
      }),
      t
    );
  },
  getColor(t) {
    let e = this.app.block.get().getBlock(),
      i = e.children().first(),
      s = i.hasClass('email-button'),
      a = this.utils.cssToObject(e.attr('style'));
    return (
      s && (a = this.utils.cssToObject(i.attr('style'))),
      t.set('style', { color: a.color, background: a.background }),
      t
    );
  },
  getEmail(t) {
    let e = this._buildHtmlEmail(),
      i = this._buildHeadEmail(e),
      s = this._buildBodyEmail(e),
      a = this._buildContainerEmail(s),
      o = this._buildMainEmail(a);
    (this._buildContentEmail(o), this._buildPreheaderEmail(s));
    let l = this.opts.get('email.doctype');
    this._buildFontLink(i);
    let n = l + e.outer();
    if (t) {
      n = this.app.create('tidy').parse(n, 'email');
    }
    return (n = (n = this.utils.replaceRgbToHex(n))
      .replace(/&amp;/g, '&')
      .replace(/&quot;(.*?)&quot;/gi, "'$1'"));
  },
  getContent() {
    let t = this.app.editor.getLayout().html();
    return ((t = this.unparse(t)), (t = this.unparseBlocks(t)));
  },
  getJson() {
    let t = {},
      e = [],
      i = this.dom('<div>'),
      s = this.app.editor.getHtml();
    const a = this.app.create('unparser'),
      o = this.app.create('parser');
    ((s = a.unparse(s)),
      i.html(s),
      i.find('.email-options, .email-preheader, .email-style').remove());
    let l = o.parse(i.html(), { type: 'html', nodes: !0 });
    (i.html(l),
      i.children('[data-rx-type]').each(function (t) {
        let i = t.dataget('instance');
        e.push(i.getJson());
      }),
      (t.blocks = e));
    let n = this.opts.get('email.options');
    n && (t.options = JSON.parse(n));
    let r = ['preheader', 'style'];
    for (let e = 0; e < r.length; e++)
      this.opts.is('email.' + r[e]) &&
        (t[r[e]] = this.opts.get('email.' + r[e]).trim());
    return t;
  },
  addButton() {
    this.app.dropdown.close();
    let t = this.app.block.create(),
      e = this.dom('<a href="#">');
    (e.addClass('email-button'),
      e.html(this.lang.parse('## email.button ##')),
      t.getBlock().append(e),
      this.insertion.insert({ instance: t }));
  },
  showPreview() {
    if (this.$preview) return this.hidePreview();
    (this.app.blocks.unset(),
      this.app.block.unset(),
      this.app.editor.unsetSelectAll(),
      this.app.ui.close(),
      this.app.ui.disable(),
      this.app.toolbar.setToggled('preview'));
    let t = this.getEmail();
    ((this.$preview = this.app.container.get('preview')),
      this.app.container.get('editor').hide());
    let e = this.dom('<iframe>');
    (e.attr('scrolling', 'no'),
      e.css({
        border: '1px solid var(--rx-border-dark-subtle)',
        width: this.defaults.mobileWidth,
        'border-radius': '12px',
        margin: '0 auto',
        background: 'var(--rx-bg-raised)',
        display: 'block',
      }),
      this.$preview.css({
        background: 'var(--rx-bg-aluminum)',
        padding: '24px 0 40px 0',
      }),
      this.$preview.html(''),
      this.$preview.append(e));
    let i = e.get().contentWindow.document;
    (i.open(), i.write(t), i.close(), this.$preview.show());
    let s = e.get(),
      a = s.contentDocument || s.contentWindow;
    a.document && (a = a.document);
    let o = setInterval(
      function () {
        'complete' == a.readyState &&
          (clearInterval(o),
          this._setFrameHeight(e),
          setTimeout(
            function () {
              this._setFrameHeight(e);
            }.bind(this),
            200
          ));
      }.bind(this),
      100
    );
  },
  hidePreview() {
    ((this.$preview = !1),
      this.app.container.get('preview').hide(),
      this.app.container.get('editor').show(),
      this.app.ui.enable(),
      this.app.toolbar.unsetToggled('preview'));
  },
  parse(t, e) {
    return (
      !1 !== e && this._buildOptions(),
      t.find('a').each(this._setLinkStyle.bind(this)),
      t.find('.email-button').each(this._setButtonStyle.bind(this)),
      t.find('[data-rx-type=heading]').each(this._setHeadingStyle.bind(this)),
      t.find('[data-rx-type=image]').each(this._setImageStyle.bind(this)),
      t.find('hr').each(this._setDividerStyle.bind(this)),
      t.find('[data-rx-type=list]').each(this._setListStyle.bind(this)),
      t
    );
  },
  parseLayout() {
    (this.parse(this.$main), this._buildSpacing());
  },
  parseBlocks(t) {
    let e = ['preheader', 'options', 'style'];
    return (t = this.utils.wrap(
      t,
      function (t) {
        for (let i = 0; i < e.length; i++)
          t.find('.email-' + e[i]).each(
            function (t) {
              (this.opts.set('email.' + e[i], t.html()),
                'style' === e[i] && this._createStyle(t.html()),
                t.remove());
            }.bind(this)
          );
      }.bind(this)
    ));
  },
  unparse(t) {
    let e = this.opts.get('email.spacing');
    return (t = this.utils.wrap(
      t,
      function (t) {
        (t.find('.email-button').each(this._unparseButton.bind(this)),
          t.find('hr').each(this._unparseDivider.bind(this)),
          t
            .find('h1, h2, h3, h4, h5, h6')
            .each(this._unparseHeading.bind(this)),
          t.find('ul, ol').each(this._unparseList.bind(this)),
          t.children().each(
            function (t) {
              let i = this.utils.cssToObject(t.attr('style'));
              (t.css(
                'margin-bottom',
                i['margin-bottom'] && i['margin-bottom'] === e
                  ? ''
                  : i['margin-bottom']
              ),
                '' === t.attr('style') && t.removeAttr('style'));
            }.bind(this)
          ));
      }.bind(this)
    ));
  },
  unparseBlocks(t) {
    let e,
      i = ['preheader', 'options', 'style'],
      s = '',
      a = this.dom('<div>');
    for (let o = 0; o < i.length; o++)
      ((e = this.opts.get('email.' + i[o])),
        a.html(t),
        e &&
          0 === a.find('.email-' + i[o]).length &&
          (t =
            (s = '<div class="email-' + i[o] + '">' + e + '</div>\r\n') + t));
    return t;
  },
  _unparseList(t) {
    t.css({ 'list-style-type': '', 'margin-left': '' });
  },
  _unparseHeading(t) {
    let e = this.utils.cssToObject(t.attr('style')),
      i = t.get().tagName.toLowerCase();
    t.css({
      'font-size':
        e['font-size'] === this.opts.get('email.' + i + '.fontSize')
          ? ''
          : e['font-size'],
      'font-weight':
        e['font-weight'] === this.opts.get('email.' + i + '.fontWeight')
          ? ''
          : e['font-weight'],
      'line-height':
        e['line-height'] === this.opts.get('email.' + i + '.lineHeight')
          ? ''
          : e['line-height'],
      color: e.color === this.opts.get('email.' + i + '.color') ? '' : e.color,
    });
  },
  _unparseDivider(t) {
    let e = this.utils.cssToObject(t.attr('style'));
    t.css({
      'margin-top': '',
      padding: '',
      border: '',
      background:
        e.background === this.opts.get('email.divider.background')
          ? ''
          : e.background,
      height: e.height === this.opts.get('email.divider.size') ? '' : e.height,
    });
  },
  _unparseButton(t) {
    let e = this.utils.cssToObject(t.attr('style'));
    t.css({
      cursor: '',
      'text-decoration': '',
      'text-align': '',
      width: '',
      margin: '',
      display: '',
      'line-height': '',
      'font-family': '',
      'font-size':
        e['font-size'] === this.opts.get('email.button.fontSize')
          ? ''
          : e['font-size'],
      'font-weight':
        e['font-weight'] === this.opts.get('email.button.fontWeight')
          ? ''
          : e['font-weight'],
      padding:
        e.padding === this.opts.get('email.button.padding') ? '' : e.padding,
      background:
        e.background === this.opts.get('email.button.background')
          ? ''
          : e.background,
      color: e.color === this.opts.get('email.button.color') ? '' : e.color,
      'border-radius':
        e['border-radius'] === this.opts.get('email.button.borderRadius')
          ? ''
          : e['border-radius'],
    });
  },
  _setLinkStyle(t) {
    if (t.hasClass('email-button') || 0 !== t.find('img').length) return;
    let e = this.opts.get('email.link');
    (this._setStyle(t, e), this._cacheStyle(t));
  },
  _setButtonStyle(t) {
    let e = this.opts.get('email.button');
    (this._setStyle(t, e),
      t.css({
        cursor: 'pointer',
        'line-height': '1',
        'text-decoration': 'none',
        'text-align': 'center',
        width: '100%',
        margin: '0',
        display: 'block',
      }),
      this._setFontFamily(t),
      this._cacheStyle(t));
  },
  _setHeadingStyle(t) {
    let e = t.get().tagName.toLowerCase(),
      i = this.opts.get('email.' + e);
    (this._setStyle(t, i), this._cacheStyle(t));
  },
  _setDividerStyle(t) {
    let e = this.utils.cssToObject(t.attr('style')),
      i = e.height ? e.height : this.opts.get('email.divider.size'),
      s = e.background
        ? e.background
        : this.opts.get('email.divider.background');
    (t.css({
      'margin-top': '0',
      padding: '0',
      border: 'none',
      height: i,
      background: s,
    }),
      this._cacheStyle(t));
  },
  _setListStyle(t) {
    let e = 'ul' === t.get().tagName.toLowerCase() ? 'disc' : 'decimal';
    (t.css('list-style-type', e), this._cacheStyle(t));
  },
  _setImageStyle(t) {
    let e = this.utils.cssToObject(t.attr('style'));
    t.css('text-align', e['text-align'] ? e['text-align'] : '');
  },
  _setFontFamily(t) {
    t.css('font-family', this.opts.get('email.font'));
  },
  _createStyle(t) {
    const e = this.app.$body.find('#rx-email-style-' + this.uuid),
      i = this.app.container.get('main');
    let s;
    (0 === e.length
      ? ((s = this.dom('<style id="rx-email-style-' + this.uuid + '">')),
        i.before(s))
      : (s = e).html(''),
      s.append(t));
  },
  _build(t) {
    (this._buildBody(t), this._buildMain(t), this._buildSpacing());
  },
  _buildOptions() {
    let t = this.opts.get('email.options');
    if (t) {
      t = t.trim();
      let e = JSON.parse(t);
      this.opts.set('email', Redactor.extend(!0, this.opts.get('email'), e));
    }
  },
  _buildBody(t) {
    let e = this.opts.get('email.body'),
      i = t ? {} : this.utils.cssToObject(this.$cont.attr('style'));
    (t && this.$cont.removeAttr('style'),
      this.$cont.css(e),
      this.$cont.css({
        fontFamily: i['font-family']
          ? i['font-family']
          : this.opts.get('email.font'),
        fontSize: i['font-size']
          ? i['font-size']
          : this.opts.get('email.text.fontSize'),
        lineHeight: i['line-height']
          ? i['line-height']
          : this.opts.get('email.text.lineHeight'),
        color: i.color ? i.color : this.opts.get('email.text.color'),
      }));
  },
  _buildMain(t) {
    let e = { ...this.opts.get('email.content') };
    (t && this.$main.removeAttr('style'),
      (e['max-width'] = e.width),
      (e.width = ''),
      this.$main.css(e),
      this.$main.removeClass(this.opts.get('classname')));
  },
  _buildSpacing() {
    let t = this.app.blocks.get({ firstLevel: !0 }),
      e = t.length;
    t.each(
      function (t, i) {
        if (e !== i + 1) {
          let e = this.utils.cssToObject(t.attr('style'));
          (t.css(
            'margin-bottom',
            e['margin-bottom']
              ? e['margin-bottom']
              : this.opts.get('email.spacing')
          ),
            this._cacheStyle(t));
        }
      }.bind(this)
    );
  },
  _buildMarginBottom(t, e) {
    return t.hasClass('email-wrapper') || t.hasClass('email-footer')
      ? e['margin-bottom']
        ? e['margin-bottom']
        : this.opts.get('email.spacing')
      : 0 !==
          t.closest('[data-block=column], .email-wrapper, .email-footer').length
        ? 0
        : this.opts.get('email.spacing');
  },
  _buildHtmlEmail() {
    let t = this.dom('<html>');
    return (
      t.attr('xmlns', 'http://www.w3.org/1999/xhtml'),
      t.attr('lang', this.opts.get('email.lang')),
      t
    );
  },
  _buildHeadEmail(t) {
    let e = this.dom('<head>'),
      i = this.dom('<meta charset="UTF-8">');
    e.append(i);
    let s = this.dom('<meta name="color-scheme" content="light">');
    (this.opts.is('email.dark') &&
      (s = this.dom('<meta name="color-scheme" content="light dark">')),
      e.append(s));
    let a = this.dom('<title>');
    (a.text(this.opts.get('email.title')), e.append(a));
    let o = this.dom('<style type="text/css">'),
      l = this.opts.get('email._headStyle');
    l = (l = l.replace(
      new RegExp('===spacing===', 'gi'),
      this.opts.get('email.spacing')
    )).replace(new RegExp('===gutter===', 'gi'), this.opts.get('email.gutter'));
    let n = this.opts.get('email.style');
    if ((n && (l += n), this.opts.is('email.dark'))) {
      l += this.opts.get('email._darkStyle');
    }
    return (o.html(l), e.append(o), t.append(e), e);
  },
  _buildBodyEmail(t) {
    let e = this.dom('<body>'),
      i = this.utils.cssToObject(this.$cont.attr('style'));
    return (e.css(i), e.css({ margin: '0', padding: '0' }), t.append(e), e);
  },
  _buildContainerEmail(t) {
    let e = this.utils.cssToObject(this.$cont.attr('style')),
      i = this.dom('<table>');
    i.attr({
      cellpadding: '0',
      cellspacing: '0',
      width: '100%',
      role: 'presentation',
    });
    let s = this.dom('<tr>'),
      a = this.dom('<td>');
    return (
      a.addClass('email-body'),
      a.attr({ align: 'center' }),
      a.css({
        padding: e.padding ? e.padding : this.opts.get('email.body.padding'),
      }),
      s.append(a),
      i.append(s),
      t.append(i),
      a
    );
  },
  _buildMainEmail(t) {
    let e = this.utils.cssToObject(this.$main.attr('style')),
      i = this.opts.get('email.content.width'),
      s = this.dom('<table>');
    (s.addClass('email-container'),
      s.attr({
        width: -1 !== i.search('%') ? i : parseInt(i),
        cellpadding: '0',
        cellspacing: '0',
        role: 'presentation',
      }));
    let a = this.dom('<tr>'),
      o = this.dom('<td>');
    return (
      o.addClass('email-content'),
      o.css({
        margin: '0',
        background: e.background
          ? e.background
          : this.opts.get('email.content.background'),
        padding: e.padding ? e.padding : this.opts.get('email.content.padding'),
        border: e.border ? e.border : this.opts.get('email.content.border'),
        'border-radius': e['border-radius']
          ? e['border-radius']
          : this.opts.get('email.content.borderRadius'),
      }),
      a.append(o),
      s.append(a),
      t.append(s),
      o
    );
  },
  _buildPreheaderEmail(t) {
    let e = this.dom('<span>');
    (e.css({
      color: 'transparent',
      display: 'none',
      height: 0,
      'max-height': 0,
      'max-width': 0,
      opacity: 0,
      overflow: 'hidden',
      visibility: 'hidden',
      width: 0,
    }),
      e.attr('style', e.attr('style') + ';mso-hide:all;'),
      t.find('.email-preheader').each(function (i) {
        let s = i.text();
        (e.text(s), t.prepend(e), i.remove());
      }));
  },
  _buildFontLink(t) {
    let e = this.opts.get('email.linkFont');
    if (e) {
      let i = this.dom('<link href="' + e + '" rel="stylesheet">');
      t.append(i);
    }
  },
  _buildContentEmail(t) {
    let e = this.app.editor.getSource();
    ((e = this.utils.wrap(
      e,
      function (t) {
        (t.find('.email-options, .email-style').remove(),
          t.find('a').each(this._buildLink.bind(this)),
          t.find('.email-button').each(this._buildButton.bind(this)),
          t.find('hr').each(this._buildDivider.bind(this)),
          t.find('img').each(this._buildImage.bind(this)),
          t.find('h1, h2, h3, h4, h5, h6').each(this._buildHeading.bind(this)),
          t.find('ul, ol').each(this._buildList.bind(this)),
          t.find('li').each(this._buildListItem.bind(this)),
          t.find('pre').each(this._buildPre.bind(this)),
          t.find('blockquote').each(this._buildQuote.bind(this)),
          t.find('p').each(this._buildText.bind(this)),
          t
            .find('.email-wrapper, .email-footer')
            .each(this._buildWrapper.bind(this)),
          t.find('[data-block=layout]').each(this._buildlayout.bind(this)));
        let e = t.children(),
          i = e.length;
        e.each(
          function (t, e) {
            if (i !== e + 1) {
              let e = this.utils.cssToObject(t.attr('style'));
              t.css(
                'margin-bottom',
                e['margin-bottom']
                  ? e['margin-bottom']
                  : this.opts.get('email.spacing')
              );
            }
          }.bind(this)
        );
      }.bind(this)
    )),
      (e = this.utils.replaceRgbToHex(e)),
      t.html(e));
  },
  _buildHeading(t) {
    this._setHeadingStyle(t);
    let e = this.utils.cssToObject(t.attr('style'));
    (t.css({
      'font-family': e['font-family']
        ? e['font-family']
        : this.opts.get('email.font'),
      'margin-top': '0px',
      'margin-bottom': e['margin-bottom']
        ? e['margin-bottom']
        : this._buildMarginBottom(t, e),
    }),
      t.attr('style', t.attr('style') + ' mso-line-height-rule:exactly;'),
      t.removeAttr('data-rx-style-cache'));
  },
  _buildText(t) {
    let e = this.utils.cssToObject(t.attr('style')),
      i = e['font-size']
        ? e['font-size']
        : this.opts.get('email.text.fontSize'),
      s = e['line-height']
        ? e['line-height']
        : this.opts.get('email.text.lineHeight'),
      a = t.closest('.email-wrapper, .email-footer');
    if (0 !== a.length) {
      let t = this.utils.cssToObject(a.attr('style'));
      ((i = t['font-size'] ? t['font-size'] : i),
        (s = t['line-height'] ? t['line-height'] : s));
    }
    let o = Math.ceil(s * parseInt(i)) + 'px';
    (t.css({
      'font-family': e['font-family']
        ? e['font-family']
        : this.opts.get('email.font'),
      'font-size': i,
      'line-height': o,
      'margin-top': '0px',
    }),
      t.attr('style', t.attr('style') + ' mso-line-height-rule:exactly;'));
  },
  _buildLink(t) {
    if (t.hasClass('email-button') || 0 !== t.find('img').length) return;
    let e = this.utils.cssToObject(t.attr('style'));
    (this._setLinkStyle(t),
      t.removeAttr('data-rx-style-cache'),
      t.attr({ target: '_blank' }),
      t.css({
        'text-decoration': e['text-decoration']
          ? e['text-decoration']
          : 'underline',
      }),
      t.addClass('email-link'));
  },
  _buildButton(t) {
    (this._setButtonStyle(t), t.removeAttr('data-rx-style-cache'));
    let e = this.utils.cssToObject(t.attr('style')),
      i = t.parent(),
      s = this.utils.cssToObject(i.attr('style')),
      a = this._createTable();
    (a.css({
      'margin-bottom': s['margin-bottom']
        ? s['margin-bottom']
        : this._buildMarginBottom(t, s),
    }),
      a.addClass(t.attr('class')));
    let o = this.dom('<tr>'),
      l = this.dom('<td>');
    (l.attr({ bgcolor: e.background, align: 'center' }),
      l.css({
        'border-radius': e['border-radius'] ? e['border-radius'] : 0,
        'font-size': e['font-size'],
      }),
      l.attr('style', l.attr('style') + ' mso-padding-alt: ' + e.padding),
      t.attr({ target: '_blank' }),
      t.css({
        display: 'inline-block',
        'padding-left': '0',
        'padding-right': '0',
      }),
      t.attr('style', t.attr('style') + ' mso-line-height-rule:exactly;'));
    let n = t.clone();
    (l.append(n), o.append(l), a.append(o), i.before(a), i.remove());
  },
  _buildDivider(t) {
    (this._setDividerStyle(t), t.removeAttr('data-rx-style-cache'));
    let e = this.utils.cssToObject(t.attr('style')),
      i = this._createTable();
    (i.css({
      'margin-bottom': e['margin-bottom']
        ? e['margin-bottom']
        : this._buildMarginBottom(t, e),
    }),
      i.addClass(t.attr('class')));
    let s = this.dom('<tr>'),
      a = this.dom('<td>');
    (a.css({
      'font-size': '1px',
      margin: 0,
      background: !!e.background && e.background,
      height: !!e.height && e.height,
      'line-height': !!e.height && e.height,
    }),
      a.html('&nbsp;'),
      s.append(a),
      i.append(s),
      t.after(i),
      t.remove());
  },
  _buildImage(t) {
    let e = t.parent(),
      i = e,
      s = !1;
    'A' === e.get().tagName &&
      ((s = e).css({
        'text-decoration': 'none',
        cursor: 'pointer',
        'line-height': '100%',
        'font-size': '0px',
        display: 'block',
      }),
      (i = e.parent()));
    let a = this.utils.cssToObject(i.attr('style')),
      o = a['text-align'] ? a['text-align'] : 'center',
      l = 'center' === o ? '0 auto' : '0',
      n = this._createTable();
    (n.css({
      'margin-bottom': a['margin-bottom']
        ? a['margin-bottom']
        : this._buildMarginBottom(i, a),
    }),
      a.margin && n.css({ margin: a.margin }),
      n.addClass(i.attr('class')));
    let r = this.dom('<tr>'),
      d = this.dom('<td>');
    (d.attr('align', o),
      d.css({
        background: !!a.background && a.background,
        'border-radius': !!a['border-radius'] && a['border-radius'],
        padding: !!a.padding && a.padding,
      }),
      (null != t.attr('width') && '' != t.attr('width')) ||
        t.attr('width', '100%'),
      t.css({
        display: 'block',
        'max-width': '100%',
        height: 'auto',
        margin: l,
        'border-radius': !!a['border-radius'] && a['border-radius'],
        border: a.border ? a.border : 'none',
      }));
    let h = s ? s.clone() : t.clone();
    (d.append(h), r.append(d), n.append(r), i.after(n), i.remove());
  },
  _buildList(t) {
    let e = 'ul' === t.get().tagName.toLowerCase() ? '20px' : '24px',
      i = this.utils.cssToObject(t.attr('style'));
    i.padding && t.css('padding', i.padding);
    let s = parseInt(t.css('padding-left'));
    ((s && '' !== s && 0 !== s) || t.css('padding-left', e),
      t.css({ 'margin-top': '0px' }));
  },
  _buildListItem(t) {
    let e = this.utils.cssToObject(t.attr('style')),
      i = e['font-size']
        ? e['font-size']
        : this.opts.get('email.text.fontSize'),
      s = e['line-height']
        ? e['line-height']
        : this.opts.get('email.text.lineHeight'),
      a = t.closest('.email-wrapper, .email-footer');
    if (0 !== a.length) {
      let t = this.utils.cssToObject(a.attr('style'));
      ((i = t['font-size'] ? t['font-size'] : i),
        (s = t['line-height'] ? t['line-height'] : s));
    }
    let o = Math.ceil(s * parseInt(i)) + 'px';
    (t.css({
      'text-align': 'left',
      'font-family': e['font-family']
        ? e['font-family']
        : this.opts.get('email.font'),
      'font-size': i,
      'line-height': o,
    }),
      t.attr('style', t.attr('style') + ' mso-line-height-rule:exactly;'));
  },
  _buildPre(t) {
    t.find('code').unwrap();
    let e = this.utils.cssToObject(t.attr('style')),
      i = t.text().trim(),
      s = this.dom('<code>');
    (s.html(i),
      t.html(''),
      t.append(s),
      s.css({ 'font-family': this.opts.get('email.pre.fontFamily') }),
      t.css({
        'font-size': e['font-size']
          ? e['font-size']
          : this.opts.get('email.pre.fontSize'),
        'line-height': e['line-height']
          ? e['line-height']
          : this.opts.get('email.pre.lineHeight'),
        'border-radius': e['border-radius']
          ? e['border-radius']
          : this.opts.get('email.pre.borderRaidus'),
        border: e.border ? e.border : this.opts.get('email.pre.border'),
        padding: e.padding ? e.padding : this.opts.get('email.pre.padding'),
        color: e.color ? e.color : this.opts.get('email.pre.color'),
        background: e.background
          ? e.background
          : this.opts.get('email.pre.background'),
        overflow: 'auto',
      }));
  },
  _buildQuote(t) {
    let e = this.utils.cssToObject(t.attr('style'));
    t.css({
      'margin-left': '0',
      'border-left': e['border-left']
        ? e['border-left']
        : this.opts.get('email.quote.borderLeft'),
      padding: e.padding ? e.padding : this.opts.get('email.quote.padding'),
      background: e.background
        ? e.background
        : this.opts.get('email.quote.pre.background'),
      color: e.color ? e.color : this.opts.get('email.quote.pre.color'),
      'font-weight': e['font-weight']
        ? e['font-weight']
        : this.opts.get('email.quote.pre.fontWeight'),
      'font-style': e['font-style']
        ? e['font-style']
        : this.opts.get('email.quote.pre.fontStyle'),
      overflow: 'auto',
    });
  },
  _buildlayout(t) {
    let e = this.utils.cssToObject(t.attr('style')),
      i = this._createTable();
    (i.addClass('email-column-container'),
      i.addClass(t.attr('class')),
      i.css({
        'margin-bottom': e['margin-bottom']
          ? e['margin-bottom']
          : this._buildMarginBottom(t, e),
        background: e.background ? e.background : '',
        padding: e.padding ? e.padding : '',
        border: e.border ? e.border : '',
        'border-radius': e['border-radius'] ? e['border-radius'] : '',
      }));
    let s = this.dom('<tr>'),
      a = t.find('[data-block=column]').filter(function (e) {
        return e.parent().is(t);
      }),
      o = a.length - 1;
    (a.each(
      function (t, e) {
        let i = this.utils.cssToObject(t.attr('style')),
          a = this.dom('<td>'),
          l = i['flex-basis'] ? i['flex-basis'] : i.width;
        (a.addClass('email-column'), a.addClass(t.attr('class')));
        let n = this.dom('<td>');
        (n.html('&nbsp;'),
          n.addClass('email-column-spacer'),
          n.width(this.opts.get('email.gutter')),
          a.hasClass('mobile-hidden') && n.addClass('mobile-hidden'),
          a.attr({ bgcolor: i.background ? i.background : '' }),
          a.css({
            background: i.background ? i.background : '',
            padding: i.padding ? i.padding : '',
            border: i.border ? i.border : '',
            'border-radius': i['border-radius'] ? i['border-radius'] : '',
            width: l,
          }),
          a.append(t.contents()),
          s.append(a),
          o !== e && s.append(n));
      }.bind(this)
    ),
      i.append(s),
      t.after(i),
      t.remove());
  },
  _buildWrapper(t) {
    let e = this.utils.cssToObject(t.attr('style')),
      i = this._createTable();
    (i.addClass(t.attr('class')),
      i.css({
        'margin-bottom': e['margin-bottom']
          ? e['margin-bottom']
          : this._buildMarginBottom(t, e),
      }));
    let s = this.dom('<tr>'),
      a = this.dom('<td>');
    (t.attr('style') && a.attr('style', t.attr('style')),
      a.css({
        background: e.background ? e.background : '',
        padding: e.padding ? e.padding : '',
        border: e.border ? e.border : '',
        'border-radius': e['border-radius'] ? e['border-radius'] : '',
      }),
      e.background && a.attr('bgcolor', e.background),
      a.html(t.html()),
      s.append(a),
      i.append(s),
      t.after(i),
      t.remove());
  },
  _createTable() {
    let t = this.dom('<table>');
    return (
      t.addClass('email-table'),
      t.attr({
        width: '100%',
        cellpadding: '0',
        cellspacing: '0',
        border: '0',
        role: 'presentation',
      }),
      t.css({ 'margin-top': '0px' }),
      t
    );
  },
  _setFrameHeight(t) {
    let e = this.dom(t.get().contentWindow.document).find('body').height();
    t.height(e);
  },
  _setStyle(t, e) {
    let i = this.utils.cssToObject(t.attr('style')),
      s = {};
    for (let [t, a] of Object.entries(e)) {
      i[t.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()] || (s[t] = a);
    }
    t.css(s);
  },
  _cacheStyle(t) {
    let e = 'data-rx-style-cache',
      i = t.attr('style');
    i
      ? ((i = i.replace(/"/g, '')), t.attr(e, i))
      : (i && '' !== i) || t.removeAttr(e);
  },
  _beforeFormat(t) {
    const e = t.get('instances');
    for (let t of Object.values(e)) {
      let e = t.getBlock(),
        i = e.find('p, li');
      (this._clearCssProps(e), this._clearCssProps(i));
    }
  },
  _clearCssProps(t) {
    (t.css({
      'font-size': '',
      'font-weight': '',
      'line-height': '',
      'letter-spacing': '',
      'text-transform': '',
    }),
      t.each(this._cacheStyle.bind(this)));
  },
});
