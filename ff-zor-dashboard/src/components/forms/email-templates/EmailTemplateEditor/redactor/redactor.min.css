:root {
  --rx-font-ui:
    -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Robot<PERSON>,
    'Helvetica Neue', sans-serif;
  --rx-font-ui-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  --rx-rounded-sm: 3px;
  --rx-rounded-md: 6px;
  --rx-rounded-lg: 9px;
  --rx-rounded-xl: 12px;
  --rx-rounded-circle: 99px;
  --rx-level-control: 3;
  --rx-level-toolbar: 5;
  --rx-level-tooltip: 99;
  --rx-level-popup: 100;
  --rx-outset-md: 28px;
  --rx-toolbar-height: 36px;
  --rx-toolbar-padding: 2px;
  --rx-toolbar-button-width: 34px;
  --rx-toolbar-button-height: 34px;
  --rx-toolbar-icon-size: 20px;
  --rx-fg-heading: #070707;
  --rx-fg-text: #2b2b3a;
  --rx-fg-subtle: #63636e;
  --rx-fg-placeholder: #73737c;
  --rx-bg-input: #ffffff;
  --rx-bg-input-shaded: #f7f7f8;
  --rx-bg-input-on: #070707;
  --rx-bg-input-off: rgba(7, 7, 7, 0.15);
  --rx-bg-input-disabled: rgba(7, 7, 7, 0.05);
  --rx-bg-aluminum: #f8f8f8;
  --rx-bg-silver: #e6e6e6;
  --rx-bg-platinum: #cdcdcd;
  --rx-bg-base: #ffffff;
  --rx-bg-raised: #ffffff;
  --rx-bg-overlay: #ffffff;
  --rx-bg-toolbar: rgba(255, 255, 255, 0.97);
  --rx-bg-toolbar-raised: rgba(255, 255, 255, 0.97);
  --rx-bg-context: #070707;
  --rx-bg-context-active: #042c6a;
  --rx-bg-source: #191919;
  --rx-border-divider: rgba(7, 7, 7, 0.1);
  --rx-border-input: rgba(7, 7, 7, 0.2);
  --rx-border-focus: #73a9ff;
  --rx-fg-dark-accent: #070707;
  --rx-fg-dark-heading: #070707;
  --rx-fg-dark-text: rgba(7, 7, 7, 0.8);
  --rx-fg-dark-subtle: rgba(7, 7, 7, 0.6);
  --rx-fg-dark-minimal: rgba(7, 7, 7, 0.5);
  --rx-fg-dark-dimmed: rgba(7, 7, 7, 0.4);
  --rx-bg-dark-dimmed: rgba(7, 7, 7, 0.03);
  --rx-bg-dark-minimal: rgba(7, 7, 7, 0.05);
  --rx-bg-dark-subtle: rgba(7, 7, 7, 0.08);
  --rx-bg-dark-medium: rgba(7, 7, 7, 0.15);
  --rx-bg-dark-strong: rgba(7, 7, 7, 0.25);
  --rx-bg-dark-accent: #070707;
  --rx-bg-dark-accent-hover: rgba(7, 7, 7, 0.8);
  --rx-border-dark-dimmed: rgba(7, 7, 7, 0.05);
  --rx-border-dark-minimal: rgba(7, 7, 7, 0.08);
  --rx-border-dark-subtle: rgba(7, 7, 7, 0.1);
  --rx-border-dark-medium: rgba(7, 7, 7, 0.2);
  --rx-border-dark-accent: #070707;
  --rx-fg-light-accent: #ffffff;
  --rx-fg-light-heading: #ffffff;
  --rx-fg-light-text: rgba(255, 255, 255, 0.8);
  --rx-fg-light-subtle: rgba(255, 255, 255, 0.6);
  --rx-fg-light-minimal: rgba(255, 255, 255, 0.5);
  --rx-fg-light-dimmed: rgba(255, 255, 255, 0.4);
  --rx-bg-light-dimmed: rgba(255, 255, 255, 0.03);
  --rx-bg-light-minimal: rgba(255, 255, 255, 0.05);
  --rx-bg-light-subtle: rgba(255, 255, 255, 0.08);
  --rx-bg-light-medium: rgba(255, 255, 255, 0.15);
  --rx-bg-light-strong: rgba(255, 255, 255, 0.25);
  --rx-bg-light-accent: #ffffff;
  --rx-bg-light-accent-hover: rgba(255, 255, 255, 0.8);
  --rx-border-light-dimmed: rgba(255, 255, 255, 0.05);
  --rx-border-light-minimal: rgba(255, 255, 255, 0.08);
  --rx-border-light-subtle: rgba(255, 255, 255, 0.1);
  --rx-border-light-medium: rgba(255, 255, 255, 0.2);
  --rx-border-light-accent: #ffffff;
  --rx-fg-black-accent: #070707;
  --rx-fg-black-heading: #070707;
  --rx-fg-black-text: rgba(7, 7, 7, 0.8);
  --rx-fg-black-subtle: rgba(7, 7, 7, 0.6);
  --rx-fg-black-minimal: rgba(7, 7, 7, 0.5);
  --rx-fg-black-dimmed: rgba(7, 7, 7, 0.4);
  --rx-bg-black-dimmed: rgba(7, 7, 7, 0.03);
  --rx-bg-black-minimal: rgba(7, 7, 7, 0.05);
  --rx-bg-black-subtle: rgba(7, 7, 7, 0.08);
  --rx-bg-black-medium: rgba(7, 7, 7, 0.15);
  --rx-bg-black-accent: #070707;
  --rx-bg-black-accent-hover: rgba(7, 7, 7, 0.8);
  --rx-border-black-dimmed: rgba(7, 7, 7, 0.05);
  --rx-border-black-minimal: rgba(7, 7, 7, 0.08);
  --rx-border-black-subtle: rgba(7, 7, 7, 0.1);
  --rx-border-black-medium: rgba(7, 7, 7, 0.2);
  --rx-border-black-accent: #070707;
  --rx-fg-white-accent: #ffffff;
  --rx-fg-white-heading: #ffffff;
  --rx-fg-white-text: rgba(255, 255, 255, 0.8);
  --rx-fg-white-subtle: rgba(255, 255, 255, 0.6);
  --rx-fg-white-minimal: rgba(255, 255, 255, 0.5);
  --rx-fg-white-dimmed: rgba(255, 255, 255, 0.4);
  --rx-bg-white-dimmed: rgba(255, 255, 255, 0.03);
  --rx-bg-white-minimal: rgba(255, 255, 255, 0.05);
  --rx-bg-white-subtle: rgba(255, 255, 255, 0.08);
  --rx-bg-white-medium: rgba(255, 255, 255, 0.15);
  --rx-bg-white-accent: #ffffff;
  --rx-bg-white-accent-hover: rgba(255, 255, 255, 0.8);
  --rx-border-white-dimmed: rgba(255, 255, 255, 0.05);
  --rx-border-white-minimal: rgba(255, 255, 255, 0.08);
  --rx-border-white-subtle: rgba(255, 255, 255, 0.1);
  --rx-border-white-medium: rgba(255, 255, 255, 0.2);
  --rx-border-white-accent: #ffffff;
  --rx-fg-primary-accent: #0063ff;
  --rx-fg-primary-strong: #0247b5;
  --rx-fg-primary-text: #043583;
  --rx-bg-primary-minimal: #f2f7ff;
  --rx-bg-primary-subtle: #e5efff;
  --rx-bg-primary-medium: #c9deff;
  --rx-bg-primary-accent: #0063ff;
  --rx-bg-primary-accent-hover: #0247b5;
  --rx-bg-primary-static: #0063ff;
  --rx-border-primary-minimal: #c9deff;
  --rx-border-primary-subtle: #99c1ff;
  --rx-border-primary-medium: #99c1ff;
  --rx-border-primary-accent: #0063ff;
  --rx-border-primary-static: #0063ff;
  --rx-fg-negative-accent: #d70015;
  --rx-fg-negative-strong: #990211;
  --rx-fg-negative-text: #6f040e;
  --rx-bg-negative-minimal: #fff0f1;
  --rx-bg-negative-subtle: #ffe1e5;
  --rx-bg-negative-medium: #ffc1c8;
  --rx-bg-negative-accent: #d70015;
  --rx-bg-negative-accent-hover: #d70015;
  --rx-bg-negative-static: #d70015;
  --rx-border-negative-minimal: #ffc1c8;
  --rx-border-negative-subtle: #ff8994;
  --rx-border-negative-medium: #ff8994;
  --rx-border-negative-acccent: #d70015;
  --rx-border-negative-static: #d70015;
  --rx-link-color: #0063ff;
  --rx-link-hover-color: #070707;
  --rx-link-dark-color: #070707;
  --rx-link-dark-hover-color: rgba(7, 7, 7, 0.6);
  --rx-link-dark-subtle-color: rgba(7, 7, 7, 0.6);
  --rx-link-dark-subtle-hover-color: #070707;
  --rx-shadow-modal:
    0 0 1px rgba(7, 7, 7, 0.25), 0 5px 10px rgba(7, 7, 7, 0.05),
    0 10px 20px rgba(7, 7, 7, 0.05), 0 20px 40px rgba(7, 7, 7, 0.05),
    0 40px 80px rgba(7, 7, 7, 0.05);
  --rx-shadow-dropdown:
    0 0 1px 0 rgba(0, 0, 0, 0.25), 0 1px 1px -0.5px rgba(0, 0, 0, 0.07),
    0 3px 3px -1.5px rgba(0, 0, 0, 0.07), 0 6px 6px -3px rgba(0, 0, 0, 0.07),
    0 12px 12px -6px rgba(0, 0, 0, 0.07);
  --rx-shadow-toolbar-raised:
    0 1px 3px rgba(7, 7, 7, 0.15), 0 1px 2px rgba(7, 7, 7, 0.06);
  --rx-shadow-reorder: 0 15px 30px rgba(7, 7, 7, 0.3);
  --rx-shadow-inner: inset 0 1px 1px rgba(7, 7, 7, 0.15);
  --rx-shadow-sm: 0 1px 3px rgba(7, 7, 7, 0.15);
  --rx-shadow-md:
    0 1px 2px rgba(7, 7, 7, 0.05), 0 3px 6px rgba(7, 7, 7, 0.05),
    0 2px 4px rgba(7, 7, 7, 0.05), 0 10px 20px rgba(7, 7, 7, 0.05);
  --rx-shadow-lg:
    0 5px 10px rgba(7, 7, 7, 0.05), 0 10px 20px rgba(7, 7, 7, 0.05),
    0 20px 40px rgba(7, 7, 7, 0.05), 0 40px 80px rgba(7, 7, 7, 0.05);
  --rx-shadow-sm-border:
    0 0 1px rgba(7, 7, 7, 0.25), 0 1px 3px rgba(7, 7, 7, 0.15);
  --rx-shadow-md-border:
    0 0 1px rgba(7, 7, 7, 0.25), 0 1px 2px rgba(7, 7, 7, 0.05),
    0 3px 6px rgba(7, 7, 7, 0.05), 0 2px 4px rgba(7, 7, 7, 0.05),
    0 10px 20px rgba(7, 7, 7, 0.05);
  --rx-shadow-lg-border:
    0 0 1px rgba(7, 7, 7, 0.25), 0 5px 10px rgba(7, 7, 7, 0.05),
    0 10px 20px rgba(7, 7, 7, 0.05), 0 20px 40px rgba(7, 7, 7, 0.05),
    0 40px 80px rgba(7, 7, 7, 0.05);
}
[rx-data-theme='dark'] {
  --rx-fg-heading: #d8d8db;
  --rx-fg-text: #c8c8cc;
  --rx-fg-subtle: #73737c;
  --rx-fg-placeholder: #73737c;
  --rx-bg-input: #141414;
  --rx-bg-input-shaded: #141414;
  --rx-bg-input-on: #73a9ff;
  --rx-bg-input-off: rgba(255, 255, 255, 0.2);
  --rx-bg-input-disabled: rgba(255, 255, 255, 0.05);
  --rx-bg-aluminum: #191919;
  --rx-bg-silver: #1e1e1e;
  --rx-bg-platinum: #2f2f2f;
  --rx-bg-base: #141414;
  --rx-bg-raised: #191919;
  --rx-bg-overlay: #191919;
  --rx-bg-toolbar: rgba(25, 25, 25, 0.97);
  --rx-bg-toolbar-raised: rgba(25, 25, 25, 0.97);
  --rx-bg-context-active: #c9deff;
  --rx-border-divider: rgba(255, 255, 255, 0.1);
  --rx-border-input: rgba(255, 255, 255, 0.2);
  --rx-border-focus: #73a9ff;
  --rx-fg-dark-accent: #ffffff;
  --rx-fg-dark-heading: rgba(255, 255, 255, 0.9);
  --rx-fg-dark-text: rgba(255, 255, 255, 0.8);
  --rx-fg-dark-subtle: rgba(255, 255, 255, 0.6);
  --rx-fg-dark-minimal: rgba(255, 255, 255, 0.5);
  --rx-fg-dark-dimmed: rgba(255, 255, 255, 0.4);
  --rx-bg-dark-dimmed: rgba(255, 255, 255, 0.03);
  --rx-bg-dark-minimal: rgba(255, 255, 255, 0.05);
  --rx-bg-dark-subtle: rgba(255, 255, 255, 0.08);
  --rx-bg-dark-medium: rgba(255, 255, 255, 0.15);
  --rx-bg-dark-strong: rgba(255, 255, 255, 0.25);
  --rx-bg-dark-accent: #f7f7f8;
  --rx-bg-dark-accent-hover: rgba(255, 255, 255, 0.8);
  --rx-border-dark-dimmed: rgba(255, 255, 255, 0.05);
  --rx-border-dark-minimal: rgba(255, 255, 255, 0.08);
  --rx-border-dark-subtle: rgba(255, 255, 255, 0.1);
  --rx-border-dark-medium: rgba(255, 255, 255, 0.2);
  --rx-border-dark-accent: #f7f7f8;
  --rx-fg-light-accent: #070707;
  --rx-fg-light-heading: rgba(7, 7, 7, 0.9);
  --rx-fg-light-text: rgba(7, 7, 7, 0.8);
  --rx-fg-light-subtle: rgba(7, 7, 7, 0.6);
  --rx-fg-light-minimal: rgba(7, 7, 7, 0.5);
  --rx-fg-light-dimmed: rgba(7, 7, 7, 0.4);
  --rx-bg-light-dimmed: rgba(7, 7, 7, 0.03);
  --rx-bg-light-minimal: rgba(7, 7, 7, 0.05);
  --rx-bg-light-subtle: rgba(7, 7, 7, 0.08);
  --rx-bg-light-medium: rgba(7, 7, 7, 0.15);
  --rx-bg-light-strong: rgba(7, 7, 7, 0.25);
  --rx-bg-light-accent: #070707;
  --rx-bg-light-accent-hover: rgba(7, 7, 7, 0.8);
  --rx-border-light-dimmed: rgba(7, 7, 7, 0.05);
  --rx-border-light-minimal: rgba(7, 7, 7, 0.08);
  --rx-border-light-subtle: rgba(7, 7, 7, 0.1);
  --rx-border-light-medium: rgba(7, 7, 7, 0.2);
  --rx-border-light-accent: #070707;
  --rx-fg-primary-accent: #73a9ff;
  --rx-fg-primary-strong: #99c1ff;
  --rx-fg-primary-text: #c9deff;
  --rx-bg-primary-minimal: #070d18;
  --rx-bg-primary-subtle: #061939;
  --rx-bg-primary-medium: #042c6a;
  --rx-bg-primary-accent: #73a9ff;
  --rx-bg-primary-accent-hover: #99c1ff;
  --rx-bg-primary-static: #0063ff;
  --rx-border-primary-minimal: #042c6a;
  --rx-border-primary-subtle: #043583;
  --rx-border-primary-medium: #043583;
  --rx-border-primary-accent: #73a9ff;
  --rx-border-primary-static: #0063ff;
  --rx-fg-negative-accent: #ff5d6c;
  --rx-fg-negative-strong: #ff8994;
  --rx-fg-negative-text: #ffc1c8;
  --rx-bg-negative-minimal: #160708;
  --rx-bg-negative-subtle: #31060a;
  --rx-bg-negative-medium: #5a040d;
  --rx-bg-negative-accent: #ff5d6c;
  --rx-bg-negative-accent-hover: #ff8994;
  --rx-bg-negative-static: #d70015;
  --rx-border-negative-minimal: #5a040d;
  --rx-border-negative-subtle: #6f040e;
  --rx-border-negative-medium: #6f040e;
  --rx-border-negative-accent: #ff5d6c;
  --rx-border-negative-static: #d70015;
  --rx-link-color: #73a9ff;
  --rx-link-hover-color: #ffffff;
  --rx-link-dark-color: #ffffff;
  --rx-link-dark-hover-color: rgba(255, 255, 255, 0.6);
  --rx-link-dark-subtle-color: rgba(255, 255, 255, 0.6);
  --rx-link-dark-subtle-hover-color: #ffffff;
  --rx-shadow-modal:
    0 0 1px rgba(255, 255, 255, 0.5), 0 5px 10px rgba(7, 7, 7, 0.05),
    0 10px 20px rgba(7, 7, 7, 0.05), 0 20px 40px rgba(7, 7, 7, 0.05),
    0 40px 80px rgba(7, 7, 7, 0.05);
  --rx-shadow-dropdown:
    0 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px -0.5px rgba(0, 0, 0, 0.07),
    0 3px 3px -1.5px rgba(0, 0, 0, 0.07), 0 6px 6px -3px rgba(0, 0, 0, 0.07),
    0 12px 12px -6px rgba(0, 0, 0, 0.07);
  --rx-shadow-sm-border:
    0 0 1px rgba(255, 255, 255, 0.5), 0 1px 3px rgba(7, 7, 7, 0.15);
  --rx-shadow-md-border:
    0 0 1px rgba(255, 255, 255, 0.5), 0 1px 2px rgba(7, 7, 7, 0.05),
    0 3px 6px rgba(7, 7, 7, 0.05), 0 2px 4px rgba(7, 7, 7, 0.05),
    0 10px 20px rgba(7, 7, 7, 0.05);
  --rx-shadow-lg-border:
    0 0 1px rgba(255, 255, 255, 0.5), 0 5px 10px rgba(7, 7, 7, 0.05),
    0 10px 20px rgba(7, 7, 7, 0.05), 0 20px 40px rgba(7, 7, 7, 0.05),
    0 40px 80px rgba(7, 7, 7, 0.05);
}
@keyframes slideUp {
  to {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
  }
}
@keyframes slideDown {
  from {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.rx-container {
  position: relative;
  box-sizing: border-box;
}
.rx-container.rx-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  overflow-x: auto;
}
.rx-main-container {
  border-radius: var(--rx-rounded-sm);
  background-color: var(--rx-bg-base);
  border: 1px solid var(--rx-border-dark-minimal);
}
.rx-main-wym {
  background-color: var(--rx-bg-aluminum);
}
.rx-stop-scrolling {
  height: 100%;
  overflow: hidden;
}
.rx-editor-frame {
  width: 100%;
  height: 0;
  border: none;
  background-color: var(--rx-bg-base);
  margin-left: auto;
  margin-right: auto;
  display: block;
}
.rx-editor-container {
  box-sizing: border-box;
}
.rx-editor-disabled {
  opacity: 0.4;
}
.rx-editor-disabled img {
  filter: grayscale(1);
}
.rx-editor-overlay {
  width: 100%;
  height: 100%;
  content: '';
  position: absolute;
  z-index: 1;
  background: 0 0;
  top: 0;
  left: 0;
}
.rx-toolbox-container {
  background: var(--rx-bg-toolbar);
  z-index: var(--rx-level-toolbar);
}
.rx-editor-breakline address + *,
.rx-editor-breakline blockquote + *,
.rx-editor-breakline dl + *,
.rx-editor-breakline figure + *,
.rx-editor-breakline ol + *,
.rx-editor-breakline p + *,
.rx-editor-breakline pre + *,
.rx-editor-breakline table + *,
.rx-editor-breakline ul + *,
.rx-editor-breakline > div + * {
  margin-top: 0 !important;
}
.rx-editor-breakline h1 + h2,
.rx-editor-breakline h2 + h3,
.rx-editor-breakline h3 + h4,
.rx-editor-breakline h4 + h5,
.rx-editor-breakline h5 + h6 {
  margin-top: 0 !important;
}
.rx-editor *,
.rx-editor ::after,
.rx-editor ::before {
  box-sizing: inherit;
}
.rx-editor {
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}
.rx-editor [dir='rtl'] {
  text-align: right;
}
.rx-editor [dir='ltr'] {
  text-align: left;
}
.rx-editor,
.rx-editor [data-rx-type],
.rx-editor a,
.rx-editor figcaption,
.rx-editor p,
.rx-editor td,
.rx-editor th {
  outline: 0;
}
.rx-editor [data-rx-tag='br'],
.rx-editor [data-rx-tag='tbr'] {
  margin-top: 0;
}
.rx-editor [data-rx-tag='br']:empty,
.rx-editor [data-rx-tag='tbr']:empty,
.rx-editor [data-rx-type='text']:empty,
.rx-editor [data-rx-type='todoitem'] div:empty {
  min-width: 1em;
  min-height: 1.5em;
}
.rx-editor::after {
  content: '';
  clear: both;
  display: table;
}
.rx-editor [data-rx-type='embed'],
.rx-editor [data-rx-type='image'] {
  position: relative;
}
.rx-editor [data-rx-type='embed'] img,
.rx-editor [data-rx-type='image'] img {
  vertical-align: middle;
}
.rx-editor figure > a img,
.rx-editor figure > div,
.rx-editor figure > iframe,
.rx-editor figure > img,
.rx-editor figure > pre {
  vertical-align: middle;
}
.rx-editor [data-rx-focusable].rx-block-focus {
  outline: 2px solid var(--rx-border-focus) !important;
}
.rx-editor .rx-block-control-focus {
  outline: 1px solid var(--rx-border-focus) !important;
  outline-offset: 1px;
}
.rx-editor [data-rx-type='wrapper']:empty {
  outline: 1px dashed var(--rx-border-dark-medium);
  outline-offset: 1px;
}
.rx-editor [data-rx-type='heading'] a {
  text-decoration: underline;
}
.rx-editor .rx-layout-grid {
  display: flex;
  flex-direction: row;
  gap: 24px;
}
.rx-editor [data-rx-type='column'] {
  outline: 1px dashed var(--rx-border-dark-medium);
}
.rx-editor.rx-editor-email [data-rx-type='column'] {
  outline: 0;
}
.rx-editor.rx-editor-email [data-rx-type='column']:has(p:empty),
.rx-editor.rx-editor-email [data-rx-type='column']:hover {
  outline: 1px dashed var(--rx-border-dark-medium);
}
.rx-editor .rx-nowrap {
  white-space: nowrap;
}
.rx-editor [data-rx-type='pre'] {
  position: relative;
}
.rx-editor [data-rx-type='embed'] {
  position: relative;
}
.rx-editor [data-rx-type='embed']:before {
  width: 100%;
  height: 100%;
  content: '';
  position: absolute;
  z-index: 1;
}
.rx-editor [data-rx-type='embed'] iframe {
  display: inline-block;
}
.rx-editor [data-rx-type='embed'] figcaption {
  position: relative;
  top: 0;
  z-index: 2;
}
.rx-editor [data-rx-type='embed'].rx-block-focus:before {
  display: none;
}
.rx-editor .rx-embed-placeholder {
  text-align: center;
  border: 1px dashed var(--rx-border-dark-medium);
  border-radius: var(--rx-rounded-md);
  padding: 40px 20px;
  font-size: 14px;
  color: var(--rx-fg-placeholder);
}
.rx-editor [data-rx-type='todo'] {
  --rx-todo-size: 20px;
  --rx-todo-mark-color: var(--rx-fg-light-accent);
  --rx-todo-border-color: var(--rx-border-input);
  --rx-todo-bg: var(--rx-bg-input);
  --rx-todo-checked-border-color: var(--rx-border-dark-accent);
  --rx-todo-checked-bg: var(--rx-bg-dark-accent);
  margin-left: 0;
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.rx-editor [data-rx-type='todoitem'] {
  display: flex;
  gap: 6px;
  cursor: text;
}
.rx-editor [data-rx-type='todoitem'] input {
  position: relative;
  top: 1px;
  appearance: none;
  display: inline-block;
  padding: 0;
  box-shadow: none;
  width: var(--rx-todo-size);
  height: var(--rx-todo-size);
  cursor: pointer;
}
.rx-editor [data-rx-type='todoitem'] input:before {
  content: '';
  display: block;
  width: var(--rx-todo-size);
  height: var(--rx-todo-size);
  border: 1px solid var(--rx-todo-border-color);
  border-radius: 6px;
  background: var(--rx-todo-bg);
}
.rx-editor [data-rx-type='todoitem'] input:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: calc(var(--rx-todo-size) / 3.5);
  height: calc(var(--rx-todo-size) / 1.8);
  background-color: transparent;
  transform: translate(-50%, -85%) scale(0) rotate(45deg);
}
.rx-editor [data-rx-type='todoitem'] input:checked:before {
  border-color: var(--rx-todo-checked-border-color);
  background-color: var(--rx-todo-checked-bg);
  box-shadow: var(--rx-todo-checked-shadow);
}
.rx-editor [data-rx-type='todoitem'] input:checked:after {
  transform: translate(-50%, -85%) scale(1) rotate(45deg);
  box-shadow: 2px 2px 0 0 var(--rx-todo-mark-color);
}
.rx-editor [data-rx-type='todoitem'] div {
  outline: 0;
  line-height: 1.4;
}
.rx-editor [data-rx-type='mergetag'] {
  font-family: var(--rx-font-ui-mono);
  font-size: 85%;
  line-height: 1;
  display: inline-block;
  cursor: pointer;
  padding: 4px 6px;
  color: var(--rx-fg-primary-text);
  background: var(--rx-bg-primary-subtle);
  border: 1px solid var(--rx-border-primary-subtle);
  border-radius: 4px;
}
.rx-editor [data-rx-type='mergetag'].rx-block-focus,
.rx-editor [data-rx-type='mergetag']:hover {
  background: var(--rx-bg-primary-medium);
}
.rx-editor .rx-block-placeholder {
  position: relative;
}
.rx-editor .rx-block-placeholder:before {
  position: absolute;
}
.rx-editor .rx-block-placeholder:before,
.rx-editor [data-placeholder]:empty:before {
  content: attr(data-placeholder);
  display: inline-block;
  color: var(--rx-fg-placeholder);
  font-weight: 400;
  font-style: italic;
  font-size: inherit;
  cursor: text;
  max-height: 20px;
}
.rx-editor [data-placeholder]:empty:focus:before {
  content: '';
}
.rx-editor.rx-placeholder:before {
  position: absolute;
  z-index: 0;
  content: attr(placeholder);
  color: var(--rx-fg-placeholder);
  font-weight: 400;
  cursor: text;
}
.rx-in-blur .rx-editor [data-rx-focusable].rx-block-focus {
  outline-color: var(--rx-border-dark-medium) !important;
}
.rx-draggable-placeholder {
  height: 20px;
  min-width: 80px;
  outline: 3px dashed gold !important;
  background: rgba(255, 215, 0, 0.2) !important;
  margin-bottom: 20px;
}
.rx-toolbar-container {
  display: flex;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.rx-toolbar-container::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.rx-toolbar-container:empty {
  display: none;
}
.rx-sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}
.rx-sticky-on {
  border-bottom: 1px solid var(--rx-border-dark-minimal);
}
.rx-raised {
  margin-left: -1px;
  margin-right: -1px;
  margin-top: -1px;
  margin-bottom: -1px;
  background: var(--rx-bg-toolbar-raised);
  box-shadow: var(--rx-shadow-toolbar-raised);
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.rx-toolbox-external.rx-raised {
  margin: 0;
}
.rx-raised.rx-sticky-on {
  border-bottom: none;
}
.rx-extrabar-buttons,
.rx-toolbar-buttons {
  display: flex;
  white-space: nowrap;
  align-items: center;
  gap: 1px;
}
.rx-extrabar,
.rx-toolbar {
  padding: var(--rx-toolbar-padding);
  height: var(--rx-toolbar-height);
}
.rx-toolbar {
  order: 1;
  flex: 1;
}
.rx-extrabar {
  order: 2;
}
.rx-extrabar .rx-button,
.rx-toolbar .rx-button {
  justify-content: center;
  width: var(--rx-toolbar-button-width);
  height: var(--rx-toolbar-button-height);
}
.rx-extrabar .rx-button-icon,
.rx-toolbar .rx-button-icon {
  width: var(--rx-toolbar-button-width);
  min-width: var(--rx-toolbar-button-width);
  height: var(--rx-toolbar-button-height);
}
.rx-extrabar .rx-button-icon svg,
.rx-toolbar .rx-button-icon svg {
  width: var(--rx-toolbar-icon-size);
  height: var(--rx-toolbar-icon-size);
}
.rx-extrabar .rx-button-title,
.rx-toolbar .rx-button-title {
  display: none;
}
.rx-pathbar {
  font-family: var(--rx-font-ui);
  margin: 0;
  padding: 0 8px;
  position: relative;
  overflow: hidden;
  background: var(--rx-bg-toolbar);
  box-sizing: border-box;
  border: none;
  display: flex;
  gap: 12px;
  border-bottom: 1px solid var(--rx-border-dark-dimmed);
}
.rx-pathbar:empty {
  display: none;
}
.rx-pathbar.disable .rx-pathbar-item {
  opacity: 0.5;
}
.rx-pathbar-item {
  position: relative;
  font-size: 12px;
  padding: 6px 0;
  margin: 0;
  line-height: 16px;
  color: var(--rx-fg-dark-minimal);
  cursor: pointer;
}
.rx-pathbar-item:hover {
  color: var(--rx-fg-dark-accent);
  text-decoration: underline;
}
.rx-pathbar-item.active,
.rx-pathbar-item.active:hover {
  color: var(--rx-fg-dark-accent);
  text-decoration: none;
  cursor: text;
}
.rx-pathbar-item:after {
  position: absolute;
  content: '-';
  width: 8px;
  padding-left: 3px;
  font-size: 12px;
  line-height: 16px;
  color: var(--rx-fg-dark-minimal);
}
.rx-pathbar-item:last-child:after {
  display: none;
}
.rx-statusbar {
  font-family: var(--rx-font-ui-mono);
  margin: 0;
  position: relative;
  overflow: hidden;
  background: var(--rx-bg-base);
  box-sizing: border-box;
  border: none;
  display: flex;
  align-items: center;
  gap: 10px;
}
.rx-statusbar-end,
.rx-statusbar-start {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 10px;
}
.rx-statusbar-end:empty,
.rx-statusbar-start:empty {
  display: none;
}
.rx-statusbar-start {
  flex: auto;
}
.rx-statusbar.disable .rx-statusbar-item {
  opacity: 0.5;
}
.rx-statusbar-item {
  font-size: 12px;
  padding: 0;
  margin: 0;
  line-height: 16px;
  color: var(--rx-fg-text);
}
.rx-statusbar-item:after {
  content: '';
  display: inline-block;
  position: relative;
  top: 2px;
  width: 1px;
  height: 12px;
  margin-left: 10px;
  background: var(--rx-border-divider);
}
.rx-statusbar-item:last-child:after {
  display: none;
}
.rx-statusbar-item a {
  color: var(--rx-link-dark-color);
  text-decoration: underline;
}
.rx-statusbar-item a:hover {
  color: var(--rx-link-dark-hover-color);
  text-decoration: underline;
}
.rx-fullscreen .rx-statusbar {
  position: fixed;
  bottom: 0;
  width: 100%;
}
.rx-control {
  position: absolute;
  top: 0;
  left: 0;
  font-family: var(--rx-font-text);
  z-index: var(--rx-level-control);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  width: 28px;
  line-height: 0;
}
.rx-control-buttons {
  display: flex;
  gap: 1;
  flex-direction: column;
  flex-wrap: wrap;
}
.rx-control .rx-button {
  justify-content: center;
  background: var(--rx-bg-raised);
}
.rx-control .rx-button-title {
  display: none;
}
.rx-control .rx-button-icon svg {
  width: 14px;
  height: 14px;
}
.rx-context {
  font-family: var(--rx-font-text);
  position: absolute;
  top: 0;
  left: 0;
  z-index: var(--rx-level-toolbar);
  border-radius: var(--rx-rounded-lg);
  padding: 4px 6px;
  margin: 0;
  line-height: 1;
  background: var(--rx-bg-context);
  box-shadow: var(--rx-shadow-sm-border);
  max-width: 380px;
}
.rx-context-line {
  font-size: 13px;
  font-weight: 400;
  line-height: 1;
  padding: 6px;
  padding-bottom: 8px;
  border-radius: var(--rx-rounded-sm);
  display: flex;
  align-items: center;
  vertical-align: baseline;
}
.rx-context-line,
.rx-context-line a {
  color: var(--rx-fg-white-text);
}
.rx-context-line a:hover {
  color: var(--rx-fg-white-subtle);
}
.rx-context-buttons {
  display: flex;
  align-items: center;
  gap: 1px;
  flex-wrap: wrap;
}
.rx-context .rx-button {
  justify-content: center;
}
.rx-context .rx-button.active,
.rx-context .rx-button.pressed,
.rx-context .rx-button:hover {
  background: var(--rx-bg-white-medium);
}
.rx-context .rx-button.active .rx-button-icon svg,
.rx-context .rx-button.pressed .rx-button-icon svg,
.rx-context .rx-button:hover .rx-button-icon svg {
  fill: var(--rx-fg-white-text);
}
.rx-context .rx-button.disabled .rx-button-icon svg {
  fill: var(--rx-fg-white-dimmed);
}
.rx-context .rx-button-icon {
  border-radius: var(--rx-rounded-md);
}
.rx-context .rx-button-icon svg {
  fill: var(--rx-fg-white-text);
  width: 18px;
  height: 18px;
}
.rx-context .rx-button-title {
  display: none;
}
.rx-button {
  font-family: var(--rx-font-text);
  display: flex;
  align-items: center;
  vertical-align: middle;
  text-decoration: none;
  background: 0 0;
  outline: 0;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-size: 0;
  line-height: 1;
  gap: 8px;
  border-radius: var(--rx-rounded-sm);
  width: 28px;
  height: 28px;
  transition: none;
  cursor: pointer;
}
.rx-button.pressed {
  background: var(--rx-bg-silver);
}
.rx-button.pressed .rx-button-icon svg {
  fill: var(--rx-fg-dark-accent);
}
.rx-button:hover {
  text-decoration: none;
}
.rx-button:hover {
  background: var(--rx-bg-silver);
}
.rx-button:hover .rx-button-icon svg {
  fill: var(--rx-fg-dark-accent);
}
.rx-button.active {
  background: var(--rx-bg-silver);
}
.rx-button.disabled {
  cursor: default;
  background-color: transparent !important;
}
.rx-button.disabled .rx-button-icon svg {
  fill: var(--rx-fg-dark-dimmed);
}
.rx-button-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  vertical-align: middle;
  border-radius: var(--rx-rounded-sm);
  width: 28px;
  min-width: 28px;
  height: 28px;
}
.rx-button-icon:empty {
  display: none;
}
.rx-button-icon svg {
  fill: var(--rx-fg-dark-text);
  width: 16px;
  height: 16px;
}
.rx-button-title {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  color: var(--rx-fg-dark-text);
}
.rx-tooltip {
  position: absolute;
  z-index: 99;
  margin: 0;
  padding: 4px 7px;
  border-radius: var(--rx-rounded-sm);
  line-height: 1;
  font-family: var(--rx-font-ui-mono);
  font-size: 12px;
  font-weight: 500;
  color: var(--rx-fg-white-text);
  background: var(--rx-bg-black-accent);
  pointer-events: none;
}
.rx-source-container {
  display: none;
}
.rx-source,
.rx-source:focus,
.rx-source:hover {
  text-align: left;
  box-sizing: border-box;
  font-family: var(--rx-font-ui-mono);
  width: 100%;
  display: block;
  margin: 0;
  border: none;
  box-shadow: none;
  border-radius: 0;
  background-color: var(--rx-bg-source);
  color: var(--rx-fg-white-text);
  font-size: 14px;
  line-height: 1.7;
  outline: 0;
  padding: 10px 18px 20px 18px;
  min-height: 60px;
  resize: vertical;
}
.rx-drag-active {
  outline: 3px dashed gold !important;
  outline-offset: 0 !important;
  position: relative;
  max-height: 40px;
  overflow: hidden;
  padding: 0;
}
.rx-drag-active:before {
  width: 100%;
  height: 100%;
  content: '';
  top: 0;
  left: 0;
  background: rgba(255, 215, 0, 0.4);
  position: absolute;
  z-index: 1;
}
.rx-dragging {
  opacity: 0.95;
  padding: 8px;
  background: var(--rx-bg-body);
  box-shadow: var(--rx-shadow-reorder);
}
.rx-dragging img {
  max-width: 100%;
}
.rx-dropdown {
  position: absolute;
  font-family: var(--rx-font-ui);
  border-radius: var(--rx-rounded-md);
  background: var(--rx-bg-overlay);
  box-shadow: var(--rx-shadow-dropdown);
  position: absolute;
  top: 0;
  left: 0;
  font-size: 14px;
  line-height: 1.4;
  z-index: var(--rx-level-popup);
  overflow-y: auto;
}
.rx-dropdown-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--rx-bg-dark-accent);
  padding: 8px;
}
.rx-dropdown-title:empty {
  display: none;
}
.rx-dropdown-items {
  padding: 4px;
}
.rx-dropdown-tabs {
  display: flex;
  background: var(--rx-bg-aluminum);
}
.rx-dropdown-tab {
  padding: 4px 8px;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.rx-dropdown-tab.active,
.rx-dropdown-tab:hover {
  background: var(--rx-bg-light-accent);
}
.rx-dropdown-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 292px;
  padding: 10px;
}
.rx-dropdown-images img {
  border-radius: 6px;
  vertical-align: top;
  min-width: 85px;
  max-width: 85px;
  min-height: 56px;
  max-height: 56px;
  object-fit: fill;
  height: 100%;
  width: min-content;
  cursor: pointer;
}
.rx-dropdown-images img:hover {
  outline: 3px solid var(--rx-border-focus);
}
.rx-option-list {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
  font-size: 14px;
}
.rx-option-item {
  display: flex;
  align-items: center;
  padding: 0.5em 0.75em;
  cursor: pointer;
  outline: 0;
  position: relative;
  border-radius: var(--rx-rounded-sm);
}
.rx-option-item.passive,
.rx-option-item.passive .rx-option-label {
  cursor: default;
}
.rx-option-item:not(.passive):hover {
  background: var(--rx-bg-silver);
}
.rx-option-item.active,
.rx-option-item:has(input:checked) {
  background: var(--rx-bg-silver);
  font-weight: 500;
}
.rx-option-item.active .rx-option-check,
.rx-option-item:has(input:checked) .rx-option-check {
  display: inline-block;
}
.rx-option-item.disabled,
.rx-option-item.disabled:hover {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  background: 0 0;
}
.rx-option-header {
  font-size: 12px;
  font-weight: 600;
  color: var(--rx-fg-dark-accent);
  padding: 0.5em 0.75em;
  pointer-events: none;
}
.rx-option-label {
  --gap: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
  gap: var(--gap);
  color: var(--rx-fg-dark-accent);
}
a.rx-option-label,
a.rx-option-label:hover {
  color: var(--rx-fg-text);
  text-decoration: none;
}
.rx-option-item.focus,
.rx-option-item.focus .option-label,
.rx-option-item.focus a.option-label,
.rx-option-item.focus a.option-label:hover,
.rx-option-item.focus:hover,
.rx-option-item.focus:hover .option-label,
.rx-option-item.focus:hover a.option-label,
.rx-option-item.focus:hover a.option-label:hover {
  background: var(--rx-bg-silver);
}
.rx-option-label.danger,
a.rx-option-label.danger,
a.rx-option-label.danger:hover {
  color: var(--rx-fg-negative-accent);
}
.rx-option-label.danger svg,
a.rx-option-label.danger svg,
a.rx-option-label.danger:hover svg {
  fill: var(--rx-fg-negative-accent);
}
.rx-option-text {
  flex: 1;
}
.rx-option-text-xlarge {
  font-size: 21px;
}
.rx-option-check {
  color: var(--rx-fg-dark-accent);
  font-weight: 700;
  display: none;
}
.rx-option-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--rx-fg-dark-accent);
}
.rx-option-icon svg {
  height: 16px;
  width: 16px;
  fill: var(--rx-fg-dark-accent);
}
.rx-option-icon:empty,
.rx-option-text:empty {
  display: none;
}
.rx-option-shortcut {
  font-weight: 400;
  font-size: 13px;
  color: var(--rx-fg-subtle);
  margin-left: 12px;
}
.rx-dropdown-grid-container .rx-option-list,
.rx-dropdown-type-grid .rx-option-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  box-sizing: border-box;
}
.rx-dropdown-grid-container .rx-option-item,
.rx-dropdown-type-grid .rx-option-item {
  padding: 0;
}
.rx-dropdown-grid-container .rx-option-label,
.rx-dropdown-type-grid .rx-option-label {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  justify-content: center;
  align-items: center;
}
.rx-colorpicker {
  position: absolute;
  font-family: var(--rx-font-ui);
  border-radius: var(--rx-rounded-md);
  background: var(--rx-bg-overlay);
  box-shadow: var(--rx-shadow-dropdown);
  position: absolute;
  top: 0;
  left: 0;
  font-size: 14px;
  line-height: 1.4;
  z-index: var(--rx-level-popup);
  overflow-y: auto;
  padding: 4px;
}
.rx-swatches {
  display: flex;
  flex-wrap: nowrap;
}
.rx-swatches-wrap {
  flex-wrap: wrap;
}
.rx-swatches-colors {
  display: flex;
  flex-direction: column;
}
.rx-swatches-colors-row {
  flex-direction: row;
}
.rx-swatch {
  cursor: pointer;
  position: relative;
  font-size: 0;
  width: 20px;
  height: 20px;
  border: 1px solid transparent;
}
.rx-swatch.active {
  border: 2px solid var(--rx-border-dark-accent);
  box-shadow: 0 0 0 2px var(--rx-border-light-accent);
  z-index: 2;
}
.rx-swatch-size-large {
  width: 32px;
  height: 32px;
}
.rx-color-contrast {
  border-color: var(--rx-border-dark-subtle);
}
.rx-colorpicker-tabs {
  font-size: 13px;
  line-height: 1;
  font-weight: 500;
  display: flex;
  gap: 2px;
  border-radius: var(--rx-rounded-sm);
  background: var(--rx-bg-dark-minimal);
  padding: 2px;
  margin-bottom: 4px;
}
.rx-colorpicker-tab {
  flex-grow: 1;
  border-radius: var(--rx-rounded-sm);
  padding: 6px 8px;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  color: var(--rx-fg-dark-subtle);
}
.rx-colorpicker-tab.active,
.rx-colorpicker-tab:hover {
  text-decoration: none;
  background: var(--rx-bg-dark-subtle);
  color: var(--rx-fg-dark-text);
}
.rx-colorpicker-tab.active {
  cursor: text;
  text-decoration: none;
  background: var(--rx-bg-light-accent);
  color: var(--rx-fg-dark-text);
  box-shadow: var(--shadow-sm-border);
}
.rx-button-dropdown {
  position: relative;
  padding: 8px 10px;
  height: auto;
  width: 100%;
  border-radius: var(--rx-rounded-md);
}
.rx-form {
  padding: 10px;
}
.rx-form-item {
  padding-bottom: 12px;
}
.rx-form-item:empty {
  display: none;
}
.rx-form-item:last-child {
  padding-bottom: 0;
}
.rx-form-box {
  padding: 16px;
}
.rx-form-image img,
.rx-form-image svg {
  max-width: 100%;
}
.rx-form-section,
.rx-form-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--rx-bg-dark-accent);
  padding-bottom: 4px;
}
.rx-form-section:empty,
.rx-form-title:empty {
  display: none;
}
.rx-form-section:not(:first-child) {
  padding-top: 8px;
}
.rx-form-label {
  box-sizing: border-box;
  font-family: var(--rx-font-ui);
  font-size: 13px;
  line-height: 1.4;
  font-weight: 500;
  display: block;
  padding: 0;
  margin: 0;
  padding-bottom: 6px;
  color: var(--rx-fg-dark-text);
}
.rx-form-hint {
  font-size: 13px;
  font-weight: 400;
  color: var(--rx-fg-dark-subtle);
  margin-left: 2px;
}
.rx-form-flex {
  display: flex;
  gap: 8px;
  align-items: center;
  padding-bottom: 12px;
}
.rx-form-flex .rx-form-item {
  padding: 0;
}
.rx-form-flex .rx-form-input,
.rx-form-flex .rx-form-select {
  flex: auto;
}
.rx-form-flex:last-child {
  padding-bottom: 0;
}
.rx-form-color-container {
  position: relative;
}
.rx-form-color-container .rx-form-input {
  padding-left: 40px;
}
.rx-form-color-toggle {
  position: absolute;
  top: 4px;
  left: 6px;
  height: 24px;
  width: 24px;
  border-radius: 8px;
  box-shadow: var(--rx-shadow-inner);
  cursor: pointer;
  background: var(--rx-bg-white-accent);
}
.rx-form-button {
  box-sizing: border-box;
  font-family: var(--rx-font-ui);
  font-size: 13px;
  font-weight: 400;
  outline: 0;
  border-radius: 6px;
  padding: 6px 10px;
  min-height: 32px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  line-height: 1.25;
  height: auto;
  background: 0 0;
  color: var(--rx-fg-dark-accent);
  border: 1px solid var(--rx-border-dark-medium);
}
.rx-form-button:hover {
  background: var(--rx-bg-dark-subtle);
}
.rx-form-button-primary {
  background: var(--rx-bg-dark-accent);
  color: var(--rx-fg-light-accent);
  border-color: transparent;
}
.rx-form-button-primary:hover {
  color: var(--rx-fg-light-accent);
  background: var(--rx-bg-dark-accent-hover);
}
.rx-form-button-danger {
  border: 1px solid var(--rx-border-negative-medium);
  color: var(--rx-fg-negative-accent);
}
.rx-form-button-danger:hover {
  color: var(--rx-fg-negative-accent);
  background: var(--rx-bg-negative-subtle);
}
.rx-form-button-fullwidth {
  display: block;
  width: 100%;
}
.rx-form-button-push-right {
  margin-left: auto;
}
.rx-form-input,
.rx-form-select,
.rx-form-textarea {
  box-sizing: border-box;
  display: block;
  width: 100%;
  font-weight: 400;
  padding: 4px 8px;
  height: 32px;
  font-family: var(--rx-font-ui);
  font-size: 14px;
  outline: 0;
  border-radius: 4px;
  box-shadow: none;
  line-height: 1.3;
  color: var(--rx-fg-dark-text);
  background: var(--rx-bg-input-shaded);
  border: 1px solid var(--rx-border-input);
}
.rx-form-input:focus,
.rx-form-select:focus,
.rx-form-textarea:focus {
  border-color: var(--rx-border-focus);
  box-shadow: 0 0 0 1px var(--rx-border-focus);
}
.rx-form-textarea {
  height: auto;
  resize: vertical;
  line-height: 1.5;
}
.rx-form-select {
  font-size: 15px;
  cursor: pointer;
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg height="6" viewBox="0 0 10 6" width="10" xmlns="http://www.w3.org/2000/svg"><path fill="rgb(0,0,0)" opacity=".6" d="m6.6168815 3-4.44908109-4.09883609c-.22373388-.20615371-.22373388-.54039492 0-.74654863s.58647818-.20615371.81021206 0l4.85418712 4.47211041c.22373388.20615371.22373388.54039491 0 .74654862l-4.85418712 4.47211041c-.22373388.20615371-.58647818.20615371-.81021206 0s-.22373388-.54039492 0-.74654863z" fill-rule="evenodd" transform="matrix(0 1 -1 0 8 -2)"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.65em center;
  padding-right: 28px;
  min-height: 32px;
}
[rx-data-theme='dark'] .rx-form-select {
  background-image: url('data:image/svg+xml;utf8,<svg height="6" viewBox="0 0 10 6" width="10" xmlns="http://www.w3.org/2000/svg"><path fill="rgb(255,255,255)" opacity="0.6" d="m6.6168815 3-4.44908109-4.09883609c-.22373388-.20615371-.22373388-.54039492 0-.74654863s.58647818-.20615371.81021206 0l4.85418712 4.47211041c.22373388.20615371.22373388.54039491 0 .74654862l-4.85418712 4.47211041c-.22373388.20615371-.58647818.20615371-.81021206 0s-.22373388-.54039492 0-.74654863z" fill-rule="evenodd" transform="matrix(0 1 -1 0 8 -2)"/></svg>');
}
.rx-form-checkbox {
  appearance: auto;
  box-sizing: border-box;
  vertical-align: middle;
  margin: 0 !important;
  margin-right: 4px !important;
}
.rx-form-checkbox-label {
  color: var(--rx-fg-dark-text);
  font-family: var(--rx-font-ui);
  font-size: 14px;
  line-height: 1.4;
}
.rx-form-upload-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0;
  border: 1px dashed var(--rx-border-dark-medium);
  background: var(--rx-bg-dark-dimmed);
  border-radius: var(--rx-rounded-md);
  min-width: 160px;
  height: 160px;
  cursor: pointer;
  position: relative;
}
.rx-form-upload-box input[type='file'] {
  font-size: 13px;
}
.rx-form-upload-box img {
  position: relative;
  z-index: 2;
  object-fit: cover;
  height: 100%;
  width: 100%;
}
.rx-form-upload-box:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.rx-form-upload-box.rx-form-upload-hover {
  border-color: var(--rx-border-dark-medium);
  background-color: var(--rx-bg-dark-subtle);
}
.rx-form-upload-box.rx-form-upload-error {
  border-color: var(--rx-border-negative-medium);
  background-color: var(--rx-bg-negative-minimal);
}
.rx-upload-remove {
  position: absolute;
  z-index: 3;
  top: -6px;
  right: -10px;
  background-color: var(--rx-bg-black-accent);
  color: var(--rx-fg-white-accent);
  opacity: 1;
  width: 20px;
  height: 20px;
  text-align: center;
  border-radius: 10px;
  font-size: 14px;
  line-height: 20px;
}
.rx-upload-remove:after {
  content: '×';
}
.rx-upload-remove:hover {
  background-color: var(--rx-bg-black-accent-hover);
}
.rx-form-upload-cover-off img {
  object-fit: initial;
  height: auto;
  width: auto;
}
.rx-form-upload-placeholder {
  font-family: var(--rx-font-ui);
  color: var(--rx-fg-dark-minimal);
  font-size: 12px;
  line-height: 1.35;
  padding: 0 8px;
  text-align: center;
}
.rx-form-upload-placeholder svg {
  fill: var(--rx-fg-dark-minimal);
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.rx-editor-progress {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 11000;
  width: 100%;
  background-color: var(--rx-bg-dark-subtle);
}
.rx-editor-progress span {
  animation: progress-bar-stripes 2s linear infinite;
  content: '';
  display: block;
  min-height: 8px;
  width: 100%;
  height: 100%;
  background-color: var(--rx-bg-primary-accent);
  background-image: -webkit-linear-gradient(
    45deg,
    var(--rx-bg-light-strong) 25%,
    transparent 25%,
    transparent 50%,
    var(--rx-bg-light-strong) 50%,
    var(--rx-bg-light-strong) 75%,
    transparent 75%,
    transparent
  );
  background-image: linear-gradient(
    45deg,
    var(--rx-bg-light-strong) 25%,
    transparent 25%,
    transparent 50%,
    var(--rx-bg-light-strong) 50%,
    var(--rx-bg-light-strong) 75%,
    transparent 75%,
    transparent
  );
  background-size: 40px 40px;
}
.rx-ai-main {
  padding: 12px;
  border-radius: 8px;
  background: var(--rx-bg-raised);
  border: 1px solid var(--rx-border-dark-subtle);
}
.rx-ai-footer {
  margin-top: 16px !important;
  display: flex;
  gap: 8px;
}
.rx-ai-buttons {
  margin-left: auto !important;
  display: flex;
  gap: 8px;
}
.rx-ai-label,
.rx-ai-preview-label {
  box-sizing: border-box;
  font-family: var(--rx-font-ui);
  font-size: 13px;
  line-height: 1.4;
  font-weight: 500;
  display: block;
  padding: 0;
  margin: 0;
  padding-bottom: 6px;
  color: var(--rx-fg-dark-subtle);
}
.rx-ai-select,
.rx-ai-size {
  width: auto;
  max-width: 320px;
}
.rx-ai-progress {
  margin-bottom: 12px !important;
}
.rx-ai-progress:empty {
  display: none;
}
.rx-ai-progress svg {
  fill: var(--rx-fg-dark-accent);
}
.rx-ai-preview {
  font-family: var(--rx-font-ui);
  font-weight: 400;
  letter-spacing: normal;
  text-transform: none;
  font-size: 16px;
  line-height: 1.618;
  color: var(--rx-fg-dark-text);
  margin-bottom: 12px !important;
}
.rx-ai-preview:empty {
  display: none;
}
@media only screen and (max-width: 767px) {
  .rx-ai-buttons,
  .rx-ai-footer {
    flex-direction: column;
    width: 100%;
  }
  .rx-ai-select,
  .rx-ai-size {
    max-width: 100%;
  }
  .rx-ai-button {
    justify-content: center;
  }
}
.rx-voice-label {
  display: none;
}
.rx-visually-hidden {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
.rx-hidden {
  display: none !important;
}
.rx-code-lang-select {
  position: absolute;
  top: 2px;
  right: 4px;
  z-index: 3;
}
.rx-collapsed {
  position: relative;
}
.rx-collapsed:before {
  content: '›';
  position: absolute;
  left: -12px;
  line-height: 1;
  top: 50%;
  margin-top: -7px;
  font-size: 14px;
}
.rx-toc {
  margin-bottom: 24px;
}
.rx-empty address:empty,
.rx-empty blockquote:empty,
.rx-empty dd:empty,
.rx-empty dt:empty,
.rx-empty figcaption:empty,
.rx-empty h1:empty,
.rx-empty h2:empty,
.rx-empty h3:empty,
.rx-empty h4:empty,
.rx-empty h5:empty,
.rx-empty h6:empty,
.rx-empty li:empty,
.rx-empty p:empty {
  min-height: 1.5em;
}
.rx-empty pre:empty {
  min-height: 3.5em;
}
.rx-empty b:empty,
.rx-empty cite:empty,
.rx-empty code:empty,
.rx-empty del:empty,
.rx-empty em:empty,
.rx-empty i:empty,
.rx-empty ins:empty,
.rx-empty span:empty,
.rx-empty strong:empty,
.rx-empty sub:empty,
.rx-empty sup:empty,
.rx-empty u:empty {
  display: inline-block;
  min-width: 1px;
  min-height: 1em;
}
.rx-empty td:empty:after,
.rx-empty th:empty:after {
  content: '​';
}
.rx-empty code:after,
.rx-empty kbd:after,
.rx-empty mark:after {
  content: '​';
}
.rx-empty pre code:after {
  display: none;
}
.rx-empty code + code {
  margin-left: 2px;
}
.rx-empty table {
  empty-cells: show;
}
.rx-empty embed,
.rx-empty img,
.rx-empty object {
  max-width: 100%;
  height: auto;
}
.rx-content {
  text-align: left;
  --rx-font-text:
    -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto,
    'Helvetica Neue', sans-serif;
  --rx-font-heading: inherit;
  --rx-font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  --rx-body-color: var(--rx-bg-body);
  --rx-heading-color: var(--rx-fg-heading);
  --rx-text-color: var(--rx-fg-text);
  --rx-table-border: var(--rx-border-dark-minimal);
  --rx-line-color: var(--rx-border-dark-minimal);
  --rx-pre-color: var(--rx-fg-text);
  --rx-pre-bg: var(--rx-bg-dark-minimal);
  --rx-var-color: var(--rx-fg-text);
  --rx-kbd-color: var(--rx-fg-text);
  --rx-kbd-border: var(--rx-border-dark-subtle);
  --rx-kbd-bg: var(--rx-bg-light-accent);
  --rx-code-color: var(--rx-fg-text);
  --rx-code-bg: var(--rx-bg-dark-medium);
  --rx-abbr-border: var(--rx-border-dark-accent);
  --rx-quote-border-color: var(--rx-border-dark-accent);
  background: var(--rx-body-color);
  font-family: var(--rx-font-text);
  color: var(--rx-text-color);
  font-size: 16px;
  line-height: 1.5;
}
.rx-content[dir='rtl'] {
  text-align: right;
}
.rx-content * {
  margin: 0;
}
.rx-content address,
.rx-content dd,
.rx-content dt,
.rx-content li,
.rx-content p {
  font-size: 16px;
  line-height: 1.5;
}
.rx-content h1 + *,
.rx-content h2 + *,
.rx-content h3 + *,
.rx-content h4 + *,
.rx-content h5 + *,
.rx-content h6 + * {
  margin-top: 12px;
}
.rx-content address + h2,
.rx-content address + h3,
.rx-content address + h4,
.rx-content address + h5,
.rx-content address + h6,
.rx-content dl + h2,
.rx-content dl + h3,
.rx-content dl + h4,
.rx-content dl + h5,
.rx-content dl + h6,
.rx-content ol + h2,
.rx-content ol + h3,
.rx-content ol + h4,
.rx-content ol + h5,
.rx-content ol + h6,
.rx-content p + h2,
.rx-content p + h3,
.rx-content p + h4,
.rx-content p + h5,
.rx-content p + h6,
.rx-content pre + h2,
.rx-content pre + h3,
.rx-content pre + h4,
.rx-content pre + h5,
.rx-content pre + h6,
.rx-content ul + h2,
.rx-content ul + h3,
.rx-content ul + h4,
.rx-content ul + h5,
.rx-content ul + h6 {
  margin-top: 24px;
}
.rx-content address + *,
.rx-content dl + *,
.rx-content ol + *,
.rx-content p + *,
.rx-content pre + *,
.rx-content ul + * {
  margin-top: 24px;
}
.rx-content blockquote + *,
.rx-content figure + *,
.rx-content table + *,
.rx-content > div + * {
  margin-top: 24px;
}
.rx-content h1 + h2,
.rx-content h2 + h3,
.rx-content h3 + h4,
.rx-content h4 + h5,
.rx-content h5 + h6 {
  margin-top: 12px;
}
.rx-content a,
.rx-content a:focus,
.rx-content a:hover,
.rx-content a:visited {
  color: var(--rx-link-color);
}
.rx-content h1,
.rx-content h2,
.rx-content h3,
.rx-content h4,
.rx-content h5,
.rx-content h6 {
  font-family: var(--rx-font-heading);
  font-weight: 700;
  font-style: normal;
  color: var(--rx-heading-color);
  text-rendering: optimizeLegibility;
  letter-spacing: 0;
}
.rx-content h1 a,
.rx-content h2 a,
.rx-content h3 a,
.rx-content h4 a,
.rx-content h5 a,
.rx-content h6 a {
  text-decoration: underline;
}
.rx-content h1 {
  font-weight: 800;
  letter-spacing: -0.02em;
}
.rx-content h2 {
  font-weight: 700;
  letter-spacing: -0.02em;
}
.rx-content h3,
.rx-content h4,
.rx-content h5,
.rx-content h6 {
  font-weight: 600;
  letter-spacing: -0.01em;
}
.rx-content h1 {
  font-size: 36px;
  line-height: 1.25;
}
.rx-content h2 {
  font-size: 24px;
  line-height: 1.25;
}
.rx-content h3 {
  font-size: 20px;
  line-height: 1.25;
}
.rx-content h4 {
  font-size: 16px;
  line-height: 1.25;
}
.rx-content h5 {
  font-size: 16px;
  line-height: 1.25;
}
.rx-content h6 {
  font-size: 16px;
  line-height: 1.25;
}
.rx-content abbr,
.rx-content dfn {
  font-size: 95%;
}
.rx-content cite,
.rx-content code,
.rx-content kbd,
.rx-content small,
.rx-content var {
  font-size: 85%;
}
.rx-content sub,
.rx-content sup {
  font-size: 65%;
}
.rx-content b,
.rx-content strong {
  font-weight: 700;
}
.rx-content em,
.rx-content i {
  font-style: italic;
}
.rx-content del,
.rx-content s,
.rx-content strike {
  text-decoration: line-through;
}
.rx-content u {
  text-decoration: underline;
}
.rx-content code,
.rx-content kbd,
.rx-content var {
  font-family: var(--rx-font-mono);
  font-style: normal;
  line-height: 1;
  vertical-align: baseline;
}
.rx-content code,
.rx-content kbd {
  border-radius: 4px;
}
.rx-content kbd {
  color: var(--rx-kbd-color);
  background: var(--rx-kbd-bg);
  border: 1px solid var(--rx-kbd-border);
  padding: 0.2em 0.4em;
}
.rx-content var {
  color: var(--rx-var-color);
}
.rx-content code {
  position: relative;
  top: -1px;
  padding: 0.2em 0.4em 0.2em;
  color: var(--rx-code-color);
  background: var(--rx-code-bg);
  border: 1px solid var(--rx-code-border);
}
.rx-content mark {
  background-color: #ffea80;
  color: #000;
}
.rx-content b,
.rx-content strong {
  font-weight: 700;
}
.rx-content abbr[title],
.rx-content dfn {
  letter-spacing: 0.01em;
  text-transform: uppercase;
  text-decoration: none;
  border-bottom: 1px dotted var(--rx-abbr-border);
  cursor: help;
}
.rx-content cite {
  color: var(--rx-cite-color, var(--rx-text-color));
  font-style: italic;
}
.rx-content sub,
.rx-content sup {
  line-height: 1;
  margin-left: 2px;
}
.rx-content sub {
  vertical-align: sub;
}
.rx-content sup {
  vertical-align: super;
}
.rx-content ul li {
  list-style: disc;
}
.rx-content ol li {
  list-style: decimal;
}
.rx-content ol > li > ol li {
  list-style-type: lower-alpha;
}
.rx-content ol > li > ol > li > ol li {
  list-style-type: lower-roman;
}
.rx-content ol,
.rx-content ul {
  padding-left: 0;
  margin-left: 20px;
}
.rx-content li li {
  font-size: 1em;
}
.rx-content dt {
  font-weight: 700;
}
.rx-content dd + dt {
  margin-top: 0.25em;
}
.rx-content pre {
  overflow: auto;
  white-space: pre;
  font-family: var(--rx-font-mono);
  font-size: 14px;
  line-height: 1.6;
  padding: 1em;
  border-radius: 3px;
  border: 1px solid var(--rx-pre-border, transparent);
  background: var(--rx-pre-bg, transparent);
  color: var(--rx-pre-color, var(--rx-text-color));
}
.rx-content pre code {
  position: initial;
  color: inherit;
  white-space: pre;
  padding: 0;
  border: 0;
  font-size: 100%;
  display: block;
  line-height: inherit;
  background: 0 0;
}
.rx-content blockquote,
.rx-content figure:has(blockquote) {
  padding: 0;
  background: var(--rx-quote-bg, transparent);
  padding-left: 24px;
  border-left: var(--rx-quote-border-width, 3px) solid
    var(--rx-quote-border-color);
}
.rx-content blockquote p,
.rx-content figure:has(blockquote) p {
  font-style: italic;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.4;
  color: var(--rx-quote-color, var(--rx-text-color));
}
.rx-content blockquote p + p,
.rx-content figure:has(blockquote) p + p {
  margin-top: 0.5em;
}
.rx-content figure:has(blockquote) blockquote {
  padding: 0;
  border: none;
  box-shadow: none;
  background: 0 0;
  border-radius: 0;
}
.rx-content blockquote cite,
.rx-content figcaption cite {
  font-style: italic;
  font-weight: 400;
  color: var(--rx-quote-caption-color, var(--rx-text-color));
  font-size: 14px;
  line-height: 1.3;
}
.rx-content caption {
  text-align: left;
  font-style: normal;
}
.rx-content table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  empty-cells: show;
  font-size: 15px;
  line-height: 1.5;
  color: var(--rx-table-color, var(--rx-text-color));
}
.rx-content td,
.rx-content th {
  text-align: left;
  vertical-align: top;
  padding: 0.75em;
  border: 1px solid var(--rx-table-border);
}
.rx-content th {
  font-weight: 400;
}
.rx-content thead th {
  font-weight: 700;
}
.rx-content address {
  font-style: italic;
}
.rx-content figcaption {
  display: block;
  margin-top: 4px;
  font-size: 14px;
  line-height: 1.3;
  background: var(--rx-caption-bg, transparent);
  color: var(--rx-caption-color, var(--rx-text-color));
}
.rx-content figure > div,
.rx-content figure > iframe,
.rx-content figure > img,
.rx-content figure > img a,
.rx-content figure > pre {
  vertical-align: middle;
}
.rx-content .embed-responsive:has(iframe) {
  position: relative;
  padding: 0;
  padding-bottom: 56.25%;
  height: 0;
}
.rx-content .embed-responsive:has(iframe) iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.rx-content div.embed-responsive {
  margin: 0;
}
.rx-content hr {
  --rx-line-size: 1px;
  --rx-line-width: 100%;
  --rx-line-style: solid;
  position: relative;
  line-height: 1;
  background: 0 0;
  border: none !important;
  text-align: left;
  padding-top: 1.5em;
  padding-bottom: 1.5em;
  margin: 0 !important;
  opacity: 1;
  width: auto;
}
.rx-content hr:after {
  content: '';
  position: absolute;
  top: 50%;
  width: var(--rx-line-width);
  max-width: var(--rx-line-width);
  margin-top: -calc(var(--rx-line-size) / 2);
  border-top-width: var(--rx-line-size);
  border-top-style: var(--rx-line-style);
  border-top-color: var(--rx-line-color);
}
.rx-content .wrap-center {
  text-align: center;
}
.rx-content .wrap-center img {
  margin-left: auto;
  margin-right: auto;
}
.rx-content .wrap-center figcaption {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}
.rx-content .float-left {
  float: left;
  margin-right: 1em;
  margin-bottom: 1em;
  max-width: 200px;
}
.rx-content .float-right {
  float: right;
  margin-left: 1em;
  margin-bottom: 1em;
  max-width: 200px;
}
.rx-content .outset-right {
  position: relative;
  width: calc(100% + var(--rx-outset-md));
  max-width: calc(100% + var(--rx-outset-md));
  transform: translateX(var(--rx-outset-md)) translate3d(0, 0, 0);
  left: calc(var(--rx-outset-md) * -1);
}
.rx-content .outset-left {
  width: calc(100% + var(--rx-outset-md));
  max-width: calc(100% + var(--rx-outset-md));
  transform: translateX(calc(var(--rx-outset-md) * -1)) translate3d(0, 0, 0);
}
.rx-content .outset-both {
  position: relative;
  width: calc(100% + var(--rx-outset-md) + var(--rx-outset-md));
  max-width: calc(100% + var(--rx-outset-md) + var(--rx-outset-md));
  transform: translateX(-50%) translate3d(0, 0, 0);
  left: 50%;
}
.rx-editor-wym address,
.rx-editor-wym dl,
.rx-editor-wym h1,
.rx-editor-wym h2,
.rx-editor-wym h3,
.rx-editor-wym h4,
.rx-editor-wym h5,
.rx-editor-wym h6,
.rx-editor-wym ol:not([data-structure='false']),
.rx-editor-wym p,
.rx-editor-wym ul:not([data-structure='false']) {
  position: relative;
  background: var(--rx-bg-base);
  padding: 8px 12px;
}
.rx-editor-wym address:after,
.rx-editor-wym dl:after,
.rx-editor-wym h1:after,
.rx-editor-wym h2:after,
.rx-editor-wym h3:after,
.rx-editor-wym h4:after,
.rx-editor-wym h5:after,
.rx-editor-wym h6:after,
.rx-editor-wym ol:not([data-structure='false']):after,
.rx-editor-wym p:after,
.rx-editor-wym ul:not([data-structure='false']):after {
  position: absolute;
  font-size: 11px;
  font-weight: 400;
  top: 2px;
  right: 4px;
  cursor: pointer;
  color: var(--rx-fg-placeholder);
}
.rx-editor-wym address:empty::before,
.rx-editor-wym dl:empty::before,
.rx-editor-wym h1:empty::before,
.rx-editor-wym h2:empty::before,
.rx-editor-wym h3:empty::before,
.rx-editor-wym h4:empty::before,
.rx-editor-wym h5:empty::before,
.rx-editor-wym h6:empty::before,
.rx-editor-wym ol:not([data-structure='false']):empty::before,
.rx-editor-wym p:empty::before,
.rx-editor-wym ul:not([data-structure='false']):empty::before {
  content: '​';
  display: inline-block;
}
.rx-editor-wym pre {
  position: relative;
}
.rx-editor-wym pre:after {
  position: absolute;
  font-size: 11px;
  font-weight: 400;
  top: 2px;
  right: 4px;
  cursor: pointer;
  color: var(--rx-fg-placeholder);
}
.rx-editor-wym ol:not([data-structure='false']) ol,
.rx-editor-wym ul:not([data-structure='false']) ul {
  outline: 1px solid var(--rx-border-divider) !important;
}
.rx-editor-wym ol,
.rx-editor-wym ul:not([data-rx-type='todo']) {
  margin-left: 0;
  padding-left: 24px;
  margin-bottom: 3px;
}
.rx-editor-wym ol:not([data-structure='false']):after {
  content: 'ol';
}
.rx-editor-wym ul:not([data-structure='false']):after {
  content: 'ul';
}
.rx-editor-wym dl:after {
  content: 'dl';
}
.rx-editor-wym h1:after {
  content: 'h1';
}
.rx-editor-wym h2:after {
  content: 'h2';
}
.rx-editor-wym h3:after {
  content: 'h3';
}
.rx-editor-wym h4:after {
  content: 'h4';
}
.rx-editor-wym h5:after {
  content: 'h5';
}
.rx-editor-wym h6:after {
  content: 'h6';
}
.rx-editor-wym p:after {
  content: 'p';
}
.rx-editor-wym pre:after {
  content: 'pre';
}
.rx-editor-wym address:after {
  content: 'address';
}
