import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import {
  EmailTemplateFormSchema,
  EmailTemplateFormValues,
} from '@/components/forms/schemas/email-templates/EmailTemplateFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormSwitch from '@/components/global/form-inputs/FormSwitch';
import { Button } from '@/components/ui/button';
import { Form, FormLabel } from '@/components/ui/form';
import { useUpdateEmailTemplate } from '@/hooks/email-templates/useUpdateEmailTemplate';
import { cn } from '@/lib/ui/utils';

import EmailTemplateEditor from './EmailTemplateEditor/EmailTemplateEditor';

/**
 * Email template form component
 * @param props - Component props
 * @returns JSX.Element
 */
const EmailTemplateForm = ({
  emailTemplate,
}: {
  emailTemplate: EmailTemplateFormValues;
}): JSX.Element => {
  const navigate = useNavigate();
  const form = useForm<EmailTemplateFormValues>({
    resolver: zodResolver(EmailTemplateFormSchema),
    defaultValues: {
      ...emailTemplate,
    },
  });

  const {
    mutate: updateEmailTemplate,
    isLoading: updateEmailTemplateIsLoading,
    isSuccess: updateEmailTemplateIsSuccess,
  } = useUpdateEmailTemplate();

  const handleSubmit = (values: EmailTemplateFormValues) => {
    updateEmailTemplate({
      id: emailTemplate.id,
      payload: values,
    });
  };

  const handleCancel = () => {
    navigate('/dashboard/settings?tab=email-settings');
  };

  useEffect(() => {
    if (updateEmailTemplateIsSuccess) {
      navigate('/dashboard/settings?tab=email-settings');
    }
  }, [updateEmailTemplateIsSuccess]);

  return (
    <Form {...form}>
      <form
        className="relative space-y-2"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            'flex-row'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            General
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <FormInput
              type="text"
              name="name"
              label="Name"
              control={form.control}
            />
          </div>
        </div>
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            'flex-row'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Mailing Info
          </p>
          <div className="flex flex-1 flex-col gap-6">
            <div className="grid flex-1 grid-cols-2 gap-6">
              <FormInput
                type="text"
                name="template_subject"
                label="Subject"
                control={form.control}
              />
            </div>
            <div className="grid flex-1 grid-cols-1 gap-4">
              <FormLabel>Body</FormLabel>
              <EmailTemplateEditor
                content={emailTemplate.template_content || ''}
                onContentChange={(content, html) => {
                  form.setValue('template_content', content);
                  form.setValue('template_html', html);
                }}
              />
            </div>
          </div>
        </div>
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            'flex-row'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Active
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <FormSwitch
              name="active"
              control={form.control}
              className="w-full md:w-fit"
              custom_label=" "
            />
          </div>
        </div>

        <div className="flex justify-end">
          <div className="flex w-full gap-2 md:w-fit">
            <Button
              size="sm"
              type="button"
              variant="outline"
              className="w-full md:w-fit"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              type="submit"
              className="w-full md:w-fit"
              disabled={
                form.formState.isSubmitting || updateEmailTemplateIsLoading
              }
            >
              {form.formState.isSubmitting ||
                (updateEmailTemplateIsLoading && (
                  <Loader2 className="size-4 animate-spin" />
                ))}
              Save
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default EmailTemplateForm;
