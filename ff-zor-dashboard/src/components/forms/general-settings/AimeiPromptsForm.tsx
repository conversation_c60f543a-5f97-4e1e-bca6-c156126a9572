import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2, Plus, Trash2 } from 'lucide-react';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import {
  AimeiPromptsFormSchema,
  AimeiPromptsFormValues,
} from '@/components/forms/schemas/settings/AimeiPromptsFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useCreateAimeiPrompts } from '@/hooks/aimei-prompts/useCreateAimeiPrompts';
import { useGetAimeiPropmts } from '@/hooks/aimei-prompts/useGetAimeiPrompts';
import { toast } from '@/hooks/ui/use-toast';
import { formatToTwoDigits } from '@/lib/string';
import useZorAimeiPromptsStore from '@/stores/useZorAimeiPromptsStore';

interface AimeiPromptsFormProps {
  /** Edit mode identifier */
  isEdit: string | null;
  /** Function to update edit mode */
  setIsEdit: React.Dispatch<React.SetStateAction<string | null>>;
}

/**
 * AimeiPromptsForm component for managing Aimei prompts
 * Allows adding, editing, and removing prompts with validation
 */
const AimeiPromptsForm = ({ isEdit, setIsEdit }: AimeiPromptsFormProps) => {
  const { aimeiPrompts, setAimeiPrompts } = useZorAimeiPromptsStore();

  const form = useForm<AimeiPromptsFormValues>({
    resolver: zodResolver(AimeiPromptsFormSchema),
    defaultValues: {
      prompts: aimeiPrompts || [],
    },
  });

  const {
    result: getAimeiPromptsResult,
    isError: isGetAimeiPromptsError,
    isSuccess: isGetAimeiPromptsSuccess,
    isLoading: isGetAimeiPromptsLoading,
    isRefetching: isGetAimeiPromptsRefetching,
  } = useGetAimeiPropmts();

  const {
    mutate: createAimeiPrompts,
    result: createAimeiPromptsResult,
    isError: isCreateAimeiPromptsError,
    isLoading: isCreateAimeiPromptsLoading,
    isSuccess: isCreateAimeiPromptsSuccess,
  } = useCreateAimeiPrompts();

  const { fields, append, remove } = useFieldArray({
    name: 'prompts',
    control: form.control,
  });

  const isEditing = isEdit === 'aimei-prompts';
  const showRefetchingLoader = !isEdit && isGetAimeiPromptsRefetching;

  const resetForms = () => {
    form.reset({
      prompts: [...(aimeiPrompts || [])],
    });
  };

  const handleCancel = (): void => {
    resetForms();
    setIsEdit(null);
  };

  const handleRemove = (index: number): void => {
    remove(index);
  };

  const handleRemoveClick = (index: number) => (): void => {
    handleRemove(index);
  };

  const handleAddPrompt = (): void => {
    append({ title: '', prompt: '' });
  };

  const handleSubmit = (values: AimeiPromptsFormValues): void => {
    createAimeiPrompts(values);
  };

  useEffect(() => {
    resetForms();
  }, [aimeiPrompts]);

  useEffect(() => {
    const hasError = isGetAimeiPromptsError || isCreateAimeiPromptsError;
    if (hasError) {
      toast({
        title: 'Error',
        description: 'Failed to load Aimei prompts. Please try again later.',
        variant: 'destructive',
      });
    }
  }, [isGetAimeiPromptsError, isCreateAimeiPromptsError]);

  useEffect(() => {
    if (!isEdit && isGetAimeiPromptsSuccess && getAimeiPromptsResult?.data) {
      setAimeiPrompts([...getAimeiPromptsResult.data]);
    }
  }, [getAimeiPromptsResult, isGetAimeiPromptsSuccess, setAimeiPrompts]);

  useEffect(() => {
    if (isCreateAimeiPromptsSuccess && createAimeiPromptsResult?.data) {
      setAimeiPrompts([...createAimeiPromptsResult.data]);
      setIsEdit(null);
    }
  }, [createAimeiPromptsResult, isCreateAimeiPromptsSuccess, setAimeiPrompts]);

  return (
    <Form {...form}>
      <form className="space-y-2" onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="rounded-xl border border-border bg-white p-6">
          <div className="flex items-start justify-between gap-6">
            <div className="flex w-60 items-center gap-2">
              <p className="as-title-03 as-strong text-zinc-700">
                Ask Aimei Prompts
              </p>
              {showRefetchingLoader && (
                <Loader2 className="size-4 animate-spin" />
              )}
            </div>
            {!isGetAimeiPromptsLoading && (
              <div className="grid w-full flex-1 gap-4">
                <div className="space-y-6">
                  {fields.length > 0 &&
                    fields.map((field, index) => (
                      <div
                        key={field.id}
                        className="relative grid flex-1 gap-2"
                      >
                        {isEditing && (
                          <Trash2
                            className="hover:text-destructive/90 absolute right-2 top-2 h-4 w-4 cursor-pointer text-destructive"
                            aria-label="Remove prompt"
                            onClick={handleRemoveClick(index)}
                          />
                        )}
                        <FormInput
                          name={`prompts.${index}.title`}
                          label={`${formatToTwoDigits(index + 1)} Prompt Title`}
                          control={form.control}
                          disabled={!isEditing}
                        />
                        <FormTextArea
                          name={`prompts.${index}.prompt`}
                          label={`${formatToTwoDigits(index + 1)} Prompt Text`}
                          control={form.control}
                          disabled={!isEditing}
                        />
                      </div>
                    ))}
                </div>
                {isEditing && (
                  <div className="flex w-full justify-end">
                    <Button
                      size="sm"
                      type="button"
                      className="w-full text-right md:w-fit"
                      onClick={handleAddPrompt}
                    >
                      Add <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          {isGetAimeiPromptsLoading && (
            <div className="flex w-full items-center justify-center p-4">
              <Loader2 className="size-4 animate-spin" />
            </div>
          )}
          {fields.length === 0 && !isGetAimeiPromptsLoading && (
            <div className="flex h-[100px] w-full items-center justify-center text-muted-foreground">
              No prompts found
            </div>
          )}
        </div>
        {isEditing && (
          <div className="flex justify-end">
            <div className="flex w-full gap-2 md:w-fit">
              <Button
                size="sm"
                type="button"
                variant="outline"
                className="w-full md:w-fit"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                type="submit"
                disabled={isCreateAimeiPromptsLoading}
                className="w-full md:w-fit"
              >
                {isCreateAimeiPromptsLoading && (
                  <Loader2 className="size-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </div>
          </div>
        )}
      </form>
    </Form>
  );
};

export default AimeiPromptsForm;
