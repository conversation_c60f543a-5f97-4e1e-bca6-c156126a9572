import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { Link } from 'react-router-dom';

import { Switch } from '@/components/ui/switch';
import { useGetEmailTemplates } from '@/hooks/email-templates/useGetAllEmailTemplates';
import { useUpdateEmailTemplate } from '@/hooks/email-templates/useUpdateEmailTemplate';
import { toast } from '@/hooks/ui/use-toast';
import { EmailTemplate } from '@/types/email-templates/email.template.type';

/**
 * General settings form component
 * @param props - Component props
 * @returns JSX.Element
 */
const EmailSettingsForm = (): JSX.Element => {
  const {
    result: getEmailTemplatesResult,
    isLoading: getEmailTemplatesIsLoading,
    refetch: getEmailTemplatesRefetch,
  } = useGetEmailTemplates();

  if (getEmailTemplatesIsLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    <div>
      <div className="relative space-y-4">
        {[
          {
            title: 'Franchisee Emails',
            description:
              'Manage email notifications for sending to franchisees',
            data: getEmailTemplatesResult?.data?.filter((item) =>
              item.type.startsWith('franchisee-')
            ),
          },
          {
            title: 'Franchisor Emails',
            description:
              'Manage email notifications for sending to franchisors',
            data: getEmailTemplatesResult?.data?.filter((item) =>
              item.type.startsWith('franchisor-')
            ),
          },
          {
            title: 'Consultant Emails',
            description:
              'Manage email notifications for sending to consultants',
            data: getEmailTemplatesResult?.data?.filter((item) =>
              item.type.startsWith('consultant-')
            ),
          },
        ].map((group) => (
          <div
            key={group.title}
            className="flex flex-col rounded-xl border border-border bg-white"
          >
            <div className="flex flex-col p-6">
              <p className="as-title-02 as-strong w-[280px] text-zinc-700">
                {group.title}
              </p>
              <p className="as-body-03 text-zinc-500">{group.description}</p>
            </div>
            <div className="flex flex-col divide-y divide-slate-200 border-t border-border">
              {group.data?.map((item) => (
                <EmailSettingsFormItem
                  key={item.id}
                  item={item}
                  refetch={getEmailTemplatesRefetch}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const EmailSettingsFormItem = ({
  item,
  refetch,
}: {
  item: EmailTemplate;
  refetch: () => void;
}): JSX.Element => {
  const {
    mutate: updateEmailTemplate,
    isError: updateEmailTemplateIsError,
    isSuccess: updateEmailTemplateIsSuccess,
  } = useUpdateEmailTemplate();

  const handleSubmit = (values: EmailTemplate): void => {
    updateEmailTemplate({
      id: item.id,
      payload: {
        name: values.name,
        template_subject: values.template_subject,
        active: values.active,
      },
    });
  };

  useEffect(() => {
    if (updateEmailTemplateIsError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [updateEmailTemplateIsError]);

  useEffect(() => {
    if (updateEmailTemplateIsSuccess) {
      toast({
        title: 'Email template updated',
        description: 'Email template updated successfully',
        variant: 'default',
      });
      refetch();
    }
  }, [updateEmailTemplateIsSuccess]);

  return (
    <div className="flex flex-col divide-y divide-slate-200 last:rounded-b-xl">
      <Link
        to={`/dashboard/email-templates/${item.id}`}
        className="relative flex w-full items-center justify-between px-4 hover:bg-zinc-50"
      >
        <div className="py-4">
          <p className="as-body-03 as-strong w-[280px] font-medium text-zinc-700">
            {item.name}
          </p>
          <p className="as-body-04 text-zinc-400">{item.description}</p>
        </div>
        <div className="absolute right-6 top-1/2 -translate-y-1/2">
          <Switch
            checked={item.active}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleSubmit({ ...item, active: !item.active });
            }}
          />
        </div>
      </Link>
    </div>
  );
};

export default EmailSettingsForm;
