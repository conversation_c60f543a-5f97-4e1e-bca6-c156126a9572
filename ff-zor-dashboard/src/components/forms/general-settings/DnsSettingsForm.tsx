import { Loader2 } from 'lucide-react';

import { useGetAllDomains } from '@/hooks/dns-settings/useGetAllDomains';

import DnsMailDomainForm from '../dns/DnsMailDomainForm';
import DnsPortalDomainForm from '../dns/DnsPortalDomainForm';
import DnsRootDomainForm from '../dns/DnsRootDomainForm';

/**
 * General settings form component
 * @param props - Component props
 * @returns JSX.Element
 */
const DnsSettingsForm = (): JSX.Element => {
  const {
    result: getDomainsResult,
    isLoading: getDomainsIsLoading,
    refetch: refetchDomains,
  } = useGetAllDomains();

  const getDomainByType = (type: string) =>
    getDomainsResult?.data?.find((item) => item.type === type);

  const rootDomain = getDomainByType('root');
  const mailDomains = getDomainsResult?.data?.filter((item) =>
    item.type.startsWith('mail-')
  );

  if (getDomainsIsLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <DnsRootDomainForm domain={rootDomain} refetchDomains={refetchDomains} />

      {rootDomain && (
        <div className="flex flex-col gap-4">
          <DnsPortalDomainForm
            domain={getDomainByType('portal-franchisee')}
            rootDomain={rootDomain}
            type="portal-franchisee"
            refetchDomains={refetchDomains}
          />
          <DnsPortalDomainForm
            domain={getDomainByType('portal-consultant')}
            rootDomain={rootDomain}
            type="portal-consultant"
            refetchDomains={refetchDomains}
          />
          <DnsMailDomainForm
            domains={mailDomains}
            rootDomain={rootDomain}
            refetchDomains={refetchDomains}
          />
        </div>
      )}
    </div>
  );
};

export default DnsSettingsForm;
