import { forwardRef, useImperative<PERSON>andle, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { GeneralFormValues } from '@/components/forms/schemas/settings/GeneralFormSchema';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import {
  exampleConverionTrackingData,
  exampleIntegrationData,
  IntegrationDataType,
} from '@/constants/dummy';

interface IntegrationFormProps {
  /** Form instance from react-hook-form */
  form: UseFormReturn<GeneralFormValues>;
}

interface IntegrationFormRef {
  /** Submit the form programmatically */
  submitForm: () => void;
}

/**
 * IntegrationForm component for managing third-party integrations
 * Allows connecting and disconnecting various services
 */
const IntegrationForm = forwardRef<IntegrationFormRef, IntegrationFormProps>(
  ({ form }, ref) => {
    const [integrationData, setIntegrationData] = useState<
      IntegrationDataType[]
    >(exampleIntegrationData);
    const [conversionTrackingData, setConversionTrackingData] = useState<
      IntegrationDataType[]
    >(exampleConverionTrackingData);

    const removeIntegrationConnection = (indexToRemove: number): void => {
      setIntegrationData((prevData) =>
        prevData.map((item, index) =>
          index === indexToRemove ? { ...item, connected: false } : item
        )
      );
    };

    const removeConversionTrackingConnection = (
      indexToRemove: number
    ): void => {
      setConversionTrackingData((prevData) =>
        prevData.map((item, index) =>
          index === indexToRemove ? { ...item, connected: false } : item
        )
      );
    };

    const handleRemoveClick =
      (onRemove: (index: number) => void, index: number) => (): void => {
        onRemove(index);
      };

    useImperativeHandle(ref, () => ({
      submitForm: () => {
        form.handleSubmit(async () => {})();
      },
    }));

    const renderIntegrationItem = (
      item: IntegrationDataType,
      index: number,
      onRemove: (index: number) => void
    ) => (
      <div
        key={`integration_${index}`}
        className="flex items-center justify-between rounded-xl bg-zinc-50 p-3"
      >
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center rounded-md bg-white p-2">
            <img
              src={item.icon}
              alt={`${item.title} icon`}
              className="h-6 w-6"
            />
          </div>
          <p className="as-title-03 font-medium text-zinc-700">{item.title}</p>
        </div>
        {item.connected ? (
          <div className="flex items-center gap-2">
            <p className="as-title-03 font-medium text-zinc-900">
              Already connected
            </p>
            <Button
              size="sm"
              type="button"
              variant="outline"
              className="text-zinc-900"
              onClick={handleRemoveClick(onRemove, index)}
            >
              Remove
            </Button>
          </div>
        ) : (
          <Button size="sm" type="button">
            Connect
          </Button>
        )}
      </div>
    );

    return (
      <Form {...form}>
        <form className="space-y-2">
          <div className="flex gap-6 rounded-xl border border-border bg-white p-6">
            <div className="w-[280px]">
              <p className="as-title-03 as-strong text-zinc-700">
                Integrations
              </p>
              <p className="as-title-04 text-typo-gray-tertiary">
                Customize your integrations
              </p>
            </div>
            <div className="grid flex-1 gap-2">
              {integrationData.map((item, index) =>
                renderIntegrationItem(item, index, removeIntegrationConnection)
              )}
            </div>
          </div>
          <div className="flex gap-6 rounded-xl border border-border bg-white p-6">
            <p className="as-title-03 as-strong w-[280px] text-zinc-700">
              Conversion tracking
            </p>
            <div className="grid flex-1 gap-2">
              {conversionTrackingData.map((item, index) =>
                renderIntegrationItem(
                  item,
                  index,
                  removeConversionTrackingConnection
                )
              )}
            </div>
          </div>
        </form>
      </Form>
    );
  }
);

IntegrationForm.displayName = 'IntegrationForm';

export default IntegrationForm;
