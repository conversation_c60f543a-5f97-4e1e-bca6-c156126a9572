import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import {
  GeneralFormSchema,
  GeneralFormValues,
} from '@/components/forms/schemas/settings/GeneralFormSchema';
import FormImage from '@/components/global/form-inputs/FormImage';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { toast } from '@/hooks/ui/use-toast';
import { useCreateZorGeneralSetting } from '@/hooks/zor-general-setting/useCreateZorGeneralSetting';
import { useGetZorGeneralSettings } from '@/hooks/zor-general-setting/useGetZorGeneralSettings';
import { useUpdateZorGeneralSetting } from '@/hooks/zor-general-setting/useUpdateZorGeneralSetting';
import { cn } from '@/lib/ui/utils';
import useZorGeneralSettingStore from '@/stores/useZorGeneralSettingStore';

interface GeneralSettingsFormProps {
  /** Edit mode identifier */
  isEdit: string | null;
  /** Function to update edit mode */
  setIsEdit: React.Dispatch<React.SetStateAction<string | null>>;
}

/**
 * General settings form component
 * @param props - Component props
 * @returns JSX.Element
 */
const GeneralSettingsForm = ({
  isEdit,
  setIsEdit,
}: GeneralSettingsFormProps) => {
  const form = useForm<GeneralFormValues>({
    resolver: zodResolver(GeneralFormSchema),
    defaultValues: {},
  });

  const { zorGeneralSetting, setZorGeneralSetting } =
    useZorGeneralSettingStore();

  const {
    result: getZorGeneralSettingsResult,
    isError: getZorGeneralSettingsIsError,
    isLoading: getZorGeneralSettingsIsLoading,
    isSuccess: getZorGeneralSettingsIsSuccess,
    isRefetching: getZorGeneralSettingsIsRefetching,
  } = useGetZorGeneralSettings();

  const {
    mutate: createZorGeneralSetting,
    result: createZorGeneralSettingResult,
    isError: createZorGeneralSettingIsError,
    isLoading: createZorGeneralSettingIsLoading,
    isSuccess: createZorGeneralSettingIsSuccess,
  } = useCreateZorGeneralSetting();

  const {
    mutate: updateZorGeneralSetting,
    result: updateZorGeneralSettingResult,
    isError: updateZorGeneralSettingIsError,
    isLoading: updateZorGeneralSettingIsLoading,
    isSuccess: updateZorGeneralSettingIsSuccess,
  } = useUpdateZorGeneralSetting();

  const resetForms = () => {
    form.reset({ ...zorGeneralSetting });
  };

  const handleCancel = (): void => {
    resetForms();
    setIsEdit(null);
  };

  const handleSubmit = (values: GeneralFormValues): void => {
    if (zorGeneralSetting) {
      updateZorGeneralSetting(values);
    } else {
      createZorGeneralSetting(values);
    }
  };

  useEffect(() => {
    resetForms();
  }, [zorGeneralSetting]);

  useEffect(() => {
    const hasError =
      getZorGeneralSettingsIsError ||
      createZorGeneralSettingIsError ||
      updateZorGeneralSettingIsError;
    if (hasError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [
    getZorGeneralSettingsIsError,
    createZorGeneralSettingIsError,
    updateZorGeneralSettingIsError,
  ]);

  useEffect(() => {
    if (
      !isEdit &&
      getZorGeneralSettingsIsSuccess &&
      getZorGeneralSettingsResult?.data
    ) {
      setZorGeneralSetting({ ...getZorGeneralSettingsResult.data });
    }
  }, [
    getZorGeneralSettingsResult,
    getZorGeneralSettingsIsSuccess,
    getZorGeneralSettingsIsRefetching,
    setZorGeneralSetting,
  ]);

  useEffect(() => {
    if (createZorGeneralSettingIsSuccess || updateZorGeneralSettingIsSuccess) {
      const newData =
        createZorGeneralSettingResult?.data ||
        updateZorGeneralSettingResult?.data;

      if (newData) {
        setZorGeneralSetting({ ...newData });
        setIsEdit(null);
      }
    }
  }, [
    createZorGeneralSettingResult,
    updateZorGeneralSettingResult,
    createZorGeneralSettingIsSuccess,
    updateZorGeneralSettingIsSuccess,
    setZorGeneralSetting,
  ]);

  if (getZorGeneralSettingsIsLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  const isDisabled = (): boolean => isEdit !== 'general';
  const isLoading =
    createZorGeneralSettingIsLoading || updateZorGeneralSettingIsLoading;
  const showRefetchingLoader = !isEdit && getZorGeneralSettingsIsRefetching;

  return (
    <Form {...form}>
      <form
        className="relative space-y-2"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        {showRefetchingLoader && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="size-4 animate-spin" />
          </div>
        )}
        {/* <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            isEdit === 'general' ? 'flex-row' : 'flex-col'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Domain Routing
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <FormInput
              type="text"
              name="website_url"
              label="Website URL"
              control={form.control}
              disabled={isDisabled()}
            />
          </div>
        </div> */}
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            isEdit === 'general' ? 'flex-row' : 'flex-col'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Company UI
          </p>
          <div className="grid flex-1 gap-6">
            <div className="flex w-1/2 flex-col items-start gap-6 rounded-xl border border-border bg-white p-6">
              <div className="as-title-03 as-strong text-zinc-700">
                Logo
                <p className="as-title-03 font-normal">
                  This will be displayed in the application
                </p>
              </div>
              <div className="flex w-full items-center justify-between gap-1">
                <FormImage
                  name="company_logo_url"
                  isEdit={isEdit === 'general'}
                  control={form.control}
                  setValue={form.setValue}
                />
              </div>
            </div>
            <FormTextArea
              type="text"
              name="login_greeting_text_h1"
              label="Log-in Page Greeting (H1)"
              control={form.control}
              disabled={isDisabled()}
            />
            <FormTextArea
              type="text"
              name="login_greeting_text_h2"
              label="Log-in Page Greeting (H2)"
              control={form.control}
              disabled={isDisabled()}
            />
          </div>
        </div>
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            isEdit === 'general' ? 'flex-row' : 'flex-col'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Franchise Homepage Welcome
          </p>
          <div className="grid flex-1 gap-6">
            <FormTextArea
              type="text"
              name="homepage_greeting_text"
              label="Greeting text"
              control={form.control}
              disabled={isDisabled()}
            />
            <FormInput
              type="number"
              name="onboarding_countdown"
              label="Onboarding Countdown (Days)"
              control={form.control}
              disabled={isDisabled()}
              className="max-w-sm"
            />
          </div>
        </div>

        {isEdit === 'general' && (
          <div className="flex justify-end">
            <div className="flex w-full gap-2 md:w-fit">
              <Button
                size="sm"
                type="button"
                variant="outline"
                className="w-full md:w-fit"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                type="submit"
                className="w-full md:w-fit"
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="size-4 animate-spin" />}
                Save
              </Button>
            </div>
          </div>
        )}
      </form>
    </Form>
  );
};

export default GeneralSettingsForm;
