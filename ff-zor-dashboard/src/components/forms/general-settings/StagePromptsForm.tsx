import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Plus, Trash2 } from 'lucide-react';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import {
  StagePromptsFormSchema,
  StagePromptsFormValues,
} from '@/components/forms/schemas/settings/StagePromptsFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useCreateStagePrompt } from '@/hooks/stage-prompts/useCreateStagePrompts';
import { useGetStagePrompts } from '@/hooks/stage-prompts/useGetStagePrompts';
import { toast } from '@/hooks/ui/use-toast';
import { formatToTwoDigits } from '@/lib/string';
import useZorAimeiPromptsStore from '@/stores/useZorAimeiPromptsStore';

interface StagePromptsFormProps {
  isEdit: string | null;
  setIsEdit: React.Dispatch<React.SetStateAction<string | null>>;
}

/**
 * StagePromptsForm component for managing stage prompts
 * Allows adding, editing, and removing stage prompts with validation
 */
const StagePromptsForm = ({ isEdit, setIsEdit }: StagePromptsFormProps) => {
  const { stagePrompts, setStagePrompts } = useZorAimeiPromptsStore();

  const form = useForm<StagePromptsFormValues>({
    resolver: zodResolver(StagePromptsFormSchema),
    defaultValues: {
      prompts: [],
    },
  });

  const {
    result: getStagePromptsResult,
    isError: isGetStagePromptsError,
    isSuccess: isGetStagePromptsSuccess,
    isLoading: isGetStagePromptsLoading,
    isRefetching: isGetStagePromptsRefetching,
  } = useGetStagePrompts();

  const {
    mutate: createStagePrompts,
    result: createStagePromptsResult,
    isError: isCreateStagePromptsError,
    isLoading: isCreateStagePromptsLoading,
    isSuccess: isCreateStagePromptsSuccess,
  } = useCreateStagePrompt();

  const { fields, append, remove } = useFieldArray({
    name: 'prompts',
    control: form.control,
  });

  const isEditing = isEdit === 'stage-prompts';
  const showRefetchingLoader = !isEdit && isGetStagePromptsRefetching;

  const resetForms = () => {
    form.reset({
      prompts: [...(stagePrompts || [])],
    });
  };

  const handleCancel = (): void => {
    resetForms();
    setIsEdit(null);
  };

  const handleRemove = (index: number): void => {
    remove(index);
  };

  const handleRemoveClick = (index: number) => (): void => {
    handleRemove(index);
  };

  const handleAddPrompt = (): void => {
    append({ title: '', prompt: '' });
  };

  const handleSubmit = (values: StagePromptsFormValues): void => {
    createStagePrompts(values);
  };

  useEffect(() => {
    resetForms();
  }, [stagePrompts]);

  useEffect(() => {
    const hasError = isGetStagePromptsError || isCreateStagePromptsError;
    if (hasError) {
      toast({
        title: 'Error',
        description: 'Failed to load stage prompts. Please try again later.',
        variant: 'destructive',
      });
    }
  }, [isGetStagePromptsError, isCreateStagePromptsError]);

  useEffect(() => {
    if (!isEdit && isGetStagePromptsSuccess && getStagePromptsResult?.data) {
      setStagePrompts([...getStagePromptsResult.data]);
    }
  }, [getStagePromptsResult, isGetStagePromptsSuccess, setStagePrompts]);

  useEffect(() => {
    if (isCreateStagePromptsSuccess && createStagePromptsResult?.data) {
      setStagePrompts([...createStagePromptsResult.data]);
      setIsEdit(null);
    }
  }, [createStagePromptsResult, isCreateStagePromptsSuccess, setStagePrompts]);

  return (
    <Form {...form}>
      <form className="space-y-2" onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="rounded-xl border border-border bg-white p-6">
          <div className="flex items-start justify-between gap-6">
            <div className="flex w-60 items-center gap-2">
              <p className="as-title-03 as-strong text-zinc-700">
                Stage Prompts
              </p>
              {showRefetchingLoader && (
                <Loader2 className="size-4 animate-spin" />
              )}
            </div>
            {!isGetStagePromptsLoading && (
              <div className="grid w-full flex-1 gap-4">
                <div className="space-y-6">
                  {fields.length > 0 &&
                    fields.map((field, index) => (
                      <div
                        key={field.id}
                        className="relative grid flex-1 gap-2"
                      >
                        {isEditing && (
                          <Trash2
                            className="hover:text-destructive/90 absolute right-2 top-2 h-4 w-4 cursor-pointer text-destructive"
                            onClick={handleRemoveClick(index)}
                            aria-label="Remove prompt"
                          />
                        )}
                        <FormInput
                          type="text"
                          name={`prompts.${index}.title`}
                          label={`${formatToTwoDigits(index + 1)} Prompt Title`}
                          control={form.control}
                          disabled={!isEditing}
                        />
                        <FormTextArea
                          name={`prompts.${index}.prompt`}
                          label={`${formatToTwoDigits(index + 1)} Prompt Text`}
                          control={form.control}
                          disabled={!isEditing}
                        />
                      </div>
                    ))}
                </div>
                {isEditing && (
                  <div className="flex w-full justify-end">
                    <Button
                      size="sm"
                      type="button"
                      className="w-full text-right md:w-fit"
                      onClick={handleAddPrompt}
                    >
                      Add <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          {isGetStagePromptsLoading && (
            <div className="flex w-full items-center justify-center p-4">
              <Loader2 className="size-4 animate-spin" />
            </div>
          )}
          {fields.length === 0 && !isGetStagePromptsLoading && (
            <div className="flex h-[100px] w-full items-center justify-center text-muted-foreground">
              No prompts found
            </div>
          )}
        </div>
        {isEditing && (
          <div className="flex justify-end">
            <div className="flex w-full gap-2 md:w-fit">
              <Button
                size="sm"
                type="button"
                variant="outline"
                className="w-full md:w-fit"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                type="submit"
                disabled={isCreateStagePromptsLoading}
                className="w-full md:w-fit"
              >
                {isCreateStagePromptsLoading && (
                  <Loader2 className="size-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </div>
          </div>
        )}
      </form>
    </Form>
  );
};

export default StagePromptsForm;
