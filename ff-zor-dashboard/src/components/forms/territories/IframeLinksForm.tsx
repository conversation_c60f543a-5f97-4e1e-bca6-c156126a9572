import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { PenBoxIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { cn } from '@/lib/ui/utils';

const IframeLinksFormSchema = z.object({
  website_iframe: z.string().min(1, {
    message: "Website Iframe can't be empty",
  }),
  email_iframe: z.string().min(1, {
    message: "Email Iframe can't be empty",
  }),
  mobile_iframe: z.string().min(1, {
    message: "Mobile Iframe can't be empty",
  }),
});

const IframeLinksForm = () => {
  const [isEdit, setIsEdit] = useState<boolean>(false);

  const form = useForm<z.infer<typeof IframeLinksFormSchema>>({
    resolver: zodResolver(IframeLinksFormSchema),
    defaultValues: {
      website_iframe: '',
      email_iframe: '',
      mobile_iframe: '',
    },
  });

  const handleEditClick = (): void => {
    setIsEdit(true);
  };

  const handleCancelClick = (): void => {
    setIsEdit(false);
  };

  return (
    <div className="rounded-xl border border-border bg-muted">
      <div className="flex items-center justify-between border-b border-border px-6 py-3.5">
        <p className="as-title-01 as-strong text-gray-700">iFrame Links</p>
        {!isEdit && (
          <Button size="sm" variant="outline" onClick={handleEditClick}>
            <PenBoxIcon /> Edit
          </Button>
        )}
      </div>
      <div className="p-4">
        <Form {...form}>
          <form
            className={cn(
              'flex gap-5 rounded-xl border border-border bg-white p-6',
              isEdit ? 'flex-row' : 'flex-col'
            )}
          >
            <p className="as-title-02 as-strong w-[280px] text-zinc-700">
              iFrame Embed Links
            </p>
            <div className="flex-1">
              <div className="grid grid-cols-2 gap-6 pb-6">
                <FormInput
                  name="website_iframe"
                  label="Website Iframe"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormInput
                  name="email_iframe"
                  label="Email Iframe"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormInput
                  name="mobile_iframe"
                  label="Mobile Iframe"
                  control={form.control}
                  disabled={!isEdit}
                />
              </div>
              {isEdit && (
                <div className="flex w-full justify-end">
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      type="button"
                      variant="outline"
                      className="w-full md:w-fit"
                      onClick={handleCancelClick}
                    >
                      Cancel
                    </Button>
                    <Button size="sm" className="w-full md:w-fit">
                      Save Change
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default IframeLinksForm;
