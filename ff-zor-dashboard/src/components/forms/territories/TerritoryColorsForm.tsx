import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, PenBoxIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  TerritoryColorsFormSchema,
  TerritoryColorsFormValues,
} from '@/components/forms/schemas/territories/TerritoryColorsFormSchema';
import FormColorPicker from '@/components/global/form-inputs/FormColorPicker';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useUpdateTerritorySettings } from '@/hooks/territories/territories-settings/useUpdateTerritorySettings';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';

/**
 * TerritoryColorsForm component for managing territory color settings
 * Allows customizing colors for different territory statuses
 */
const TerritoryColorsForm = () => {
  const [isEdit, setIsEdit] = useState<boolean>(false);

  const { territorySettings, setTerritorySettings } = useTerritoryStore();

  const form = useForm<TerritoryColorsFormValues>({
    resolver: zodResolver(TerritoryColorsFormSchema),
    defaultValues: {
      sold: territorySettings?.colors?.sold || '',
      liked: territorySettings?.colors?.liked || '',
      reserved: territorySettings?.colors?.reserved || '',
      available: territorySettings?.colors?.available || '',
      unavailable: territorySettings?.colors?.unavailable || '',
      pending: territorySettings?.colors?.pending || '',
    },
  });

  const {
    mutate: updateTerritorySettings,
    result: updateTerritorySettingsResult,
    isError: isUpdateTerritorySettingsError,
    isSuccess: isUpdateTerritorySettingsSuccess,
    isLoading: isUpdateTerritorySettingsLoading,
  } = useUpdateTerritorySettings();

  const handleSubmit = (values: TerritoryColorsFormValues): void => {
    updateTerritorySettings({
      colors: values,
    });
  };

  const handleCancel = (): void => {
    setIsEdit(false);
    form.reset({
      sold: territorySettings?.colors?.sold || '',
      liked: territorySettings?.colors?.liked || '',
      reserved: territorySettings?.colors?.reserved || '',
      available: territorySettings?.colors?.available || '',
      unavailable: territorySettings?.colors?.unavailable || '',
      pending: territorySettings?.colors?.pending || '',
    });
  };

  const handleEditClick = (): void => {
    setIsEdit(true);
  };

  useEffect(() => {
    if (territorySettings) {
      form.reset({
        sold: territorySettings.colors?.sold || '',
        liked: territorySettings.colors?.liked || '',
        reserved: territorySettings.colors?.reserved || '',
        available: territorySettings.colors?.available || '',
        unavailable: territorySettings.colors?.unavailable || '',
        pending: territorySettings.colors?.pending || '',
      });
    }
  }, [territorySettings, form]);

  useEffect(() => {
    if (isUpdateTerritorySettingsError) {
      toast({
        title: 'Error',
        description:
          'Failed to update territory colors. Please try again later.',
        variant: 'destructive',
      });
    }
  }, [isUpdateTerritorySettingsError]);

  useEffect(() => {
    if (isUpdateTerritorySettingsSuccess) {
      setTerritorySettings({
        ...territorySettings,
        ...(updateTerritorySettingsResult?.data || {}),
      });
      setIsEdit(false);
      toast({
        title: 'Success',
        description: 'Territory colors updated successfully.',
      });
    }
  }, [
    updateTerritorySettingsResult,
    isUpdateTerritorySettingsSuccess,
    setTerritorySettings,
  ]);

  return (
    <div className="rounded-xl border border-border bg-muted">
      <div className="flex items-center justify-between border-b border-border px-6 py-3.5">
        <p className="as-title-01 as-strong text-gray-700">Map Data</p>
        {!isEdit && (
          <Button size="sm" variant="outline" onClick={handleEditClick}>
            <PenBoxIcon className="h-4 w-4" /> Edit
          </Button>
        )}
      </div>
      <div className="p-4">
        <Form {...form}>
          <form
            className={cn(
              'flex gap-5 rounded-xl border border-border bg-white p-6',
              isEdit ? 'flex-row' : 'flex-col'
            )}
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <p className="as-title-02 as-strong w-[280px] text-zinc-700">
              Territory Colors
            </p>
            <div className="flex-1">
              <div className="grid grid-cols-2 gap-6 pb-6">
                <FormColorPicker
                  name="sold"
                  label="Sold"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormColorPicker
                  name="liked"
                  label="Liked"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormColorPicker
                  name="reserved"
                  label="Reserved"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormColorPicker
                  name="available"
                  label="Available"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormColorPicker
                  name="unavailable"
                  label="Unavailable"
                  control={form.control}
                  disabled={!isEdit}
                />
                <FormColorPicker
                  name="pending"
                  label="Pending"
                  control={form.control}
                  disabled={!isEdit}
                />
              </div>
              {isEdit && (
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      type="button"
                      variant="outline"
                      className="w-full md:w-fit"
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      type="submit"
                      className="w-full md:w-fit"
                      disabled={isUpdateTerritorySettingsLoading}
                    >
                      {isUpdateTerritorySettingsLoading ? (
                        <>
                          <Loader2 className="size-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default TerritoryColorsForm;
