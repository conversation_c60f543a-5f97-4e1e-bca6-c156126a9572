import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2, PenBoxIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import getAvailableZipCodesCount from '@/actions/territories/getAvailableZipCodesCount';
import FormRadioGroup from '@/components/global/form-inputs/FormRadioGroup';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  MinimumAverageHouseholdIncomeOptions,
  MinimumPopulationSizeOptions,
} from '@/constants/options';
import { useUpdateTerritorySettings } from '@/hooks/territories/territories-settings/useUpdateTerritorySettings';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';

const PolygonDataFormSchema = z.object({
  is_heatmap_active: z.boolean(),
  min_population_size: z.string().min(1, {
    message: "Minimum Population Size can't be empty",
  }),
  min_median_household_income: z.string().min(1, {
    message: "Minimum Average Household Income can't be empty",
  }),
});

const PolygonDataForm = () => {
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [availableZipCodesCount, setAvailableZipCodesCount] = useState<
    number | null
  >(null);

  const { territorySettings, setTerritorySettings } = useTerritoryStore();

  const form = useForm<z.infer<typeof PolygonDataFormSchema>>({
    resolver: zodResolver(PolygonDataFormSchema),
    defaultValues: {
      is_heatmap_active: territorySettings?.is_heatmap_active || false,
      min_population_size:
        territorySettings?.min_population_size?.toString() || undefined,
      min_median_household_income:
        territorySettings?.min_median_household_income?.toString() || undefined,
    },
  });

  const {
    mutate: updateTerritorySettings,
    result: updatedTerritorySettings,
    isError,
    isSuccess,
    isLoading,
  } = useUpdateTerritorySettings();

  const fetchAvailableZipCodesCount = async () => {
    setAvailableZipCodesCount(null);
    const response = await getAvailableZipCodesCount({
      minPopulationSize: form.getValues('min_population_size')
        ? Number(form.getValues('min_population_size'))
        : undefined,
      minMedianHouseholdIncome: form.getValues('min_median_household_income')
        ? Number(form.getValues('min_median_household_income'))
        : undefined,
    });
    setAvailableZipCodesCount(response.data);
  };

  const handleSubmit = (data: z.infer<typeof PolygonDataFormSchema>) => {
    updateTerritorySettings({
      is_heatmap_active: data.is_heatmap_active,
      min_population_size: Number(data.min_population_size),
      min_median_household_income: Number(data.min_median_household_income),
    });
  };

  const handleCancel = () => {
    setIsEdit(false);
    form.reset({
      is_heatmap_active: territorySettings?.is_heatmap_active || false,
      min_population_size:
        territorySettings?.min_population_size?.toString() || undefined,
      min_median_household_income:
        territorySettings?.min_median_household_income?.toString() || undefined,
    });
    fetchAvailableZipCodesCount();
  };

  const handleEditClick = (): void => {
    setIsEdit(true);
  };

  useEffect(() => {
    if (isError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isError]);

  useEffect(() => {
    if (territorySettings) {
      form.reset({
        is_heatmap_active: territorySettings.is_heatmap_active || false,
        min_population_size:
          territorySettings.min_population_size?.toString() || undefined,
        min_median_household_income:
          territorySettings.min_median_household_income?.toString() ||
          undefined,
      });

      fetchAvailableZipCodesCount();
    }
  }, [territorySettings, form]);

  useEffect(() => {
    if (isSuccess) {
      setIsEdit(false);
      setTerritorySettings({
        ...territorySettings,
        ...(updatedTerritorySettings?.data || {}),
      });
    }
  }, [isSuccess, updatedTerritorySettings]);

  return (
    <div className="rounded-xl border border-border bg-muted">
      <div className="flex items-center justify-between border-b border-border px-6 py-3.5">
        <p className="as-title-01 as-strong text-gray-700">Polygon Data</p>
        {!isEdit && (
          <Button size="sm" variant="outline" onClick={handleEditClick}>
            <PenBoxIcon /> Edit
          </Button>
        )}
      </div>
      <div className="p-4">
        <Form {...form}>
          <form
            className={cn(
              'flex gap-5 rounded-xl border border-border bg-white p-6',
              isEdit ? 'flex-row' : 'flex-col'
            )}
            onChange={fetchAvailableZipCodesCount}
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <p className="as-title-02 as-strong w-[280px] text-zinc-700">
              Data
            </p>
            <div className="flex-1">
              <div className="grid grid-cols-2 gap-6 border-b border-border pb-6">
                <FormSelect
                  name="min_population_size"
                  label="Minimum Population Size"
                  control={form.control}
                  options={MinimumPopulationSizeOptions}
                  disabled={!isEdit}
                />
                <FormSelect
                  name="min_median_household_income"
                  label="Minimum Average Household Income"
                  control={form.control}
                  options={MinimumAverageHouseholdIncomeOptions}
                  disabled={!isEdit}
                />
                <FormRadioGroup
                  name="is_heatmap_active"
                  label="Heatmap"
                  control={form.control}
                  disabled={!isEdit}
                  radioLabels={['Active', 'Inactive']}
                />
              </div>
              <div className="space-y-1 pt-6">
                <div className="space-y-2">
                  <Label htmlFor="available_zip_codes_count">
                    Total Available Zip Codes Based On Settings
                  </Label>
                  <Input
                    id="available_zip_codes_count"
                    value={availableZipCodesCount || 'Calculating...'}
                    readOnly
                    className="max-w-sm"
                  />
                </div>
                {isEdit && (
                  <div className="flex w-full justify-end">
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        type="button"
                        variant="outline"
                        disabled={isLoading}
                        className="w-full md:w-fit"
                        onClick={handleCancel}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        disabled={isLoading}
                        className="w-full md:w-fit"
                      >
                        {isLoading && <Loader2 className="size-4" />}
                        Save Change
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default PolygonDataForm;
