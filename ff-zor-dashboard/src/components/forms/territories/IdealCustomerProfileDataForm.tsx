import { Loader2, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { Switch } from '@/components/ui/switch';
import { CENSUS_DATA_FIELDS_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { DEFAULT_CENSUS_DATA_FIELDS } from '@/constants/territories/variables';
import apiManager from '@/helpers/api.manager';
import { useUpdateTerritorySettings } from '@/hooks/territories/territories-settings/useUpdateTerritorySettings';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { TerritorySettings } from '@/types/territories/territory.settings.type';

const IdealCustomerProfileDataForm = () => {
  const [isSaving, setIsSaving] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [newDataField, setNewDataField] = useState<string | undefined>(
    undefined
  );
  const [selectedDataField, setSelectedDataField] = useState<
    string | undefined
  >(undefined);
  const [addingNewDataField, setAddingNewDataField] = useState(false);

  const { acsVariables, territorySettings, setTerritorySettings } =
    useTerritoryStore();

  const {
    mutate: updateTerritorySettings,
    result: updateTerritorySettingsResult,
    isError: isUpdateTerritorySettingsError,
    isLoading: isUpdateTerritorySettingsLoading,
    isSuccess: isUpdateTerritorySettingsSuccess,
  } = useUpdateTerritorySettings();

  const handleDeleteClick = (fieldId: string) => (): void => {
    setSelectedDataField(fieldId);
    handleDeleteDataField(fieldId);
  };

  const handleCancelAddClick = (): void => {
    setAddingNewDataField(false);
  };

  const handleAddDataFieldClick = (): void => {
    setAddingNewDataField(true);
  };

  const handleNewDataFieldChange = (value: string): void => {
    setNewDataField(value);
  };

  const handleSaveNewDataField = async () => {
    if (newDataField) {
      setIsSaving(true);

      const response = await apiManager.post(
        CENSUS_DATA_FIELDS_API_URL,
        {
          code: newDataField,
        },
        {
          baseURL: configuration.TERRITORIES_API_URL,
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${window.localStorage.getItem('access-token')}`,
          },
        }
      );

      if (response.status !== 200) {
        return;
      }

      setTerritorySettings({
        ...territorySettings,
        extra_acs_variables: [
          ...(territorySettings?.extra_acs_variables || []),
          ...response.data.data,
        ],
      });

      setNewDataField(undefined);
      setAddingNewDataField(false);
      setIsSaving(false);
    }
  };

  const handleDeleteDataField = async (id: string) => {
    setIsDeleting(true);

    const response = await apiManager.delete(
      `${CENSUS_DATA_FIELDS_API_URL}/${id}`,
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${window.localStorage.getItem('access-token')}`,
        },
      }
    );

    if (response.status !== 200) {
      setSelectedDataField(undefined);
      setIsDeleting(false);
      return;
    }

    setSelectedDataField(undefined);
    setTerritorySettings({
      ...territorySettings,
      extra_acs_variables: territorySettings?.extra_acs_variables?.filter(
        (field) => field.id !== id
      ),
    });

    setIsDeleting(false);
  };

  const handleUpdateHeatmapActive = async (id: string, checked: boolean) => {
    setIsUpdating(true);
    setSelectedDataField(id);

    const response = await apiManager.patch(
      `${CENSUS_DATA_FIELDS_API_URL}/${id}`,
      {
        is_heatmap_active: checked,
      },
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${window.localStorage.getItem('access-token')}`,
        },
      }
    );

    if (response.status !== 200) {
      setIsUpdating(false);
      setSelectedDataField(undefined);
      return;
    }

    setTerritorySettings({
      ...territorySettings,
      extra_acs_variables: territorySettings?.extra_acs_variables?.map(
        (field) =>
          field.id === id ? { ...field, is_heatmap_active: checked } : field
      ),
    });

    setIsUpdating(false);
  };

  useEffect(() => {
    if (isUpdateTerritorySettingsError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });

      setSelectedDataField(undefined);
    }
  }, [isUpdateTerritorySettingsError]);

  useEffect(() => {
    if (isUpdateTerritorySettingsSuccess) {
      setTerritorySettings({
        ...territorySettings,
        ...(updateTerritorySettingsResult?.data || {}),
      });

      setSelectedDataField(undefined);
    }
  }, [
    isUpdateTerritorySettingsSuccess,
    updateTerritorySettingsResult?.data,
    setTerritorySettings,
  ]);

  return (
    <div className="rounded-xl border border-border bg-muted">
      <div className="flex items-center justify-between border-b border-border px-6 py-3.5">
        <p className="as-title-01 as-strong text-gray-700">
          Ideal Customer Profile Data
        </p>
      </div>
      <div className="p-4">
        <div className="flex gap-5 rounded-xl border border-border bg-white p-6">
          <div className="w-[280px]">
            <p className="as-title-03 as-strong text-zinc-700">Census Data</p>
            <p className="as-title-04 as-strong text-muted-foreground">
              Maximum 10 data fields
            </p>
          </div>
          <div className="flex-1 space-y-4">
            <div className="rounded-lg border border-border">
              {DEFAULT_CENSUS_DATA_FIELDS.map((field, index) => (
                <div
                  key={field.id}
                  className={cn(
                    'flex items-center gap-5 border-border p-4',
                    index !== 0 && 'border-t'
                  )}
                >
                  <Input
                    value={field.label}
                    readOnly
                    disabled
                    className="flex-1"
                  />
                  <div className="flex w-[120px] items-center gap-2">
                    {isUpdateTerritorySettingsLoading &&
                    field.id === selectedDataField ? (
                      <Loader2 className="size-4 animate-spin" />
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Switch
                          id={`${field.id}-heatmap`}
                          checked={
                            (
                              territorySettings?.[
                                field.id as keyof TerritorySettings
                              ] as { is_heatmap_active: boolean }
                            )?.is_heatmap_active ?? false
                          }
                          onCheckedChange={(checked) => {
                            setSelectedDataField(field.id);
                            updateTerritorySettings({
                              [field.id]: {
                                is_heatmap_active: checked,
                                heatmap_weight:
                                  (
                                    territorySettings?.[
                                      field.id as keyof TerritorySettings
                                    ] as { heatmap_weight: number }
                                  )?.heatmap_weight || 0,
                              },
                            });
                          }}
                        />
                        <Label htmlFor={`${field.id}-heatmap`}>Heatmap</Label>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {territorySettings?.extra_acs_variables?.map(
                (field) =>
                  field.label && (
                    <div
                      key={field.id}
                      className="flex items-center gap-5 border-t border-border p-4"
                    >
                      <Input
                        value={field.label}
                        readOnly
                        disabled
                        className="flex-1"
                      />
                      <div className="flex w-[120px] items-center gap-2">
                        {isUpdating && field.id === selectedDataField ? (
                          <Loader2 className="size-4 animate-spin" />
                        ) : (
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`${field.id}-heatmap`}
                              checked={
                                territorySettings?.extra_acs_variables?.find(
                                  (variable) => variable.id === field.id
                                )?.is_heatmap_active ?? false
                              }
                              onCheckedChange={(checked) => {
                                setSelectedDataField(field.id);
                                handleUpdateHeatmapActive(field.id, checked);
                              }}
                            />
                            <Label htmlFor={`${field.id}-heatmap`}>
                              Heatmap
                            </Label>
                          </div>
                        )}
                      </div>
                      <Button
                        size="icon"
                        variant="destructive"
                        onClick={handleDeleteClick(field.id)}
                      >
                        {isDeleting && field.id === selectedDataField ? (
                          <Loader2 className="size-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  )
              )}
              {addingNewDataField && (
                <div className="flex items-center gap-4 border-t border-border p-4">
                  <SearchableSelect
                    value={newDataField}
                    options={
                      acsVariables
                        ?.filter(
                          (field) =>
                            !territorySettings?.extra_acs_variables?.some(
                              (extraField) => extraField.acs_code === field.code
                            )
                        )
                        ?.map((field) => ({
                          value: field.code,
                          label: field.new_label,
                        })) || []
                    }
                    placeholder="Select a data field"
                    onValueChange={handleNewDataFieldChange}
                  />
                  <Button variant="outline" onClick={handleCancelAddClick}>
                    Cancel
                  </Button>
                  <Button disabled={isSaving} onClick={handleSaveNewDataField}>
                    {isSaving ? 'Saving...' : 'Save'}
                  </Button>
                </div>
              )}
            </div>
            <Button
              variant="outline"
              className="float-right"
              disabled={territorySettings?.extra_acs_variables?.length === 4}
              onClick={handleAddDataFieldClick}
            >
              + Add data field
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IdealCustomerProfileDataForm;
