import { z } from 'zod';

import { VAL<PERSON>ATION_LIMITS, VAL<PERSON>ATION_MESSAGES } from '@/constants/validation';

/**
 * Schema for financial form validation
 * @see FinancialForm
 */
export const FinancialFormSchema = z
  .object({
    startup_cost_min: z
      .number()
      .min(1, {
        message: VAL<PERSON>ATION_MESSAGES.STARTUP_COST_REQUIRED,
      })
      .max(VALIDATION_LIMITS.MAX_FINANCIAL_AMOUNT, {
        message: VALIDATION_MESSAGES.STARTUP_COST_MAX,
      }),
    startup_cost_max: z
      .number()
      .min(1, {
        message: VALIDATION_MESSAGES.STARTUP_COST_REQUIRED,
      })
      .max(VALIDATION_LIMITS.MAX_FINANCIAL_AMOUNT, {
        message: VALIDATION_MESSAGES.STARTUP_COST_MAX,
      }),
    min_franchisee_net_worth: z
      .number()
      .min(1, {
        message: VALIDATION_MESSAGES.MIN_NETWORTH_REQUIRED,
      })
      .max(VALIDATION_LIMITS.MAX_FINANCIAL_AMOUNT, {
        message: VALIDATION_MESSAGES.MIN_NETWORTH_MAX,
      }),
    franchise_fee: z
      .number()
      .min(1, {
        message: VALIDATION_MESSAGES.FRANCHISE_FEE_REQUIRED,
      })
      .max(VALIDATION_LIMITS.MAX_INVESTMENT_AMOUNT, {
        message: VALIDATION_MESSAGES.FRANCHISE_FEE_MAX,
      }),
    veteran_franchise_fee: z
      .number()
      .min(1, {
        message: VALIDATION_MESSAGES.VETERAN_FEE_REQUIRED,
      })
      .max(VALIDATION_LIMITS.MAX_INVESTMENT_AMOUNT, {
        message: VALIDATION_MESSAGES.VETERAN_FEE_MAX,
      }),
    is_sba_eligible: z.boolean({
      required_error: VALIDATION_MESSAGES.SBA_ELIGIBLE_REQUIRED,
    }),
    is_veteran_discount: z.boolean({
      required_error: VALIDATION_MESSAGES.VETERAN_DISCOUNT_REQUIRED,
    }),
  })
  .refine((data) => data.startup_cost_max >= data.startup_cost_min, {
    message: VALIDATION_MESSAGES.STARTUP_COST_RANGE_INVALID,
    path: ['startup_cost_max'],
  });

/**
 * Type inference for the financial form schema
 */
export type FinancialFormValues = z.infer<typeof FinancialFormSchema>;

export default FinancialFormSchema;
