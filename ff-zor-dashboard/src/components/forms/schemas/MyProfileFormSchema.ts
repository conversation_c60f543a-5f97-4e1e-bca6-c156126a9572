import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const MyProfileFormSchema = z.object({
  full_name: z
    .string()
    .min(1, { message: VALIDATION_MESSAGES.FULL_NAME_REQUIRED }),
  position: z
    .string()
    .min(1, { message: VALIDATION_MESSAGES.ORGANIZATION_ROLE_REQUIRED }),
  email: z.string().email({ message: VALIDATION_MESSAGES.EMAIL_INVALID }),
  // optional fields
  photo_url: z.string().optional(),
  city: z.string().optional(),
  phone: z.string().optional(),
  meeting_link: z.string().optional(),
  linkedin: z.string().optional(),
  preferred_contact: z.string().optional(),
});

export default MyProfileFormSchema;
