import { z } from 'zod';

import { VALIDATION_LIMITS, VALIDATION_MESSAGES } from '@/constants/validation';

/**
 * Schema for validating Aimei prompt entries
 */
const AimeiPromptSchema = z.object({
  id: z.string().optional(),
  title: z
    .string({
      required_error: VALIDATION_MESSAGES.TITLE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.TITLE_REQUIRED,
    })
    .max(VALIDATION_LIMITS.SHORT_TEXT, {
      message: VALIDATION_MESSAGES.TITLE_TOO_LONG,
    }),
  prompt: z
    .string({
      required_error: VALIDATION_MESSAGES.PROMPT_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.PROMPT_REQUIRED,
    })
    .max(1000, {
      message: VALIDATION_MESSAGES.PROMPT_TOO_LONG,
    }),
});

/**
 * Schema for validating the Aimei prompts form
 */
export const AimeiPromptsFormSchema = z.object({
  prompts: z
    .array(AimeiPromptSchema)
    .min(1, {
      message: VALIDATION_MESSAGES.AT_LEAST_ONE_PROMPT,
    })
    .max(50, {
      message: VALIDATION_MESSAGES.MAX_PROMPTS_EXCEEDED,
    }),
});

/**
 * Type inference for the Aimei prompts form values
 */
export type AimeiPromptsFormValues = z.infer<typeof AimeiPromptsFormSchema>;
