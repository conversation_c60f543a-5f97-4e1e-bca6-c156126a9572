import { z } from 'zod';

import { VALIDATION_LIMITS, VALIDATION_MESSAGES } from '@/constants/validation';

/**
 * Schema for general settings form validation
 * @see GeneralSettingsForm
 */
export const GeneralFormSchema = z.object({
  // website_url: z
  //   .string({
  //     required_error: VALIDATION_MESSAGES.WEBSITE_REQUIRED,
  //   })
  //   .min(1, { message: VALIDATION_MESSAGES.WEBSITE_REQUIRED })
  //   .refine(
  //     (val) => {
  //       const urlPattern =
  //         /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?$/;
  //       return urlPattern.test(val);
  //     },
  //     {
  //       message: VALIDATION_MESSAGES.WEBSITE_INVALID,
  //     }
  //   ),

  company_logo_url: z
    .string({
      required_error: VALIDATION_MESSAGES.COMPANY_LOGO_URL_REQUIRED,
    })
    .min(1, { message: VALIDATION_MESSAGES.COMPANY_LOGO_URL_REQUIRED })
    .url({ message: VALIDATION_MESSAGES.LOGO_URL_INVALID })
    .max(VALIDATION_LIMITS.URL, {
      message: VALIDATION_MESSAGES.LOGO_URL_TOO_LONG,
    }),

  login_greeting_text_h1: z
    .string({
      required_error: VALIDATION_MESSAGES.LOGIN_H1_REQUIRED,
    })
    .min(1, { message: VALIDATION_MESSAGES.LOGIN_H1_REQUIRED })
    .max(VALIDATION_LIMITS.SHORT_TEXT, {
      message: VALIDATION_MESSAGES.LOGIN_H1_TOO_LONG,
    }),

  login_greeting_text_h2: z
    .string({
      required_error: VALIDATION_MESSAGES.LOGIN_H2_REQUIRED,
    })
    .min(1, { message: VALIDATION_MESSAGES.LOGIN_H2_REQUIRED })
    .max(VALIDATION_LIMITS.MEDIUM_TEXT, {
      message: VALIDATION_MESSAGES.LOGIN_H2_TOO_LONG,
    }),

  homepage_greeting_text: z
    .string({
      required_error: VALIDATION_MESSAGES.HOMEPAGE_GREETING_REQUIRED,
    })
    .min(1, { message: VALIDATION_MESSAGES.HOMEPAGE_GREETING_REQUIRED })
    .max(VALIDATION_LIMITS.LONG_TEXT, {
      message: VALIDATION_MESSAGES.HOMEPAGE_GREETING_TOO_LONG,
    }),

  onboarding_countdown: z
    .number()
    .min(1, { message: VALIDATION_MESSAGES.ONBOARDING_COUNTDOWN_MIN })
    .max(VALIDATION_LIMITS.MAX_DAYS, {
      message: VALIDATION_MESSAGES.ONBOARDING_COUNTDOWN_MAX,
    }),
});

/**
 * Type inference for the general settings form schema
 */
export type GeneralFormValues = z.infer<typeof GeneralFormSchema>;

export default GeneralFormSchema;
