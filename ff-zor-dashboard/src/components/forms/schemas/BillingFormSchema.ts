import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const BillingFormSchema = z.object({
  legal_business_name: z
    .string({
      required_error: VALIDATION_MESSAGES.BUSINESS_NAME_REQUIRED,
    })
    .min(1, {
      message: VAL<PERSON>ATION_MESSAGES.BUSINESS_NAME_REQUIRED,
    }),
  email: z
    .string({
      required_error: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .email({
      message: VALIDATION_MESSAGES.EMAIL_INVALID,
    }),
  phone: z
    .string({
      required_error: VALIDATION_MESSAGES.PHONE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.PHONE_REQUIRED,
    }),
  postal_code: z
    .string({
      required_error: VALID<PERSON>ION_MESSAGES.POSTAL_CODE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.POSTAL_CODE_REQUIRED,
    }),
});

export default BillingFormSchema;
