import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

// Phone number validation regex (US format to match FormInput formatting)
// Matches: (************* or (************* ext.123
const phoneRegex = /^\([0-9]{3}\) [0-9]{3}-[0-9]{4}( ext\.[0-9]+)?$/;

// URL validation regex
const urlRegex =
  /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/;

// LinkedIn profile URL validation regex
const linkedinRegex =
  /^(https?:\/\/)?(www\.)?linkedin\.com\/in\/[a-zA-Z0-9\-_]+\/?$/;

export const LeadershipUserFormSchema = z.object({
  full_name: z
    .string({
      required_error: VALIDATION_MESSAGES.FULL_NAME_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.FULL_NAME_REQUIRED,
    }),
  position: z
    .string({
      required_error: VALIDATION_MESSAGES.POSITION_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.POSITION_REQUIRED,
    }),
  city: z
    .string({
      required_error: VALIDATION_MESSAGES.CITY_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.CITY_REQUIRED,
    }),
  phone: z
    .string({
      required_error: VALIDATION_MESSAGES.PHONE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.PHONE_REQUIRED,
    })
    .regex(phoneRegex, {
      message: 'Please enter a valid phone number',
    }),
  email: z
    .string({
      required_error: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .email({ message: VALIDATION_MESSAGES.EMAIL_INVALID }),
  meeting_link: z
    .string({
      required_error: VALIDATION_MESSAGES.MEETING_LINK_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.MEETING_LINK_REQUIRED,
    })
    .regex(urlRegex, {
      message: 'Please enter a valid meeting link URL',
    }),
  linkedin: z
    .string({
      required_error: VALIDATION_MESSAGES.LINKEDIN_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.LINKEDIN_REQUIRED,
    })
    .regex(linkedinRegex, {
      message:
        'Please enter a valid LinkedIn profile URL (e.g., https://linkedin.com/in/username)',
    }),
  preferred_contact: z
    .string({
      required_error: VALIDATION_MESSAGES.PREFERRED_CONTACT_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.PREFERRED_CONTACT_REQUIRED,
    }),
  is_public: z.boolean(),
});
