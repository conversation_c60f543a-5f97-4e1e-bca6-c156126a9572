import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

export const Fran<PERSON>seesFormSchema = z.object({
  name: z
    .string({
      required_error: VALIDATION_MESSAGES.NAME_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.NAME_REQUIRED,
    }),
  email: z
    .string({
      required_error: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .email({
      message: VALIDATION_MESSAGES.EMAIL_INVALID,
    }),
  phone: z
    .string({
      required_error: VALIDATION_MESSAGES.PHONE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.PHONE_REQUIRED,
    }),
  location: z
    .string({
      required_error: VALIDATION_MESSAGES.LOCATION_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.LOCATION_REQUIRED,
    }),
});
