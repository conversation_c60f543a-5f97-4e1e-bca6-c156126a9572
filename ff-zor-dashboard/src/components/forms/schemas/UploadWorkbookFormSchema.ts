import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const UploadWorkbookFormSchema = z.object({
  file: z
    .instanceof(File)
    .refine((value) => value !== null, {
      message: VALIDATION_MESSAGES.WORKBOOK_FILE_REQUIRED,
    })
    .refine(
      (file) => {
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
          '.xlsx',
          '.xls',
        ];
        return (
          allowedTypes.includes(file.type) ||
          file.name.endsWith('.xlsx') ||
          file.name.endsWith('.xls')
        );
      },
      {
        message: VALIDATION_MESSAGES.WORKBOOK_FILE_TYPE_INVALID,
      }
    ),
  file_name: z
    .string({
      required_error: VALIDATION_MESSAGES.TITLE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.TITLE_REQUIRED,
    }),
});

export default UploadWorkbookFormSchema;
