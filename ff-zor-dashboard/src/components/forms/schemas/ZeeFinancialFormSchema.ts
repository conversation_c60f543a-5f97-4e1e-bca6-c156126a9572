import { z } from 'zod';

export const Z<PERSON>FinancialFormSchema = z.object({
  employment: z.string().min(1, {
    message: "Employment can't be empty",
  }),
  annual_compensation: z.number().min(1, {
    message: "Annual Compensation can't be empty",
  }),
  spouse_significant_other_name: z.string().optional(),
  spouse_significant_other_employment: z.string().optional(),
  spouse_significant_other_annual_compensation: z.number().optional(),
  spouse_significant_other_phone: z.string().optional(),
  is_veteran: z.string().min(1, {
    message: "Veteran Status can't be empty",
  }),
  is_first_responder_and_medical_professional: z.string().min(1, {
    message: "First Responder and Medical Professional can't be empty",
  }),
  credit_score_range: z.string().min(1, {
    message: "Credit Score Range can't be empty",
  }),
  is_sba_eligible: z.string().min(1, {
    message: "SBA Eligible can't be empty",
  }),
  owner_type: z.array(z.string()).min(1, {
    message: 'Please select at least one owner type',
  }),
  plan_to_have_investor_or_partner: z.string().min(1, {
    message: "Plan to Have Investor or Partner can't be empty",
  }),
  investor_partner_description: z.string().optional(),
});
