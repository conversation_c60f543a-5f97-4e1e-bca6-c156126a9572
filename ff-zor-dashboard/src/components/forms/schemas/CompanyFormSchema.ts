import { z } from 'zod';

import { VALIDATION_LIMITS, VAL<PERSON>ATION_MESSAGES } from '@/constants/validation';

/**
 * Schema for company form validation
 * @see CompanyDetailsForm
 */
export const CompanyFormSchema = z.object({
  name: z
    .string({
      required_error: VALIDATION_MESSAGES.COMPANY_NAME_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.COMPANY_NAME_REQUIRED,
    })
    .max(VALIDATION_LIMITS.SHORT_TEXT, {
      message: VALIDATION_MESSAGES.COMPANY_NAME_TOO_LONG,
    }),
  logo_url: z
    .string({
      required_error: VALIDATION_MESSAGES.COMPANY_LOGO_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.COMPANY_LOGO_REQUIRED,
    })
    .url({
      message: VAL<PERSON>ATION_MESSAGES.URL_INVALID,
    }),
  headquarter_location: z
    .string({
      required_error: VALIDATION_MESSAGES.HEADQUARTER_LOCATION_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.HEADQUARTER_LOCATION_REQUIRED,
    })
    .max(VALIDATION_LIMITS.MEDIUM_TEXT, {
      message: VALIDATION_MESSAGES.HEADQUARTER_LOCATION_TOO_LONG,
    }),
  franchise_since: z
    .string({
      required_error: VALIDATION_MESSAGES.FRANCHISE_SINCE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.FRANCHISE_SINCE_REQUIRED,
    })
    .refine(
      (val) => {
        const year = parseInt(val);
        return (
          year >= VALIDATION_LIMITS.MIN_YEAR &&
          year <= VALIDATION_LIMITS.MAX_YEAR
        );
      },
      {
        message: VALIDATION_MESSAGES.YEAR_INVALID(
          VALIDATION_LIMITS.MIN_YEAR,
          VALIDATION_LIMITS.MAX_YEAR
        ),
      }
    ),
  industry: z
    .string({
      required_error: VALIDATION_MESSAGES.INDUSTRY_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.INDUSTRY_REQUIRED,
    }),
  number_of_units: z
    .number()
    .min(1, {
      message: VALIDATION_MESSAGES.UNITS_MIN,
    })
    .max(100000, {
      message: VALIDATION_MESSAGES.UNITS_MAX,
    }),
  website: z
    .string({
      required_error: VALIDATION_MESSAGES.WEBSITE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.WEBSITE_REQUIRED,
    })
    .refine(
      (val) => {
        const urlPattern =
          /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?$/;
        return urlPattern.test(val);
      },
      {
        message: VALIDATION_MESSAGES.WEBSITE_INVALID,
      }
    ),
  fdd_renewal_date: z.date({
    required_error: VALIDATION_MESSAGES.FDD_RENEWAL_DATE_REQUIRED,
    invalid_type_error: VALIDATION_MESSAGES.DATE_INVALID,
  }),
});

/**
 * Type inference for the company form schema
 */
export type CompanyFormValues = z.infer<typeof CompanyFormSchema>;

export default CompanyFormSchema;
