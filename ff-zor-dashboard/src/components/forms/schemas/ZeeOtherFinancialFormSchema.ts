import { z } from 'zod';

export const <PERSON><PERSON><PERSON>therFinancialFormSchema = z.object({
  cash_or_equivalent_assets: z.number().optional(),
  savings_or_certificates: z.number().optional(),
  stocks_or_bonds_or_securities: z.number().optional(),
  retirement_plan_or_ira: z.number().optional(),
  home_market_value: z.number().optional(),
  other_real_estate: z.number().optional(),
  autos: z.number().optional(),
  other_assets: z.number().optional(),
  other_assets_description: z.string().optional().nullable(),
  total_assets: z.number().optional(),
  home_mortgage_balance: z.number().optional(),
  other_real_estate_debt: z.number().optional(),
  auto_loans: z.number().optional(),
  other_debt: z.number().optional(),
  other_debt_description: z.string().optional().nullable(),
  total_liabilities: z.number().optional(),
  net_worth: z.number().optional(),
});
