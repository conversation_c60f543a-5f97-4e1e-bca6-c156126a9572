import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const <PERSON>eeProfileFormSchema = z.object({
  full_name: z
    .string()
    .min(1, { message: VALIDATION_MESSAGES.FULL_NAME_REQUIRED }),
  email: z.string().email({ message: VALIDATION_MESSAGES.EMAIL_INVALID }),
  birth_date: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip_code: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  meeting_link: z.string().optional(),
  linkedin: z.string().optional(),
  preferred_markets: z.array(z.string()).optional(),
});

export default ZeeProfileFormSchema;
