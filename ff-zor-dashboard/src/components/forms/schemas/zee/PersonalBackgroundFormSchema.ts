import { z } from 'zod';

export const PersonalBackgroundFormSchema = z.object({
  has_bankruptcy: z.string().min(1, {
    message: "Bankruptcy can't be empty",
  }),
  has_criminal_conviction: z.string().min(1, {
    message: "Criminal Conviction can't be empty",
  }),
  has_pending_lawsuits: z.string().min(1, {
    message: "Pending Lawsuits can't be empty",
  }),
  has_civil_judgments: z.string().min(1, {
    message: "Civil Judgments can't be empty",
  }),
  affirmative_answers_explanation: z.string().optional().nullable(),
});
