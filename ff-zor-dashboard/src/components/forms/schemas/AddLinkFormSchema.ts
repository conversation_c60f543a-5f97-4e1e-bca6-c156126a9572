import { z } from 'zod';

const AddLinkFormSchema = z.object({
  title: z
    .string({
      required_error: 'Title is required',
    })
    .min(1, { message: 'Title is required' }),
  link: z
    .string({
      required_error: 'Link is required',
    })
    .min(1, { message: 'Link is required' })
    .refine(
      (val) => /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(\/[^\s]*)?$/.test(val),
      {
        message: 'Link must be a valid URL',
      }
    ),
  franchisor_dashboard: z.boolean().default(false),
  consultant_dashboard: z.boolean().default(false),
  franchisee_dashboard: z.boolean().default(false),
});

export default AddLinkFormSchema;
