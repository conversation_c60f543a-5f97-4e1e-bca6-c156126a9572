import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const PaymentFormSchema = z.object({
  company_id: z.string().optional(),
  card_number: z
    .string({
      required_error: VALIDATION_MESSAGES.CARD_NUMBER_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.CARD_NUMBER_REQUIRED,
    }),
  expiration_date: z
    .string({
      required_error: VALIDATION_MESSAGES.EXPIRATION_DATE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.EXPIRATION_DATE_REQUIRED,
    })
    .refine(
      (value) => {
        const regex = /^(0[1-9]|1[0-2])\/\d{2}$/;
        return regex.test(value);
      },
      {
        message: VALIDATION_MESSAGES.EXPIRATION_DATE_INVALID,
      }
    ),
  security_code: z
    .string({
      required_error: VALIDATION_MESSAGES.SECURITY_CODE_REQUIRED,
    })
    .length(3, {
      message: VALIDATION_MESSAGES.SECURITY_CODE_INVALID,
    }),
});

export const CardListFormSchema = z.object({
  cardData: z.array(PaymentFormSchema),
});

export default PaymentFormSchema;
