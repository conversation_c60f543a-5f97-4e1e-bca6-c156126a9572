import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const ResetPasswordFormSchema = z
  .object({
    old_password: z
      .string({
        required_error: VALIDATION_MESSAGES.OLD_PASSWORD_REQUIRED,
      })
      .min(8, { message: VAL<PERSON>ATION_MESSAGES.OLD_PASSWORD_TOO_SHORT }),
    new_password: z
      .string({
        required_error: VALIDATION_MESSAGES.NEW_PASSWORD_REQUIRED,
      })
      .min(8, { message: VALIDATION_MESSAGES.NEW_PASSWORD_TOO_SHORT }),
    confirm_new_password: z
      .string({
        required_error: VAL<PERSON>ATION_MESSAGES.CONFIRM_PASSWORD_REQUIRED,
      })
      .min(8, {
        message: VAL<PERSON>ATION_MESSAGES.CONFIRM_PASSWORD_TOO_SHORT,
      }),
  })
  .refine((data) => data.new_password === data.confirm_new_password, {
    message: VAL<PERSON>ATION_MESSAGES.PASSWORDS_MUST_MATCH,
    path: ['confirm_new_password'],
  });

export default ResetPasswordFormSchema;
