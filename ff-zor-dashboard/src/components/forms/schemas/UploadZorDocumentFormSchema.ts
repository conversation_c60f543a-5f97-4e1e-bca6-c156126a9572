import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const ACCEPTED_FILE_TYPES = [
  'image/png',
  'image/jpeg',
  'image/gif',
  'image/webp',
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];

const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB in bytes

const UploadZorDocumentFormSchema = z.object({
  file: z
    .instanceof(File)
    .refine((value) => value !== null, {
      message: VALIDATION_MESSAGES.DOCUMENT_FILE_REQUIRED,
    })
    .refine((file) => file.size <= MAX_FILE_SIZE, {
      message: VALIDATION_MESSAGES.FILE_SIZE_TOO_LARGE,
    })
    .refine((file) => ACCEPTED_FILE_TYPES.includes(file.type), {
      message: VALIDATION_MESSAGES.FILE_TYPE_INVALID,
    }),
  file_name: z
    .string({
      required_error: VALIDATION_MESSAGES.TITLE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.TITLE_REQUIRED,
    }),
  for_stage: z.boolean().optional(),
  for_franchisee: z.boolean().optional(),
  for_consultant: z.boolean().optional(),
});

export default UploadZorDocumentFormSchema;
