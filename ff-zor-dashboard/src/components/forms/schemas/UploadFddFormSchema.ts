import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

const UploadFddFormSchema = z.object({
  file: z.instanceof(File).refine((value) => value !== null, {
    message: VALIDATION_MESSAGES.FDD_FILE_REQUIRED,
  }),
  file_name: z
    .string({
      required_error: VALIDATION_MESSAGES.TITLE_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.TITLE_REQUIRED,
    }),
});

export default UploadFddFormSchema;
