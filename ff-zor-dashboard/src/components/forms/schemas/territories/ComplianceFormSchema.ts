import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

export const ComplianceFormSchema = z.object({
  complianceData: z.array(
    z.object({
      id: z.string().optional(),
      location: z
        .string({
          required_error: VALIDATION_MESSAGES.LOCATION_REQUIRED,
        })
        .min(1, {
          message: VALIDATION_MESSAGES.LOCATION_REQUIRED,
        }),
      compliance: z.boolean().optional(),
      status: z.boolean().optional(),
    })
  ),
});
