import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

/**
 * Schema for validating territory color entries
 */
export const TerritoryColorsFormSchema = z.object({
  sold: z
    .string({
      required_error: VALIDATION_MESSAGES.SOLD_COLOR_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.SOLD_COLOR_REQUIRED,
    })
    .regex(/^#[0-9A-Fa-f]{6}$/, {
      message: VALIDATION_MESSAGES.COLOR_FORMAT_INVALID,
    }),
  liked: z
    .string({
      required_error: VALIDATION_MESSAGES.LIKED_COLOR_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.LIKED_COLOR_REQUIRED,
    })
    .regex(/^#[0-9A-Fa-f]{6}$/, {
      message: VALIDATION_MESSAGES.COLOR_FORMAT_INVALID,
    }),
  reserved: z
    .string({
      required_error: VAL<PERSON>ATION_MESSAGES.RESERVED_COLOR_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.RESERVED_COLOR_REQUIRED,
    })
    .regex(/^#[0-9A-Fa-f]{6}$/, {
      message: VALIDATION_MESSAGES.COLOR_FORMAT_INVALID,
    }),
  available: z
    .string({
      required_error: VALIDATION_MESSAGES.AVAILABLE_COLOR_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.AVAILABLE_COLOR_REQUIRED,
    })
    .regex(/^#[0-9A-Fa-f]{6}$/, {
      message: VALIDATION_MESSAGES.COLOR_FORMAT_INVALID,
    }),
  unavailable: z
    .string({
      required_error: VALIDATION_MESSAGES.UNAVAILABLE_COLOR_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.UNAVAILABLE_COLOR_REQUIRED,
    })
    .regex(/^#[0-9A-Fa-f]{6}$/, {
      message: VALIDATION_MESSAGES.COLOR_FORMAT_INVALID,
    }),
  pending: z
    .string({
      required_error: VALIDATION_MESSAGES.PENDING_COLOR_REQUIRED,
    })
    .min(1, {
      message: VALIDATION_MESSAGES.PENDING_COLOR_REQUIRED,
    })
    .regex(/^#[0-9A-Fa-f]{6}$/, {
      message: VALIDATION_MESSAGES.COLOR_FORMAT_INVALID,
    }),
});

/**
 * Type inference for the territory colors form values
 */
export type TerritoryColorsFormValues = z.infer<
  typeof TerritoryColorsFormSchema
>;
