import { z } from 'zod';

import { VALIDATION_MESSAGES } from '@/constants/validation';

export const TerritoriesFormSchema = z.object({
  territoriesData: z.array(
    z.object({
      id: z.string().optional(),
      location: z
        .string({
          required_error: VALIDATION_MESSAGES.LOCATION_REQUIRED,
        })
        .min(1, {
          message: VALIDATION_MESSAGES.LOCATION_REQUIRED,
        }),
      status: z.boolean().optional(),
      job_title: z
        .string({
          required_error: VALIDATION_MESSAGES.JOB_TITLE_REQUIRED,
        })
        .min(1, {
          message: VALIDATION_MESSAGES.JOB_TITLE_REQUIRED,
        }),
      net_worth: z.number().min(1, {
        message: VALIDATION_MESSAGES.NET_WORTH_REQUIRED,
      }),
      age_range_start: z.number().min(1, {
        message: VALIDATION_MESSAGES.AGE_RANGE_START_REQUIRED,
      }),
      age_range_end: z.number().min(1, {
        message: VAL<PERSON>ATION_MESSAGES.AGE_RANGE_END_REQUIRED,
      }),
      average_income_start: z.number().min(1, {
        message: VALIDATION_MESSAGES.INCOME_START_REQUIRED,
      }),
      average_income_end: z.number().min(1, {
        message: VALIDATION_MESSAGES.INCOME_END_REQUIRED,
      }),
      population: z.number().min(1, {
        message: VALIDATION_MESSAGES.POPULATION_REQUIRED,
      }),
      gender: z
        .string({
          required_error: VALIDATION_MESSAGES.GENDER_REQUIRED,
        })
        .min(1, {
          message: VALIDATION_MESSAGES.GENDER_REQUIRED,
        }),
      data: z
        .string({
          required_error: VALIDATION_MESSAGES.DATA_REQUIRED,
        })
        .min(1, {
          message: VALIDATION_MESSAGES.DATA_REQUIRED,
        }),
    })
  ),
});
