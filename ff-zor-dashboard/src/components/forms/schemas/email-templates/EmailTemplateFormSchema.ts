import { z } from 'zod';

/**
 * Schema for email template form validation
 * @see EmailTemplateForm
 */
export const EmailTemplateFormSchema = z.object({
  id: z.string().min(1, { message: 'ID is required' }),
  name: z.string().min(1, { message: 'Name is required' }),
  template_subject: z
    .string()
    .min(1, { message: 'Template subject is required' }),
  template_html: z.string().min(1, { message: 'Template html is required' }),
  template_content: z
    .string()
    .min(1, { message: 'Template content is required' }),
  active: z.boolean().default(true),
});

/**
 * Type inference for the email template form schema
 */
export type EmailTemplateFormValues = z.infer<typeof EmailTemplateFormSchema>;

export default EmailTemplateFormSchema;
