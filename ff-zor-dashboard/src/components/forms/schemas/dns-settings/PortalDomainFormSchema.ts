import { z } from 'zod';

/**
 * Schema for root domain form validation
 * @see RootDomainForm
 */
export const PortalDomainFormSchema = z.object({
  subdomain: z.string().min(1, { message: 'Subdomain is required' }),
  domain: z.string().min(1, { message: 'Domain is required' }),
});

/**
 * Type inference for the root domain form schema
 */
export type PortalDomainFormValues = z.infer<typeof PortalDomainFormSchema>;

export default PortalDomainFormSchema;
