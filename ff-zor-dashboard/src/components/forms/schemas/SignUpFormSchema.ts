import { z } from 'zod';

import { VAL<PERSON>ATION_MESSAGES } from '@/constants/validation';

const SignUpFormSchema = z.object({
  email: z
    .string({
      required_error: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .email({
      message: VALIDATION_MESSAGES.EMAIL_INVALID,
    }),
  password: z
    .string({
      required_error: VALIDATION_MESSAGES.PASSWORD_REQUIRED,
    })
    .min(8, {
      message: VALIDATION_MESSAGES.PASSWORD_TOO_SHORT,
    }),
  terms: z.boolean().refine((v) => v, {
    message: VALIDATION_MESSAGES.TERMS_REQUIRED,
  }),
});

export default SignUpFormSchema;
