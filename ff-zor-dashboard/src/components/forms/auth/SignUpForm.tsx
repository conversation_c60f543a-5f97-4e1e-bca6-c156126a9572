import { useSignUp } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';

import SignUpFormSchema from '@/components/forms/schemas/SignUpFormSchema';
import FormCheckbox from '@/components/global/form-inputs/FormCheckbox';
import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { SIGN_UP_ERROR_MESSAGE } from '@/constants/strings';
import { toast } from '@/hooks/ui/use-toast';

type SignUpFormValues = z.infer<typeof SignUpFormSchema>;

interface SignUpFormProps {
  customSubmit?: (values: SignUpFormValues) => Promise<void>;
  defaultEmail?: string;
}

const SignUpForm = ({ customSubmit, defaultEmail }: SignUpFormProps) => {
  const form = useForm<SignUpFormValues>({
    resolver: zodResolver(SignUpFormSchema),
    defaultValues: {
      email: defaultEmail || '',
      password: '',
      terms: false,
    },
  });

  const [loading, setLoading] = useState(false);
  const [expired, setExpired] = useState(false);
  const [verified, setVerified] = useState(false);

  const navigate = useNavigate();
  const { isLoaded, signUp } = useSignUp();

  useEffect(() => {
    if (expired) {
      toast({
        variant: 'destructive',
        title: 'Email link has been expired',
        description: 'Please try again',
      });
    }
  }, [expired]);

  useEffect(() => {
    if (verified) {
      toast({
        title: 'Email link has been verified',
        description: 'Please switch the tab to continue',
      });
    }
  }, [verified]);

  const handleSignUp = async (values: SignUpFormValues) => {
    if (!isLoaded) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again later',
      });
      return;
    }

    if (customSubmit) {
      setLoading(true);
      await customSubmit(values);
      return;
    }

    const { startEmailLinkFlow } = signUp.createEmailLinkFlow();

    setLoading(true);
    try {
      await signUp?.create({
        emailAddress: values.email,
        password: values.password,
      });

      toast({
        title: 'Email verification link has been sent',
        description: 'Please check your email',
      });

      const response = await startEmailLinkFlow({
        redirectUrl: `${window.location.origin}/auth/confirm`,
      });

      const verification = response.verifications.emailAddress;
      if (verification.verifiedFromTheSameClient()) {
        setVerified(true);
        setLoading(false);
        return;
      }

      if (verification.status === 'expired') {
        setExpired(true);
        setLoading(false);
        return;
      }

      if (response.status === 'complete') {
        setLoading(false);
        navigate('/auth/sign-in');
        return;
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : SIGN_UP_ERROR_MESSAGE;

      toast({
        variant: 'destructive',
        title: 'Sign Up Failed',
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSignUp)}
        className="w-full space-y-4"
      >
        <FormInput
          control={form.control}
          name="email"
          type="email"
          label="Email"
          readOnly={!!defaultEmail}
          placeholder="Email"
        />
        <FormInput
          control={form.control}
          name="password"
          type="password"
          label="Password"
          placeholder="Password"
        />
        <FormCheckbox
          control={form.control}
          name="terms"
          label="I agree to the Terms of Use and Privacy Policy"
        />
        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? <Loader2 className="size-4 animate-spin" /> : 'Sign Up'}
        </Button>
      </form>
    </Form>
  );
};

export default SignUpForm;
