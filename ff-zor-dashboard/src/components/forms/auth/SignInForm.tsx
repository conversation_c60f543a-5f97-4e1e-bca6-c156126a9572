import { useSignIn } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { toast } from '@/hooks/ui/use-toast';

const signInFormSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' }),
});

type SignInFormValues = z.infer<typeof signInFormSchema>;

const SignInForm = () => {
  const form = useForm<SignInFormValues>({
    resolver: zodResolver(signInFormSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { isLoaded, signIn, setActive } = useSignIn();

  const handleSignIn = async (values: SignInFormValues) => {
    if (!isLoaded) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again later',
      });
      return;
    }

    setLoading(true);
    try {
      const response = await signIn.create({
        identifier: values.email,
        password: values.password,
      });

      if (response.status === 'complete') {
        toast({
          title: 'Sign In Success',
        });

        await setActive({ session: response.createdSessionId });
        navigate('/dashboard');
      } else {
        toast({
          variant: 'destructive',
          title: 'Sign In Failed',
          description: 'Please check your credentials.',
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message.includes('verification strategy')
            ? 'Email is associated with Google Authentication - please login through Continue with Google'
            : error.message
          : 'An error occurred during sign in';

      toast({
        variant: 'destructive',
        title: 'Sign In Failed',
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSignIn)}
        className="w-full space-y-4"
      >
        <div className="space-y-4">
          <FormInput
            control={form.control}
            name="email"
            type="email"
            label="Email"
            placeholder="Email"
          />
          <div className="relative">
            <FormInput
              control={form.control}
              name="password"
              type="password"
              label="Password"
              placeholder="Password"
            />
            <Link
              to="/auth/forget-password"
              className="as-body-03 absolute right-0 top-0 text-muted-foreground underline hover:text-black"
            >
              Forgot your password?
            </Link>
          </div>
        </div>
        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? <Loader2 className="size-4 animate-spin" /> : 'Sign In'}
        </Button>
      </form>
    </Form>
  );
};

export default SignInForm;
