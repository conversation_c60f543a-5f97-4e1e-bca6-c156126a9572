import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import FormInput from '@/components/global/form-inputs/FormInput';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import FormSwitch from '@/components/global/form-inputs/FormSwitch';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { linkOptions } from '@/constants/options';
import { useUpdateStageItem } from '@/hooks/stage-items/useUpdateStageItem';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useStageItemStore from '@/stores/stages/useStageItemStore';
import { StageItem } from '@/types/stage.item.type';

import { StageItemForm } from './AddStageItemForm';
import { StageItemFormSchema } from './AddStageItemForm';
import { RenderOption } from './RenderOption';

type StageItemProps = {
  data?: StageItem;
  stage_id: string;
  setOpen: (open: boolean) => void;
};

const EditStageItemForm = ({ data, stage_id, setOpen }: StageItemProps) => {
  const form = useForm<StageItemForm>({
    resolver: zodResolver(StageItemFormSchema),
    defaultValues: {
      ...data,
      is_pipeline: data?.is_pipeline || false,
      external_link: data?.external_link || undefined,
      internal_tab: data?.internal_tab || undefined,
      document_id: data?.document_id || undefined,
      aimei_prompt_id: data?.aimei_prompt_id || undefined,
    },
  });

  const { updateStageItem: updateStageItemToStore } = useStageItemStore();

  const {
    mutate: updateStageItem,
    result: updateStageItemResult,
    isError: isUpdateStageItemError,
    isSuccess: isUpdateStageItemSuccess,
    isLoading: isUpdateStageItemLoading,
  } = useUpdateStageItem();

  const handleCancel = () => {
    setOpen(false);
  };

  const onSubmit = (values: StageItemForm) => {
    const submitData: StageItem = {
      ...values,
      id: data?.id,
      type: values.type as StageItem['type'],
      stage_id,
      is_additional_stage: false,
      previous_stage_item_id: data?.previous_stage_item_id,
      external_link: [
        'video-link',
        'meeting-link',
        'document-sign',
        'external-web-link',
      ].includes(values.type as string)
        ? values.external_link || null
        : null,
      internal_tab: ['link-to-tab'].includes(values.type as string)
        ? values.internal_tab || null
        : null,
      document_id: ['view-document'].includes(values.type as string)
        ? values.document_id || null
        : null,
      aimei_prompt_id: ['ask-aimei'].includes(values.type as string)
        ? values.aimei_prompt_id || null
        : null,
    };

    updateStageItem(submitData);
  };

  useEffect(() => {
    if (isUpdateStageItemError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isUpdateStageItemError]);

  useEffect(() => {
    if (isUpdateStageItemSuccess) {
      toast({
        title: 'Stage item updated successfully',
      });

      setOpen(false);
      updateStageItemToStore(
        updateStageItemResult?.data?.id as string,
        updateStageItemResult?.data as StageItem
      );
    }
  }, [
    updateStageItemResult,
    isUpdateStageItemSuccess,
    setOpen,
    updateStageItemToStore,
  ]);

  return (
    <Form {...form}>
      <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex w-full items-start justify-between gap-2 rounded-md">
          <div className="w-full space-y-3 rounded-lg bg-white p-5">
            <div className="flex items-start gap-2">
              <FormInput
                name="title"
                label="Title"
                control={form.control}
                className={cn(
                  form.watch('type') == 'upload' || !form.watch('type')
                    ? 'w-4/5'
                    : 'w-2/5'
                )}
              />
              <FormSelect
                name="type"
                label="Type"
                options={linkOptions}
                control={form.control}
                className="w-1/5"
              />
              <RenderOption form={form} className="w-2/5" />
            </div>
            <div className="flex items-start gap-2">
              <FormTextArea
                name="description"
                label="Add description"
                control={form.control}
                className="w-4/5"
                placeholder="description"
              />
              <FormSwitch
                name="is_pipeline"
                control={form.control}
                className="mt-8 w-1/5"
                custom_label="Pipeline"
              />
            </div>
            <div className={`flex w-full justify-end space-x-2`}>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={isUpdateStageItemLoading}>
                {isUpdateStageItemLoading && (
                  <Loader2 className="size-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default EditStageItemForm;
