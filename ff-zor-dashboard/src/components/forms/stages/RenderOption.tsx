import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import AddStagePromptDialog from '@/components/dialogs/AddStagePromptDialog';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Button } from '@/components/ui/button';
import { tabOptions } from '@/constants/options';
import { cn } from '@/lib/ui/utils';
import useStagePromptsStore from '@/stores/useStagePromptsStore';
import useZorDocumentStore from '@/stores/useZorDocumentStore';

import { StageItemForm } from './AddStageItemForm';

type RenderOptionProps = {
  form: UseFormReturn<StageItemForm>;
  className: string;
};

export const RenderOption = ({ form, className }: RenderOptionProps) => {
  const value = form.watch('type');

  const { zorDocuments } = useZorDocumentStore();
  const { stagePrompts } = useStagePromptsStore();

  const [isAddPromptOpen, setIsAddPromptOpen] = useState(false);

  return (
    <>
      <FormInput
        name="external_link"
        label="Link"
        control={form.control}
        className={cn(
          className,
          ![
            'video-link',
            'meeting-link',
            'document-sign',
            'external-web-link',
          ].includes(value) && 'hidden'
        )}
        placeholder="Link"
      />
      <FormSelect
        name="internal_tab"
        label="Tab"
        options={tabOptions}
        control={form.control}
        className={cn(className, !['link-to-tab'].includes(value) && 'hidden')}
      />
      <FormSelect
        name="document_id"
        label="Document"
        options={
          zorDocuments?.map((document) => ({
            label: document.name,
            value: document.id,
          })) || []
        }
        control={form.control}
        className={cn(
          className,
          !['view-document'].includes(value) && 'hidden'
        )}
      />
      <FormSelect
        name="aimei_prompt_id"
        label="Prompt"
        options={
          stagePrompts?.map((prompt) => ({
            label: prompt.title,
            value: prompt.id,
          })) || []
        }
        control={form.control}
        className={cn(className, !['ask-aimei'].includes(value) && 'hidden')}
        footer={
          ['ask-aimei'].includes(value) ? (
            <Button
              type="button"
              size="sm"
              className="w-full"
              onClick={() => setIsAddPromptOpen(true)}
            >
              Add new prompt
            </Button>
          ) : null
        }
      />

      <AddStagePromptDialog
        open={isAddPromptOpen}
        setOpen={setIsAddPromptOpen}
      />
    </>
  );
};
