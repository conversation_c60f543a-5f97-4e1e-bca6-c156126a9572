import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { Loader2 } from 'lucide-react';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useCreateStage } from '@/hooks/stages/useCreateStage';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useStageStore from '@/stores/stages/useStageStore';
import { Stage } from '@/types/stage.type';

const AddStageFormSchema = z.object({
  name: z.string().min(1, {
    message: "Name can't be empty",
  }),
});

interface AddStageFormProps {
  isFirstStage: boolean;
  setNewStageOpen: (open: boolean) => void;
  setSelectedStage: React.Dispatch<React.SetStateAction<Stage | null>>;
}

export const AddStageForm = ({
  isFirstStage,
  setNewStageOpen,
  setSelectedStage,
}: AddStageFormProps) => {
  const form = useForm<z.infer<typeof AddStageFormSchema>>({
    resolver: zodResolver(AddStageFormSchema),
    defaultValues: {
      name: '',
    },
  });

  const { stageList, addStage } = useStageStore();
  const {
    mutate: createStage,
    result: createStageResult,
    error: createStageError,
    isError: isCreateStageError,
    isSuccess: isCreateStageSuccess,
    isLoading: isCreateStageLoading,
  } = useCreateStage();

  const onSubmit = (values: { name: string }) => {
    createStage({
      name: values.name,
      index:
        stageList.length === 0 ? 1 : stageList[stageList.length - 1].index + 1,
    });
  };

  const handleCancel = () => {
    setNewStageOpen(false);
  };

  useEffect(() => {
    if (isCreateStageSuccess && createStageResult) {
      addStage(createStageResult.data);
      setSelectedStage(createStageResult.data);

      toast({
        title: 'Created Stage Succesfully',
      });

      setNewStageOpen(false);
    }
  }, [createStageResult, isCreateStageSuccess]);

  useEffect(() => {
    if (isCreateStageError) {
      if (createStageError instanceof AxiosError) {
        toast({
          title: createStageError.response?.data?.message,
          variant: 'destructive',
        });
      }
    }
  }, [createStageError, isCreateStageError]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className={cn('flex items-center gap-2', isFirstStage && 'ml-2')}>
          <FormInput
            control={form.control}
            name="name"
            type="text"
            placeholder="Stage Name"
          />
          <Button
            variant="outline"
            size={'sm'}
            onClick={handleCancel}
            disabled={isCreateStageLoading}
          >
            Cancel
          </Button>
          <Button size={'sm'} type="submit" disabled={isCreateStageLoading}>
            {isCreateStageLoading && (
              <Loader2 className="size-4 animate-spin" />
            )}{' '}
            Add
          </Button>
        </div>
      </form>
    </Form>
  );
};
