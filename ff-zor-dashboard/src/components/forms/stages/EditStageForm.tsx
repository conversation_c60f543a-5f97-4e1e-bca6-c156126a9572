import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { Loader2 } from 'lucide-react';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useUpdateStage } from '@/hooks/stages/useUpdateStage';
import { toast } from '@/hooks/ui/use-toast';
import useStageStore from '@/stores/stages/useStageStore';
import { Stage } from '@/types/stage.type';

const EditStageFormSchema = z.object({
  name: z.string().min(1, {
    message: "Name can't be empty",
  }),
});

interface EditStageFormProps {
  stage: Stage;
  setEditingStage: (stage: Stage | null) => void;
  setSelectedStage: React.Dispatch<React.SetStateAction<Stage | null>>;
}

export const EditStageForm = ({
  stage,
  setEditingStage,
  setSelectedStage,
}: EditStageFormProps) => {
  const form = useForm<z.infer<typeof EditStageFormSchema>>({
    resolver: zodResolver(EditStageFormSchema),
    defaultValues: {
      name: stage.name,
    },
  });

  const { updateStage } = useStageStore();
  const {
    mutate: updateStageApi,
    result: updateStageResult,
    error: updateStageError,
    isError: isUpdateStageError,
    isSuccess: isUpdateStageSuccess,
    isLoading: isUpdateStageLoading,
  } = useUpdateStage();

  const onSubmit = (values: { name: string }) => {
    updateStageApi({
      id: stage.id,
      name: values.name,
    });
  };

  const handleCancel = () => {
    setEditingStage(null);
  };

  useEffect(() => {
    if (isUpdateStageSuccess && updateStageResult) {
      updateStage(stage.id, updateStageResult.data);
      setSelectedStage(updateStageResult.data);

      toast({
        title: 'Updated Stage Successfully',
      });

      setEditingStage(null);
    }
  }, [
    updateStageResult,
    isUpdateStageSuccess,
    updateStage,
    stage.id,
    setSelectedStage,
    setEditingStage,
  ]);

  useEffect(() => {
    if (isUpdateStageError) {
      if (updateStageError instanceof AxiosError) {
        toast({
          title:
            updateStageError.response?.data?.message || 'Something went wrong',
          variant: 'destructive',
        });
      }
    }
  }, [updateStageError, isUpdateStageError]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex items-center gap-2">
          <FormInput
            control={form.control}
            name="name"
            type="text"
            placeholder="Stage Name"
          />
          <Button
            variant="outline"
            size={'sm'}
            onClick={handleCancel}
            disabled={isUpdateStageLoading}
          >
            Cancel
          </Button>
          <Button size={'sm'} type="submit" disabled={isUpdateStageLoading}>
            {isUpdateStageLoading && (
              <Loader2 className="size-4 animate-spin" />
            )}{' '}
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
};
