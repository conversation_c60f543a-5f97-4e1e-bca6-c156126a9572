import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import FormSwitch from '@/components/global/form-inputs/FormSwitch';
import FormTextArea from '@/components/global/form-inputs/FormTextArea';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { linkOptions } from '@/constants/options';
import { useCreateStageItem } from '@/hooks/stage-items/useCreateStageItem';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useStageItemStore from '@/stores/stages/useStageItemStore';
import { StageItem } from '@/types/stage.item.type';

import { RenderOption } from './RenderOption';

export const StageItemFormSchema = z
  .object({
    type: z.string().min(1, {
      message: 'Please select an item type',
    }),
    title: z.string().min(1, {
      message: 'Please enter a title',
    }),
    description: z.string().min(1, {
      message: 'Please enter a description',
    }),
    external_link: z.string().optional(),
    internal_tab: z.string().optional(),
    document_id: z.string().optional(),
    aimei_prompt_id: z.string().optional(),
    is_pipeline: z.boolean(),
  })
  .refine(
    (data) => {
      switch (data.type) {
        case 'document-sign':
        case 'video-link':
        case 'meeting-link':
        case 'external-web-link':
          return (
            data.external_link !== null && data.external_link !== undefined
          );
        default:
          return true;
      }
    },
    {
      message: 'Please enter a valid link',
      path: ['external_link'],
    }
  )
  .refine(
    (data) => {
      switch (data.type) {
        case 'link-to-tab':
          return data.internal_tab !== null && data.internal_tab !== undefined;
        default:
          return true;
      }
    },
    {
      message: 'Please select a tab',
      path: ['internal_tab'],
    }
  )
  .refine(
    (data) => {
      switch (data.type) {
        case 'view-document':
          return data.document_id !== null && data.document_id !== undefined;
        default:
          return true;
      }
    },
    {
      message: 'Please select a document',
      path: ['document_id'],
    }
  )
  .refine(
    (data) => {
      switch (data.type) {
        case 'ask-aimei':
          return (
            data.aimei_prompt_id !== null && data.aimei_prompt_id !== undefined
          );
        default:
          return true;
      }
    },
    {
      message: 'Please select a prompt',
      path: ['aimei_prompt_id'],
    }
  );

export type StageItemForm = z.infer<typeof StageItemFormSchema>;

type StageItemProps = {
  index: number;
  stage_id: string;
  previous_stage_item_id: string | null;
  setOpen: (open: boolean) => void;
};

export const AddStageItemForm = ({
  index,
  stage_id,
  previous_stage_item_id,
  setOpen,
}: StageItemProps) => {
  const form = useForm<StageItemForm>({
    resolver: zodResolver(StageItemFormSchema),
    defaultValues: {
      is_pipeline: false,
    },
  });

  const { createStageItem: createStageItemToStore } = useStageItemStore();

  const {
    mutate: createStageItem,
    result: createStageItemResult,
    isError: isCreateStageItemError,
    isSuccess: isCreateStageItemSuccess,
    isLoading: isCreateStageItemLoading,
  } = useCreateStageItem();

  const handleCancel = () => {
    setOpen(false);
  };

  const onSubmit = (values: StageItemForm) => {
    const submitData: StageItem = {
      ...values,
      type: values.type as StageItem['type'],
      stage_id,
      is_additional_stage: false,
      previous_stage_item_id,
      external_link: [
        'video-link',
        'meeting-link',
        'document-sign',
        'external-web-link',
      ].includes(values.type as string)
        ? values.external_link || null
        : null,
      internal_tab: ['link-to-tab'].includes(values.type as string)
        ? values.internal_tab || null
        : null,
      document_id: ['view-document'].includes(values.type as string)
        ? values.document_id || null
        : null,
      aimei_prompt_id: ['ask-aimei'].includes(values.type as string)
        ? values.aimei_prompt_id || null
        : null,
    };

    createStageItem(submitData);
  };

  useEffect(() => {
    if (isCreateStageItemError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isCreateStageItemError]);

  useEffect(() => {
    if (isCreateStageItemSuccess) {
      toast({
        title: 'Stage item created successfully',
      });

      setOpen(false);
      createStageItemToStore(createStageItemResult?.data as StageItem);
    }
  }, [
    createStageItemResult,
    isCreateStageItemSuccess,
    setOpen,
    createStageItemToStore,
  ]);

  return (
    <Form {...form}>
      <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex w-full items-start justify-between gap-2 rounded-md">
          <div className="flex h-[100px] w-6 flex-col items-center justify-between py-2">
            <div className="text-lg">{index + 1}</div>
          </div>
          <div className="w-full space-y-3 rounded-lg bg-white p-5">
            <div className="flex items-start gap-2">
              <FormInput
                name="title"
                label="Title"
                control={form.control}
                className={cn(
                  form.watch('type') == 'upload' || !form.watch('type')
                    ? 'w-4/5'
                    : 'w-2/5'
                )}
              />
              <FormSelect
                name="type"
                label="Type"
                options={linkOptions}
                control={form.control}
                className="w-1/5"
              />
              <RenderOption form={form} className="w-2/5" />
            </div>
            <div className="flex items-start gap-2">
              <FormTextArea
                name="description"
                label="Add description"
                control={form.control}
                className="w-4/5"
                placeholder="description"
              />
              <FormSwitch
                name="is_pipeline"
                control={form.control}
                className="mt-8 w-1/5"
                custom_label="Pipeline"
              />
            </div>
            <div className="flex w-full justify-end space-x-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={isCreateStageItemLoading}>
                {isCreateStageItemLoading && (
                  <Loader2 className="size-4 animate-spin" />
                )}
                Add Item
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
};
