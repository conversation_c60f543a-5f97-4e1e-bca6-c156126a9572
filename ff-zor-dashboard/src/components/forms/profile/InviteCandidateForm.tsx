import { useOrganization, useUser } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import { FormMultiSelect } from '@/components/global/form-inputs/FormMultiSelect';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { US_STATES } from '@/constants/territories/states';
import { VALIDATION_MESSAGES } from '@/constants/validation';
import { useSendOrgInvitation } from '@/hooks/auth/useSendOrgInvitation';
import { toast } from '@/hooks/ui/use-toast';
import useLeadsStore from '@/stores/useLeadsStore';

const InviteCandidateFormSchema = z.object({
  name: z
    .string({
      required_error: VALIDATION_MESSAGES.NAME_REQUIRED,
    })
    .min(1, { message: VALIDATION_MESSAGES.NAME_REQUIRED }),
  email: z
    .string({
      required_error: VALIDATION_MESSAGES.EMAIL_REQUIRED,
    })
    .min(1, { message: VALIDATION_MESSAGES.EMAIL_REQUIRED })
    .email({ message: VALIDATION_MESSAGES.EMAIL_INVALID }),
  preferred_markets: z.array(z.string()).optional(),
});

interface InviteCandidateFormProps {
  closeDialog: () => void;
}

const InviteCandidateForm = ({ closeDialog }: InviteCandidateFormProps) => {
  const { user } = useUser();
  const { isLoaded, organization } = useOrganization();
  const { triggerRefresh } = useLeadsStore();

  const form = useForm<z.infer<typeof InviteCandidateFormSchema>>({
    resolver: zodResolver(InviteCandidateFormSchema),
    defaultValues: {
      name: '',
      email: '',
      preferred_markets: [],
    },
  });

  const {
    mutate: sendOrgInvitation,
    isLoading: isSendingInvitation,
    isError: isErrorSendingInvitation,
    isSuccess: isSuccessSendingInvitation,
  } = useSendOrgInvitation();

  const onSubmit = (values: z.infer<typeof InviteCandidateFormSchema>) => {
    if (!isLoaded || !organization) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description:
          'You are not in an organization or organization is not loaded.',
      });
      return;
    }

    sendOrgInvitation({
      role: 'org:zee',
      name: values.name,
      email: values.email,
      orgId: organization.id,
      inviterId: user?.id || '',
      preferredMarkets: values.preferred_markets || [],
    });
  };

  useEffect(() => {
    if (isErrorSendingInvitation) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again later',
      });
    }
  }, [isErrorSendingInvitation]);

  useEffect(() => {
    if (isSuccessSendingInvitation) {
      toast({
        title: 'Invitation sent',
        description: 'The invitation has been sent to the candidate.',
      });

      // Trigger leads data refresh
      triggerRefresh();

      closeDialog();
    }
  }, [isSuccessSendingInvitation, closeDialog, triggerRefresh]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <div className="space-y-4 p-5">
          <FormInput
            type="text"
            name="name"
            label="Name"
            control={form.control}
            placeholder="Enter candidate's name"
          />

          <FormInput
            type="text"
            name="email"
            label="Email"
            control={form.control}
            placeholder="<EMAIL>"
          />

          <div className="flex flex-col items-start gap-2">
            <div className="as-title-03">
              Preferred Market
              <p className="as-title-04">You can select 5 max.</p>
            </div>
            <FormMultiSelect
              name="preferred_markets"
              control={form.control}
              options={Object.entries(US_STATES).map(([key, value]) => ({
                value: key,
                label: value,
              }))}
              className="w-full"
              maxSelections={5}
              placeholder="Select up to 5 markets"
            />
          </div>
        </div>
        <div className="flex justify-between border-t p-4">
          <Button type="button" variant="outline" onClick={closeDialog}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSendingInvitation}>
            {isSendingInvitation && <Loader2 className="size-4 animate-spin" />}
            Send Invitation
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default InviteCandidateForm;
