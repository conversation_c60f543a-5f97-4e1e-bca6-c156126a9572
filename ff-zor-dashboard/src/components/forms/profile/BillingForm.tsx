import { forwardRef, useImperativeHandle } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import BillingFormSchema from '@/components/forms/schemas/BillingFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import { Form } from '@/components/ui/form';

interface BillingFormProps {
  form: UseFormReturn<z.infer<typeof BillingFormSchema>>;
}

export const BillingForm = forwardRef(({ form }: BillingFormProps, ref) => {
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      form.handleSubmit(async () => {})();
    },
  }));

  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div
          className={`flex flex-col gap-6 rounded-xl border border-border bg-white p-6`}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Billing Details
          </p>
          <div className="w-full">
            <div className="grid flex-1 grid-cols-2 gap-6">
              <FormInput
                name="legal_business_name"
                label="Legal Business Name"
                control={form.control}
              />
              <FormInput
                type="phone"
                name="phone"
                label="Phone"
                control={form.control}
              />
              <FormInput name="email" label="Email" control={form.control} />
              <FormInput
                name="postal_code"
                label="Zip Code"
                control={form.control}
              />
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
});
