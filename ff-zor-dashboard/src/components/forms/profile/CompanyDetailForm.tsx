import { useOrganization } from '@clerk/clerk-react';
import { UseFormReturn } from 'react-hook-form';

import { CompanyFormValues } from '@/components/forms/schemas/CompanyFormSchema';
import FormCalendar from '@/components/global/form-inputs/FormCalendar';
import FormImage from '@/components/global/form-inputs/FormImage';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Form } from '@/components/ui/form';
import { IndustryOptions, OptionInterface } from '@/constants/options';
import { cn } from '@/lib/ui/utils';

interface CompanyDetailsFormProps {
  /** Form instance from react-hook-form */
  form: UseFormReturn<CompanyFormValues>;
}

/**
 * Company details form component
 * @param props - Component props
 * @returns JSX.Element
 */
export const CompanyDetailsForm = ({ form }: CompanyDetailsFormProps) => {
  const { organization } = useOrganization();

  /**
   * Generates year options for franchise since select
   * @returns Array of year options
   */
  const SelectYearOption = (): Array<OptionInterface> => {
    const currentYear = new Date().getFullYear();
    const selectOptions: Array<OptionInterface> = [];

    for (let year = currentYear; year >= 1900; year--) {
      selectOptions.push({ value: year.toString(), label: `${year}` });
    }
    return selectOptions;
  };

  const isDisabled = (fieldName: keyof CompanyFormValues): boolean => {
    if (fieldName === 'name') {
      return organization ? true : false;
    }
    return false;
  };

  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div className="flex items-center gap-6 rounded-xl border border-border bg-white p-6">
          <div className="as-title-03 as-strong min-w-[280px] text-zinc-700">
            Franchise Logo
            <p className="as-title-03 font-normal">
              This will be displayed in the application
            </p>
          </div>
          <FormImage
            name="logo_url"
            isEdit={true}
            control={form.control}
            setValue={form.setValue}
          />
        </div>
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            'flex-col'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Company Details
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <FormInput
              type="text"
              name="name"
              label="Name"
              control={form.control}
              disabled={isDisabled('name')}
            />
            <FormInput
              type="text"
              name="headquarter_location"
              label="Headquarter location"
              control={form.control}
              disabled={isDisabled('headquarter_location')}
            />
            <FormSelect
              name="franchise_since"
              label="Franchise Since"
              control={form.control}
              options={SelectYearOption()}
              disabled={isDisabled('franchise_since')}
            />
            <FormSelect
              name="industry"
              label="Industry"
              control={form.control}
              options={IndustryOptions}
              disabled={isDisabled('industry')}
              className="w-full"
            />
            <FormInput
              type="number"
              name="number_of_units"
              label="No of franchise Units"
              control={form.control}
              disabled={isDisabled('number_of_units')}
            />
            <FormInput
              type="url"
              name="website"
              label="Website"
              control={form.control}
              disabled={isDisabled('website')}
            />
            <FormCalendar
              name="fdd_renewal_date"
              label="FDD Renewal Date"
              control={form.control}
              disabled={isDisabled('fdd_renewal_date')}
            />
          </div>
        </div>
      </form>
    </Form>
  );
};
