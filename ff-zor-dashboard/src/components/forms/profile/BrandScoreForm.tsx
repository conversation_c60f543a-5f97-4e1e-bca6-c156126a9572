import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Slider } from '@/components/ui/slider';
import { VALIDATION_LIMITS, VALIDATION_MESSAGES } from '@/constants/validation';
import { useCreateOrUpdateBrandMatchScore } from '@/hooks/brand-match-scores/useCreateOrUpdateBrandMatchScore';
import { useDeleteBrandMatchScore } from '@/hooks/brand-match-scores/useDeleteBrandMatchScore';
import { toast } from '@/hooks/ui/use-toast';

const BrandScoreFormSchema = z.object({
  manual_input: z
    .number()
    .min(VALIDATION_LIMITS.MIN_SCORE, {
      message: VALIDATION_MESSAGES.BRAND_SCORE_MIN,
    })
    .max(VALIDATION_LIMITS.MAX_SCORE, {
      message: VALIDATION_MESSAGES.BRAND_SCORE_MAX,
    }),
});

interface BrandScoreFormProps {
  percentage: number;
  closeDialog: () => void;
  setPercentage: React.Dispatch<React.SetStateAction<number | 0>>;
  zeeId: string;
  hasExistingScore: boolean;
}

const BrandScoreForm = ({
  percentage,
  closeDialog,
  setPercentage,
  zeeId,
  hasExistingScore,
}: BrandScoreFormProps) => {
  const form = useForm<z.infer<typeof BrandScoreFormSchema>>({
    resolver: zodResolver(BrandScoreFormSchema),
    defaultValues: {
      manual_input: percentage,
    },
  });

  const createOrUpdateMutation = useCreateOrUpdateBrandMatchScore();
  const deleteMutation = useDeleteBrandMatchScore();

  // Watch the manual_input field to sync with slider
  const watchedValue = form.watch('manual_input');

  // Update the slider when the input value changes
  useEffect(() => {
    if (watchedValue !== undefined && watchedValue !== percentage) {
      setPercentage(watchedValue);
    }
  }, [watchedValue, percentage, setPercentage]);

  const onSubmit = async (values: z.infer<typeof BrandScoreFormSchema>) => {
    try {
      await createOrUpdateMutation.mutateAsync({
        zee_id: zeeId,
        score: values.manual_input,
      });

      form.reset({ manual_input: values.manual_input });
      setPercentage(values.manual_input);
      toast({
        title: 'Brand match score updated successfully',
      });
      closeDialog();
    } catch (error) {
      console.error('Error updating brand match score:', error);
      toast({
        title: 'Failed to update brand match score',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    try {
      await deleteMutation.mutateAsync(zeeId);
      setPercentage(0);
      toast({
        title: 'Brand match score deleted successfully',
      });
      closeDialog();
    } catch (error) {
      console.error('Error deleting brand match score:', error);
      toast({
        title: 'Failed to delete brand match score',
        variant: 'destructive',
      });
    }
  };

  const isLoading =
    createOrUpdateMutation.isPending || deleteMutation.isPending;

  return (
    <Form {...form}>
      <form className="w-full" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-4 p-5">
          <Slider
            max={100}
            step={1}
            value={[watchedValue || 0]}
            onValueChange={(value) => {
              form.setValue('manual_input', value[0]);
            }}
          />
          <FormInput
            type="number"
            name="manual_input"
            label="Manual input"
            control={form.control}
            placeholder="50"
          />
        </div>
        <div className="flex justify-between border-t p-4">
          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={closeDialog}>
              Cancel
            </Button>
            {hasExistingScore && (
              <Button
                type="button"
                variant="destructive"
                onClick={handleDelete}
                disabled={isLoading}
              >
                {deleteMutation.isPending && (
                  <Loader2 className="h-4 w-4 animate-spin" />
                )}
                Delete
              </Button>
            )}
          </div>
          <Button type="submit" disabled={isLoading}>
            {createOrUpdateMutation.isPending && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
            {hasExistingScore ? 'Update' : 'Save'} Score
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default BrandScoreForm;
