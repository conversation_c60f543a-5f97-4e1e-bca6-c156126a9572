import { forwardRef, useImperativeHandle } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import MyProfileFormSchema from '@/components/forms/schemas/MyProfileFormSchema';
import FormImage from '@/components/global/form-inputs/FormImage';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Form } from '@/components/ui/form';
import { PreferredContactOptions } from '@/constants/options';

interface MyProfileFormProps {
  form: UseFormReturn<z.infer<typeof MyProfileFormSchema>>;
}

export const MyProfileForm = forwardRef(({ form }: MyProfileFormProps, ref) => {
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      form.handleSubmit(async () => {})();
    },
  }));

  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div className="flex items-center gap-6 rounded-xl border border-border bg-white p-6">
          <div className="as-title-03 as-strong min-w-[280px] text-zinc-700">
            Profile photo
            <p className="as-title-03 font-normal">
              This will be displayed in the application
            </p>
          </div>
          <div className="min-w-[280px]">
            <FormImage
              name="photo_url"
              isEdit={true}
              control={form.control}
              setValue={form.setValue}
            />
          </div>
        </div>
        <div
          className={`flex flex-col gap-6 rounded-xl border border-border bg-white p-6`}
        >
          <p className="as-title-02 as-strong w-[280px] font-medium text-zinc-700">
            Contact Details
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <FormInput
              name="full_name"
              label="Full Name"
              control={form.control}
              disabled={false}
            />
            <FormInput
              name="position"
              label="Organization Role"
              control={form.control}
              disabled={false}
            />
            <FormInput
              name="city"
              label="City"
              control={form.control}
              disabled={false}
            />
            <FormInput
              type="phone"
              name="phone"
              label="Phone"
              control={form.control}
              disabled={false}
            />
            <FormInput
              name="email"
              label="Email"
              control={form.control}
              disabled={false}
            />
            <FormInput
              name="meeting_link"
              label="Meeting Link"
              control={form.control}
              disabled={false}
            />
            <FormInput
              name="linkedin"
              label="LinkedIn"
              control={form.control}
              disabled={false}
            />
            <FormSelect
              name="preferred_contact"
              label="Preferred Contact"
              control={form.control}
              options={PreferredContactOptions}
              disabled={false}
            />
          </div>
        </div>
      </form>
    </Form>
  );
});
