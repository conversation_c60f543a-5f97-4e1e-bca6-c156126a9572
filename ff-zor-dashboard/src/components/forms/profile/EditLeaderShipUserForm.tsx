import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { LeadershipUserFormSchema } from '@/components/forms/schemas/internal-manager/LeadershipUserFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormRadioGroup from '@/components/global/form-inputs/FormRadioGroup';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { PreferredContactOptions } from '@/constants/options';
import { toast } from '@/hooks/ui/use-toast';
import { useUpdateAddedZorMember } from '@/hooks/zor-member/useUpdateAddedZorMember';
import useZorMemberStore from '@/stores/useZorMemberStore';
import { ZorMember } from '@/types/zor.member.type';

interface LeadershipUserFormProps {
  data: ZorMember | null;
  setIsEdit: (isEdit: boolean) => void;
}

const EditLeadershipUserForm = ({
  data,
  setIsEdit,
}: LeadershipUserFormProps) => {
  const editForm = useForm<z.infer<typeof LeadershipUserFormSchema>>({
    resolver: zodResolver(LeadershipUserFormSchema),
    defaultValues: data || {},
  });

  const { zorMembers, setZorMembers } = useZorMemberStore();

  const {
    mutate: updateAddedZorMember,
    result: updateAddedZorMemberResult,
    isError: isUpdateAddedZorMemberError,
    isSuccess: isUpdateAddedZorMemberSuccess,
    isLoading: isUpdateAddedZorMemberLoading,
  } = useUpdateAddedZorMember();

  const onSubmit = (values: z.infer<typeof LeadershipUserFormSchema>) => {
    updateAddedZorMember({
      id: data?.id,
      is_added: true,
      ...values,
    });
  };

  useEffect(() => {
    if (isUpdateAddedZorMemberError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isUpdateAddedZorMemberError]);

  useEffect(() => {
    if (isUpdateAddedZorMemberSuccess && updateAddedZorMemberResult?.data) {
      setZorMembers(
        (zorMembers || []).map((zorMember) =>
          zorMember.id === updateAddedZorMemberResult?.data?.id
            ? { ...updateAddedZorMemberResult?.data, is_added: true }
            : zorMember
        )
      );

      setIsEdit(false);
    }
  }, [updateAddedZorMemberResult, isUpdateAddedZorMemberSuccess]);

  return (
    <Form {...editForm}>
      <form onSubmit={editForm.handleSubmit(onSubmit)} className="w-full">
        <div className="flex gap-6 rounded-xl border border-border bg-white p-6">
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Edit {data?.full_name}
          </p>
          <div className="w-full">
            <div className="mb-6 grid flex-1">
              <FormRadioGroup
                name="is_public"
                label="Publish Profile"
                control={editForm.control}
              />
            </div>
            <div className="grid flex-1 grid-cols-2 gap-6">
              <FormInput
                name="full_name"
                label="Full Name"
                control={editForm.control}
              />
              <FormInput
                name="position"
                label="Position"
                control={editForm.control}
                placeholder="Product Manager"
              />
              <FormInput
                name="city"
                label="City"
                control={editForm.control}
                placeholder="New York"
              />
              <FormInput
                type="phone"
                name="phone"
                label="Phone"
                control={editForm.control}
                placeholder="(*************"
              />
              <FormInput
                name="email"
                label="Email"
                control={editForm.control}
                placeholder="<EMAIL>"
              />
              <FormInput
                name="meeting_link"
                label="Meeting Link"
                control={editForm.control}
                placeholder="https://calendly.com/etg-hywo-wbk"
              />
              <FormInput
                name="linkedin"
                label="LinkedIn"
                control={editForm.control}
                placeholder="https://www.linkedin.com/in/etg-hywo-wbk"
              />
              <FormSelect
                name="preferred_contact"
                label="Preferred Contact"
                control={editForm.control}
                options={PreferredContactOptions}
              />
            </div>
          </div>
        </div>
        <div className="mt-2 flex items-center justify-end gap-2">
          <Button size="sm" variant="outline" onClick={() => setIsEdit(false)}>
            Cancel
          </Button>
          <Button size="sm" disabled={isUpdateAddedZorMemberLoading}>
            {isUpdateAddedZorMemberLoading && (
              <Loader2 className="size-4 animate-spin" />
            )}
            Save Changes
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default EditLeadershipUserForm;
