import { Plus, Trash2 } from 'lucide-react';
import { forwardRef, useImperativeHandle } from 'react';
import { useFieldArray, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { CardListFormSchema } from '@/components/forms/schemas/PaymentFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormPaymentCardInput from '@/components/global/form-inputs/FormPaymentCardInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import {
  formatCreditCardNumber,
  formatCVC,
  formatExpirationDate,
} from '@/lib/card';

interface PaymentFormProps {
  form: UseFormReturn<z.infer<typeof CardListFormSchema>>;
}

export const PaymentForm = forwardRef(({ form }: PaymentFormProps, ref) => {
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      form.handleSubmit(async () => {})();
    },
  }));

  const { fields, append, remove } = useFieldArray({
    name: 'cardData',
    control: form.control,
  });

  const addNewCard = () => {
    append({
      card_number: '',
      expiration_date: '',
      security_code: '',
    });
  };

  const handleCardNumber = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const formattedNumber = formatCreditCardNumber(value);
    return formattedNumber;
  };

  const handleExpirationDate = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const formattedDate = formatExpirationDate(value);
    return formattedDate;
  };

  const handleCVC = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cvc = formatCVC(value);
    return cvc;
  };

  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div
          className={`flex flex-col gap-6 rounded-xl border border-border bg-white p-6`}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Payment Details
          </p>
          <div className="w-full space-y-8">
            {fields?.map((field, index) => (
              <div
                className="relative grid flex-1 grid-cols-2 gap-6"
                key={field.id}
              >
                <FormPaymentCardInput
                  control={form.control}
                  name={`cardData.${index}.card_number`}
                  type="tel"
                  label="Card Number"
                  disabled={false}
                  className="w-full"
                  placeholder="1234 5678 9012 3456"
                  maxLength={19}
                  pattern="[\d| ]{, 22}"
                  onFormat={(e) => handleCardNumber(e)}
                />

                <FormInput
                  control={form.control}
                  name={`cardData.${index}.expiration_date`}
                  label="Expiration Date"
                  type="text"
                  disabled={false}
                  placeholder="MM/YY"
                  pattern="\d\d/\d\d"
                  onFormat={(e) => handleExpirationDate(e)}
                />

                <FormInput
                  control={form.control}
                  name={`cardData.${index}.security_code`}
                  type="tel"
                  label="Security Code"
                  disabled={false}
                  pattern="\d{3}"
                  onFormat={(e) => handleCVC(e)}
                />
                <Trash2
                  className="hover:text-destructive/90 absolute right-2 top-2 h-4 w-4 text-destructive"
                  onClick={() => remove(index)}
                />
              </div>
            ))}
            <Button variant={'outline'} type="button" onClick={addNewCard}>
              <Plus />
              Add new card
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
});
