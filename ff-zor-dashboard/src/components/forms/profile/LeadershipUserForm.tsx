import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { LeadershipUserFormSchema } from '@/components/forms/schemas/internal-manager/LeadershipUserFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormRadioGroup from '@/components/global/form-inputs/FormRadioGroup';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { PreferredContactOptions } from '@/constants/options';
import { toast } from '@/hooks/ui/use-toast';
import { useAddZorMember } from '@/hooks/zor-member/useAddZorMember';
import useZorMemberStore from '@/stores/useZorMemberStore';

interface LeadershipUserFormProps {
  closeDialog: () => void;
}

const LeadershipUserForm = ({ closeDialog }: LeadershipUserFormProps) => {
  const form = useForm<z.infer<typeof LeadershipUserFormSchema>>({
    resolver: zodResolver(LeadershipUserFormSchema),
    defaultValues: {
      is_public: true,
    },
  });

  const { zorMembers, setZorMembers } = useZorMemberStore();

  const {
    mutate: addZorMember,
    result: addZorMemberResult,
    isError: isAddZorMemberError,
    isSuccess: isAddZorMemberSuccess,
    isLoading: isAddZorMemberLoading,
  } = useAddZorMember();

  const onSubmit = async (values: z.infer<typeof LeadershipUserFormSchema>) => {
    addZorMember(values);
  };

  useEffect(() => {
    if (isAddZorMemberError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isAddZorMemberError]);

  useEffect(() => {
    if (isAddZorMemberSuccess && addZorMemberResult?.data) {
      setZorMembers([
        ...(zorMembers || []),
        { ...addZorMemberResult.data, is_added: true },
      ]);
      closeDialog();
    }
  }, [addZorMemberResult, isAddZorMemberSuccess, setZorMembers]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <div className="-mr-2 max-h-[640px] space-y-4 overflow-auto pb-2 pl-[1px] pr-2">
          <FormRadioGroup
            name="is_public"
            label="Publish Profile"
            control={form.control}
          />
          <FormInput
            name="full_name"
            label="Full Name"
            control={form.control}
            placeholder="John Doe"
          />
          <FormInput
            name="position"
            label="Title"
            control={form.control}
            placeholder="Product Manager"
          />
          <FormInput
            name="city"
            label="City"
            control={form.control}
            placeholder="New York"
          />
          <FormInput
            type="phone"
            name="phone"
            label="Phone"
            control={form.control}
          />
          <FormInput
            name="email"
            label="Email"
            control={form.control}
            placeholder="<EMAIL>"
          />
          <FormInput
            name="meeting_link"
            label="Meeting Link"
            control={form.control}
            placeholder="https://meet.google.com/abc-xyz-123"
          />
          <FormInput
            name="linkedin"
            label="Linked In"
            control={form.control}
            placeholder="https://www.linkedin.com/in/john-doe"
          />
          <FormSelect
            name="preferred_contact"
            label="Preferred Contact"
            control={form.control}
            options={PreferredContactOptions}
          />
        </div>
        <div className="flex justify-between py-4">
          <Button type="button" variant="outline" onClick={closeDialog}>
            Cancel
          </Button>
          <Button size="sm" disabled={isAddZorMemberLoading}>
            {isAddZorMemberLoading && (
              <Loader2 className="size-4 animate-spin" />
            )}
            Add User
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default LeadershipUserForm;
