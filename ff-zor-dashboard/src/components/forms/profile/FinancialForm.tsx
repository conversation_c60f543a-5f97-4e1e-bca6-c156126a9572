import { UseFormReturn } from 'react-hook-form';

import { FinancialFormValues } from '@/components/forms/schemas/FinancialFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormRadioGroup from '@/components/global/form-inputs/FormRadioGroup';
import { Form } from '@/components/ui/form';
import { cn } from '@/lib/ui/utils';

interface FinancialFormProps {
  /** Form instance from react-hook-form */
  form: UseFormReturn<FinancialFormValues>;
}

/**
 * Financial form component for franchise financial details
 * @param props - Component props
 * @returns JSX.Element
 */
export const FinancialForm = ({ form }: FinancialFormProps) => {
  const isDisabled = (): boolean => false;

  return (
    <Form {...form}>
      <form className="w-full space-y-1.5">
        <div
          className={cn(
            'flex gap-6 rounded-xl border border-border bg-white p-6',
            'flex-col'
          )}
        >
          <p className="as-title-03 as-strong w-[280px] text-zinc-700">
            Financials
          </p>
          <div className="grid flex-1 grid-cols-2 gap-6">
            <div className="grid grid-cols-2 gap-6">
              <FormInput
                name="startup_cost_min"
                type="number"
                label="Minimum Franchise Start-up Cost"
                preIcon
                control={form.control}
                disabled={isDisabled()}
              />
              <FormInput
                name="startup_cost_max"
                type="number"
                label="Maximum Franchise Start-up Cost"
                preIcon
                control={form.control}
                disabled={isDisabled()}
              />
            </div>
            <FormInput
              name="min_franchisee_net_worth"
              type="number"
              label="Minimum Franchisee Networth"
              preIcon
              control={form.control}
              disabled={isDisabled()}
            />
            <FormInput
              name="franchise_fee"
              type="number"
              label="Franchisee Fee"
              preIcon
              control={form.control}
              disabled={isDisabled()}
            />
            <FormInput
              name="veteran_franchise_fee"
              type="number"
              label="Veteran/First Responder Franchise Fee"
              preIcon
              control={form.control}
              disabled={isDisabled()}
            />
            <FormRadioGroup
              name="is_sba_eligible"
              label="SBA Eligible Business"
              control={form.control}
              disabled={isDisabled()}
            />
            <FormRadioGroup
              name="is_veteran_discount"
              label="Veteran/First Responder Discount"
              control={form.control}
              disabled={isDisabled()}
            />
          </div>
        </div>
      </form>
    </Form>
  );
};
