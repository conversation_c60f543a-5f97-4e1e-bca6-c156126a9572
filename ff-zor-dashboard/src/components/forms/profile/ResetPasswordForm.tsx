import { useUser } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import ResetPasswordFormSchema from '@/components/forms/schemas/ResetPassowrdFormSchema';
import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useChangePassword } from '@/hooks/auth/useChangePassword';
import { toast } from '@/hooks/ui/use-toast';

export const ResetPasswordForm = () => {
  const form = useForm<z.infer<typeof ResetPasswordFormSchema>>({
    resolver: zodResolver(ResetPasswordFormSchema),
    defaultValues: {},
  });
  const { user } = useUser();

  const [isReset, setIsReset] = useState<boolean>(false);

  const {
    mutate: changePassword,
    error: changePasswordError,
    isError: isChangePasswordError,
    isSuccess: isChangePasswordSuccess,
    isLoading: isChangePasswordLoading,
  } = useChangePassword();

  const onSubmit = async (values: z.infer<typeof ResetPasswordFormSchema>) => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'User ID not available. Please try again.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await changePassword({
        userId: user.id,
        oldPassword: values.old_password,
        newPassword: values.new_password,
      });
    } catch (error) {
      console.error('Password change error:', error);
    }
  };

  useEffect(() => {
    if (isChangePasswordError) {
      toast({
        title: 'Password Change Failed',
        description:
          changePasswordError?.message ||
          'Failed to change password. Please check your old password and try again.',
        variant: 'destructive',
      });
    }
  }, [changePasswordError, isChangePasswordError]);

  useEffect(() => {
    if (isChangePasswordSuccess) {
      toast({
        title: 'Password Changed Successfully',
        description: 'Your password has been updated successfully.',
      });
      form.reset();
      setIsReset(false);
    }
  }, [isChangePasswordSuccess]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex flex-col items-start justify-between gap-6 rounded-xl border border-border bg-white p-6 md:flex-row">
          <div className="as-title-03 as-strong min-w-[280px] text-zinc-700">
            Password
          </div>
          {!isReset && (
            <Button
              variant={'outline'}
              type="button"
              onClick={() => setIsReset(true)}
            >
              Reset password
            </Button>
          )}
          {isReset && (
            <div className="flex w-full flex-col items-end gap-5">
              <div className="grid w-full flex-1 grid-cols-2 items-end gap-6">
                <FormInput
                  control={form.control}
                  name="old_password"
                  label="Old Password"
                  type="password"
                />
              </div>
              <div className="grid w-full flex-1 grid-cols-2 items-start gap-6">
                <FormInput
                  control={form.control}
                  name="new_password"
                  label="New Password"
                  type="password"
                />
                <FormInput
                  control={form.control}
                  name="confirm_new_password"
                  label="Confirm New Password"
                  type="password"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={'outline'}
                  size={'sm'}
                  type="button"
                  onClick={() => setIsReset(false)}
                >
                  Cancel
                </Button>
                <Button size={'sm'} disabled={isChangePasswordLoading}>
                  {isChangePasswordLoading && (
                    <Loader2 className="size-4 animate-spin" />
                  )}
                  Save new password
                </Button>
              </div>
            </div>
          )}
        </div>
      </form>
    </Form>
  );
};
