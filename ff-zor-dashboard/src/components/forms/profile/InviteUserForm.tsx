import { useOrganization, useUser } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import FormInput from '@/components/global/form-inputs/FormInput';
import FormSelect from '@/components/global/form-inputs/FormSelect';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { UserPermissionOptions, UserTypeOptions } from '@/constants/options';
import { useSendOrgInvitation } from '@/hooks/auth/useSendOrgInvitation';
import { toast } from '@/hooks/ui/use-toast';

const InviteUserFormSchema = z
  .object({
    email: z.string().email({ message: 'Invalid email address' }),
    user_type: z.enum(['leadership', 'partner'], {
      message: 'Type must be either leadership or partner.',
    }),
    user_permission: z.enum(['owner', 'administrator', 'consultant', ''], {
      message: 'Type must be either owner, administrator or consultant.',
    }),
  })
  .refine(
    (data) => {
      return data.user_permission !== '';
    },
    {
      path: ['user_permission'],
      message: 'Please select a user permission.',
    }
  )
  .refine(
    (data) => {
      if (data.user_type === 'leadership') {
        return data.user_permission !== 'consultant';
      }
      return true;
    },
    {
      path: ['user_permission'],
      message: 'User permission must be either owner or administrator.',
    }
  )
  .refine(
    (data) => {
      if (data.user_type === 'partner') {
        return data.user_permission !== 'owner';
      }
      return true;
    },
    {
      path: ['user_permission'],
      message: 'User permission must be either administrator or consultant.',
    }
  );

interface InviteUserFormProps {
  closeDialog: () => void;
  onSuccess?: () => Promise<void>;
}

const InviteUserForm = ({ closeDialog, onSuccess }: InviteUserFormProps) => {
  const { user } = useUser();
  const { isLoaded, organization } = useOrganization();

  const form = useForm<z.infer<typeof InviteUserFormSchema>>({
    resolver: zodResolver(InviteUserFormSchema),
    defaultValues: {
      email: '',
      user_type: 'leadership',
      user_permission: '',
    },
  });

  const {
    mutate: sendOrgInvitation,
    isError: isErrorSendingInvitation,
    isLoading: isSendingInvitation,
    isSuccess: isSuccessSendingInvitation,
  } = useSendOrgInvitation();

  useEffect(() => {
    if (isErrorSendingInvitation) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again later',
      });
    }
  }, [isErrorSendingInvitation]);

  useEffect(() => {
    if (isSuccessSendingInvitation) {
      toast({
        title: 'Invitation sent',
        description: 'The invitation has been sent to the user.',
      });

      if (onSuccess) {
        onSuccess();
      }

      closeDialog();
    }
  }, [isSuccessSendingInvitation, closeDialog, onSuccess]);

  useEffect(() => {
    const currentUserType = form.watch('user_type');
    const currentPermission = form.watch('user_permission');

    if (
      currentUserType === 'leadership' &&
      currentPermission === 'consultant'
    ) {
      form.setValue('user_permission', '');
    } else if (currentUserType === 'partner' && currentPermission === 'owner') {
      form.setValue('user_permission', '');
    }
  }, [form.watch('user_type'), form]);

  const onSubmit = (values: z.infer<typeof InviteUserFormSchema>) => {
    if (!isLoaded || !organization) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description:
          'You are not in an organization or organization is not loaded.',
      });
      return;
    }

    sendOrgInvitation({
      role: `org:${values.user_type}_${values.user_permission}`,
      email: values.email,
      orgId: organization.id,
      inviterId: user?.id || '',
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
        <div className="space-y-4 p-5">
          <FormInput
            type="text"
            name="email"
            label="Email"
            control={form.control}
            placeholder="<EMAIL>"
          />
          <FormSelect
            name="user_type"
            label="User Type"
            control={form.control}
            options={UserTypeOptions}
          />
          <FormSelect
            name="user_permission"
            label="User Permission"
            options={
              form.watch('user_type') === 'leadership'
                ? UserPermissionOptions.filter(
                    (option) =>
                      option.value !== 'consultant' && option.value !== ''
                  )
                : UserPermissionOptions.filter(
                    (option) => option.value !== 'owner' && option.value !== ''
                  )
            }
            control={form.control}
          />
        </div>
        <div className="flex justify-between border-t p-4">
          <Button type="button" variant={'outline'} onClick={closeDialog}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSendingInvitation}>
            {isSendingInvitation && <Loader2 className="size-4 animate-spin" />}
            Send Invitation
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default InviteUserForm;
