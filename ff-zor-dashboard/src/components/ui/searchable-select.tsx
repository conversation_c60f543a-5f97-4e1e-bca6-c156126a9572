import { Check, ChevronsUpDown } from 'lucide-react';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/ui/utils';

export type Option = {
  value: string;
  label: string;
};

interface SearchableSelectProps {
  value?: string;
  options: Option[];
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
  onValueChange?: (value: string) => void;
}

export const SearchableSelect = ({
  value,
  options,
  disabled = false,
  className,
  placeholder = 'Select an option',
  emptyMessage = 'No results found.',
  searchPlaceholder = 'Search...',
  onValueChange,
}: SearchableSelectProps) => {
  const [open, setOpen] = React.useState(false);
  const [selected, setSelected] = React.useState<string | undefined>(value);

  React.useEffect(() => {
    if (value !== selected) {
      setSelected(value);
    }
  }, [value, selected]);

  const handleSelect = React.useCallback(
    (currentValue: string) => {
      setSelected(currentValue);
      onValueChange?.(currentValue);
      setOpen(false);
    },
    [onValueChange]
  );

  const selectedOption = React.useMemo(
    () => options.find((option) => option.value === selected),
    [options, selected]
  );

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild>
        <Button
          role="combobox"
          variant="outline"
          disabled={disabled}
          className={cn('w-full justify-between', className)}
          aria-expanded={open}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command>
          <CommandInput placeholder={searchPlaceholder} className="h-9" />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup className="max-h-[300px] overflow-auto">
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={handleSelect}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      selected === option.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
