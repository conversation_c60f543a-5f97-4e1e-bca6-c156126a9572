import { CloudUpload } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Control, FieldValues, Path } from 'react-hook-form';

import { FormField, FormItem, FormMessage } from '@/components/ui/form';
import { formatFileSize } from '@/lib/string';
import { cn } from '@/lib/ui/utils';

const MAX_FILE_SIZE_MB = 25;
const DEFAULT_ACCEPTED_FILE_TYPES = {
  'image/png': ['.png'],
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
  ],
} as const;

interface FileDetails {
  name: string;
  size: number;
}

interface FileUploadPanelProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  acceptedFileTypes?: Record<string, readonly string[]>;
  supportedFormatsText?: string;
  onFileUpload?: (file: File) => void;
}

const FileUploadPanel = <T extends FieldValues>({
  name,
  control,
  acceptedFileTypes = DEFAULT_ACCEPTED_FILE_TYPES,
  supportedFormatsText = 'PDF, Word (DOC, DOCX), Excel (XLS, XLSX), Images (PNG, JPG, JPEG, GIF, WebP)',
  onFileUpload,
}: FileUploadPanelProps<T>) => {
  const [fileDetails, setFileDetails] = useState<FileDetails | null>(null);

  const handleFileUpload = useCallback(
    (file: File) => {
      setFileDetails({ name: file.name, size: file.size });
      onFileUpload?.(file);
    },
    [onFileUpload]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        handleFileUpload(acceptedFiles[0]);
      }
    },
    [handleFileUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptedFileTypes,
    multiple: false,
    onDrop,
  });

  const renderUploadPrompt = () => (
    <div className="flex w-full flex-col items-center justify-center gap-4 text-center">
      <CloudUpload className="text-button h-10 w-10" color="var(--brand-500)" />
      <div>
        <h5 className="font-semibold">Drag and drop a file here, or browse</h5>
        <div className="as-body-03 mt-2 items-center justify-center text-gray-500">
          <div>Supported formats: {supportedFormatsText}</div>
          <div>Maximum size: {MAX_FILE_SIZE_MB}MB</div>
        </div>
      </div>
    </div>
  );

  const renderFileInfo = () => {
    if (!fileDetails) return null;
    const fileSizeInMB = formatFileSize(fileDetails.size);

    return (
      <>
        <div className="text-sm font-medium text-gray-700">Uploaded File:</div>
        <div className="text-sm text-gray-600">
          <span>{fileDetails.name}</span> - <span>{fileSizeInMB}</span>
        </div>
      </>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { onChange } }) => (
        <FormItem>
          <div className="flex flex-col rounded-lg">
            <div
              {...getRootProps()}
              className={cn(
                'cursor-pointer rounded-lg border-2 border-dashed p-4 transition-colors',
                isDragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-zinc-300 bg-white'
              )}
            >
              <input
                {...getInputProps({
                  onChange: (event) => {
                    const files = event.target.files;
                    if (files?.length) {
                      const file = files[0];
                      onChange(file);
                      handleFileUpload(file);
                    }
                  },
                })}
              />
              <div className="flex h-[160px] flex-col items-center justify-center">
                {fileDetails ? renderFileInfo() : renderUploadPrompt()}
              </div>
            </div>
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FileUploadPanel;
