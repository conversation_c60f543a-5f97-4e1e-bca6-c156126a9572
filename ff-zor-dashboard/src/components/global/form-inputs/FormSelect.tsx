import { Control, FieldValues, Path } from 'react-hook-form';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { OptionInterface } from '@/constants/options';
import { cn } from '@/lib/ui/utils';

type FormSelectProps<T extends FieldValues> = {
  name: Path<T>;
  label?: string;
  control: Control<T>;
  options: OptionInterface[];
  disabled?: boolean;
  className?: string;
  labelStyle?: string;
  placeholder?: string;
  setOpen?: (open: boolean) => void;
  onChange?: (value: string) => void;
  footer?: React.ReactNode; // NEW: optional footer node (e.g., an Add button)
};

const FormSelect = <T extends FieldValues>({
  name,
  label,
  options,
  control,
  disabled,
  className,
  labelStyle,
  placeholder = 'Select',
  onChange,
  footer,
}: FormSelectProps<T>) => {
  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel
              className={cn(
                labelStyle,
                disabled ? 'text-typo-gray-quaternary' : ''
              )}
            >
              {label}
            </FormLabel>
          )}
          {disabled ? (
            <div className="cursor-pointer border-none text-typo-gray-secondary">
              {field.value
                ? options.find((option) => option.value === field.value)?.label
                : '-'}
            </div>
          ) : (
            <Select
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
                onChange?.(value);
              }}
            >
              <FormControl>
                <SelectTrigger className="h-9 focus:outline-none focus:ring-0">
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.map((link, index) => (
                  <SelectItem key={index} value={link.value}>
                    <div className="flex items-center gap-2">
                      {link.color && (
                        <div
                          style={{ backgroundColor: link.color }}
                          className="h-2 w-2 rounded-full"
                        ></div>
                      )}
                      {link.label}
                    </div>
                  </SelectItem>
                ))}
                {footer && (
                  <div className="border-t border-border p-1">{footer}</div>
                )}
              </SelectContent>
            </Select>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormSelect;
