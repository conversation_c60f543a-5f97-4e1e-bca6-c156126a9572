import { CreditCard } from 'lucide-react';
import Payment from 'payment';
import { useState } from 'react';
import { Control, FieldValues, Path } from 'react-hook-form';
import {
  Alipay,
  Amex,
  Code,
  Diners,
  Discover,
  Elo,
  Generic,
  Hiper,
  Hipercard,
  Jcb,
  Maestro,
  Mastercard,
  Mir,
  Paypal,
  Unionpay,
  Visa,
} from 'react-payment-logos/dist/flat';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/ui/utils';

interface FormPaymentCardInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  type?: string;
  label?: string;
  placeholder?: string;
  description?: string;
  className?: string;
  icon?: string;
  disabled?: boolean;
  preIcon?: boolean;
  onChangeHandler?: (isChanged: boolean) => void;
  pattern?: string;
  onFormat?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxLength?: number;
}

const FormPaymentCardInput = <T extends FieldValues>({
  control,
  name,
  type,
  label,
  placeholder,
  description,
  className,
  disabled,
  pattern,
  onFormat,
  maxLength,
}: FormPaymentCardInputProps<T>) => {
  const [issuer, setIssuer] = useState<string | null>(null);

  const cardType = (issuer: string) => {
    switch (issuer) {
      case 'alipay':
        return <Alipay />;
      case 'code':
        return <Code />;
      case 'maestro':
        return <Maestro />;
      case 'mastercard':
        return <Mastercard />;
      case 'mir':
        return <Mir />;
      case 'amex':
        return <Amex />;
      case 'diners':
        return <Diners />;
      case 'jcb':
        return <Jcb />;
      case 'discover':
        return <Discover />;
      case 'elo':
        return <Elo />;
      case 'generic':
        return <Generic />;
      case 'hiper':
        return <Hiper />;
      case 'hipercard':
        return <Hipercard />;
      case 'paypal':
        return <Paypal />;
      case 'unionpay':
        return <Unionpay />;
      case 'visa':
        return <Visa />;
      default:
        return <CreditCard />;
    }
  };
  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          {label && (
            <FormLabel
              htmlFor={name}
              className={disabled ? 'text-typo-gray-quaternary' : ''}
            >
              {label}
            </FormLabel>
          )}
          <FormControl>
            {disabled ? (
              <div className="flex items-center justify-between pr-10">
                <div className="cursor-pointer border-none text-typo-gray-secondary">
                  {field.value}
                </div>
                <div>{issuer ? cardType(issuer) : ''}</div>
              </div>
            ) : (
              <div className="relative flex items-center">
                <Input
                  {...field}
                  id={name}
                  type={type}
                  placeholder={placeholder}
                  value={field.value || ''}
                  pattern={pattern}
                  maxLength={maxLength}
                  onChange={(e) => {
                    if (onFormat) {
                      const validateValue = onFormat(e);
                      field.onChange(validateValue);
                      const issuer = Payment.fns.cardType(e.target.value);
                      setIssuer(issuer);
                    } else {
                      field.onChange();
                    }
                  }}
                />
                <div className="absolute right-3">
                  {issuer ? cardType(issuer) : <CreditCard />}
                </div>
              </div>
            )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormPaymentCardInput;
