import { Control, FieldValues, Path } from 'react-hook-form';

import { Checkbox } from '@/components/ui/checkbox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { OptionInterface } from '@/constants/options';

interface FormCheckboxGroupProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label: string;
  options: OptionInterface[];
  disabled?: boolean;
  className?: string;
}

export function FormCheckboxGroup<T extends FieldValues>({
  control,
  name,
  label,
  options,
  disabled,
  className,
}: FormCheckboxGroupProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel htmlFor={name} className={'as-title-03 as-strong'}>
              {label}
            </FormLabel>
          )}
          {disabled ? (
            <div className="cursor-pointer border-none text-typo-gray-secondary">
              {field.value?.length > 0
                ? field.value
                    .map(
                      (value: string) =>
                        options.find((opt) => opt.value === value)?.label
                    )
                    .filter(Boolean)
                    .join(', ') || '-'
                : '-'}
            </div>
          ) : (
            <div className="flex flex-row gap-4">
              {options.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <FormControl>
                    <Checkbox
                      id={`${name}-${option.value}`}
                      checked={field.value?.includes(option.value)}
                      onCheckedChange={(checked) => {
                        const updatedValue = checked
                          ? [...(field.value || []), option.value]
                          : field.value?.filter(
                              (value: string) => value !== option.value
                            );
                        field.onChange(updatedValue);
                      }}
                    />
                  </FormControl>
                  <Label htmlFor={`${name}-${option.value}`}>
                    {option.label}
                    {option.description && (
                      <span className="text-muted-foreground">
                        {' '}
                        {option.description}
                      </span>
                    )}
                  </Label>
                </div>
              ))}
            </div>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
