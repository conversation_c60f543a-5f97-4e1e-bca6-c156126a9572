import { Paperclip, Phone, UploadCloud } from 'lucide-react';
import { useState } from 'react';
import {
  Control,
  ControllerRenderProps,
  FieldValues,
  Path,
} from 'react-hook-form';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { formatToLocalizedString } from '@/lib/string';
import { cn } from '@/lib/ui/utils';

interface FormInputProps<T extends FieldValues> {
  name: Path<T>;
  type?: string;
  icon?: string;
  label?: string;
  control: Control<T>;
  pattern?: string;
  preIcon?: boolean;
  readOnly?: boolean;
  disabled?: boolean;
  maxLength?: number;
  className?: string;
  placeholder?: string;
  description?: string;
  onFormat?: (e: React.ChangeEvent<HTMLInputElement>) => string | number;
  onChangeHandler?: (isChanged: boolean) => void;
}

const FormInput = <T extends FieldValues>({
  type = 'text',
  name,
  icon,
  label,
  preIcon,
  control,
  pattern,
  readOnly,
  disabled,
  maxLength,
  className,
  placeholder,
  description,
  onFormat,
  onChangeHandler,
}: FormInputProps<T>) => {
  const [initialValue] = useState<string | undefined>(undefined);

  const renderFileInput = (field: ControllerRenderProps<T, Path<T>>) => (
    <div className="relative">
      <Input
        {...field}
        id={field.name}
        type="file"
        value={field.value}
        pattern={pattern}
        maxLength={maxLength}
        className={cn('hidden', className)}
      />
      <div
        className={cn(
          'focus-visible:ring-ring flex h-9 w-full items-center gap-2 rounded-md border border-input bg-transparent px-3 py-1 text-base text-muted-foreground shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className
        )}
      >
        {icon === 'upload' ? (
          <UploadCloud className="h-4 w-4" />
        ) : (
          <Paperclip className="h-4 w-4" />
        )}
        <label htmlFor={field.name}>{placeholder || 'Upload File'}</label>
      </div>
    </div>
  );

  const renderDisabledInput = (value: string | number) => (
    <div className="cursor-pointer border-none text-typo-gray-secondary">
      {value
        ? preIcon
          ? `$${formatToLocalizedString(Number(value))}`
          : value
        : '-'}
    </div>
  );

  const formatPhoneNumber = (value: string) => {
    // Remove all non-numeric characters
    const cleaned = value.replace(/\D/g, '');

    // Format US phone numbers
    if (cleaned.length >= 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}${cleaned.length > 10 ? ` ext.${cleaned.slice(10)}` : ''}`;
    }

    // Partial formatting as user types
    if (cleaned.length > 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    if (cleaned.length > 3) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    }
    if (cleaned.length > 0) {
      return `(${cleaned}`;
    }
    return cleaned;
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: ControllerRenderProps<T, Path<T>>
  ) => {
    let newValue: string | number = e.target.value;

    if (type === 'number') {
      newValue = Number(newValue);
    } else if (type === 'phone') {
      newValue = formatPhoneNumber(newValue);
    }

    if (onFormat) {
      const validatedValue = onFormat(e);
      field.onChange(validatedValue);
    } else {
      field.onChange(newValue);
    }

    if (onChangeHandler) {
      const isChanged = newValue !== initialValue;
      onChangeHandler(isChanged);
    }
  };

  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          {label && (
            <FormLabel
              htmlFor={name}
              className={disabled ? 'text-typo-gray-quaternary' : ''}
            >
              {label}
            </FormLabel>
          )}
          <FormControl>
            {type === 'file' ? (
              renderFileInput(field)
            ) : disabled ? (
              renderDisabledInput(field.value)
            ) : (
              <div className="relative flex items-center">
                {preIcon && (
                  <span className="absolute left-3 text-gray-500">$</span>
                )}
                {type === 'phone' && (
                  <Phone className="absolute left-3 h-4 w-4 text-gray-500" />
                )}
                <Input
                  {...field}
                  id={name}
                  type={type === 'phone' ? 'tel' : type}
                  value={field.value || ''}
                  pattern={pattern}
                  maxLength={maxLength}
                  className={cn(preIcon || type === 'phone' ? 'pl-8' : '')}
                  spellCheck={type === 'text'}
                  placeholder={
                    type === 'phone' ? '(*************' : placeholder
                  }
                  onChange={(e) => handleInputChange(e, field)}
                  readOnly={readOnly}
                />
              </div>
            )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormInput;
