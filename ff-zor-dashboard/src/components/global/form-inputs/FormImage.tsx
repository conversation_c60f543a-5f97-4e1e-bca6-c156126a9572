import { useUser } from '@clerk/clerk-react';
import { Loader2 } from 'lucide-react';
import { ChangeEvent, useMemo, useRef, useState } from 'react';
import {
  Control,
  FieldValues,
  Path,
  PathValue,
  UseFormSetValue,
} from 'react-hook-form';

import DefaultCompanyLogo from '@/assets/icons/default-company-logo.svg';
import ImageEditor from '@/components/dialogs/ImageEditor';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { FormField, FormMessage } from '@/components/ui/form';
import { toast } from '@/hooks/ui/use-toast';
import getSupabaseClient from '@/lib/supabase';

const STORAGE_BUCKET = 'avatars';
const ACCEPTED_IMAGE_TYPES = 'image/jpeg, image/png';
interface FormImageProps<T extends FieldValues> {
  name: Path<T>;
  isEdit?: boolean;
  control: Control<T>;
  className?: string;
  setValue: UseFormSetValue<T>;
}

const FormImage = <T extends FieldValues>({
  name,
  isEdit = false,
  control,
  setValue,
}: FormImageProps<T>) => {
  const { user } = useUser();
  const filePickerRef = useRef<HTMLInputElement | null>(null);

  const [image, setImage] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [openImageEditor, setOpenImageEditor] = useState(false);

  const imageURL = useMemo(
    () => (image ? URL.createObjectURL(image) : ''),
    [image]
  );

  const handleChangeImage = () => {
    filePickerRef.current?.click();
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImage(file);
      setOpenImageEditor(true);
    }
  };

  const uploadImage = async (imageFile: File): Promise<string | null> => {
    const supabase = getSupabaseClient();
    const filePath = `${user?.id}/${Date.now()}-${imageFile.name}`;

    try {
      const { data: uploadedImage, error } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(filePath, imageFile, { upsert: true });

      if (error) throw error;

      const { data: response } = supabase.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(uploadedImage.path);

      return response.publicUrl;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      toast({
        title: 'Upload Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return null;
    }
  };

  const handleImageEditorSave = async (newImage: File | null) => {
    setImage(newImage);
    setOpenImageEditor(false);

    if (!newImage) return;

    setUploading(true);
    try {
      const publicUrl = await uploadImage(newImage);
      if (publicUrl) {
        setValue(name as Path<T>, publicUrl as PathValue<T, Path<T>>);
        toast({
          title: 'Image uploaded successfully',
          variant: 'default',
        });
      }
    } finally {
      setUploading(false);
    }
  };

  const renderAvatar = (value: string) => {
    if (!value) {
      return (
        <img
          src={DefaultCompanyLogo}
          alt="Default Company Logo"
          className="mx-auto h-16 w-16 cursor-pointer rounded-full border object-cover object-center"
        />
      );
    }

    return (
      <Avatar className="h-16 w-16 cursor-pointer border">
        <AvatarImage src={value || imageURL} alt="Company Logo" />
      </Avatar>
    );
  };

  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <>
          {openImageEditor && (
            <ImageEditor
              imageURL={imageURL}
              onSaveImage={handleImageEditorSave}
            />
          )}
          <div className="flex items-center gap-4">
            {renderAvatar(field.value)}
            {isEdit && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  disabled={uploading}
                  onClick={handleChangeImage}
                >
                  {uploading && <Loader2 className="size-4 animate-spin" />}
                  Update
                </Button>
                <input
                  ref={filePickerRef}
                  type="file"
                  accept={ACCEPTED_IMAGE_TYPES}
                  hidden
                  onChange={handleFileChange}
                />
              </>
            )}
          </div>
          <FormMessage className="mt-2" />
        </>
      )}
    />
  );
};

export default FormImage;
