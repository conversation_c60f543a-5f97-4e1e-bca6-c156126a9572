import { CloudUpload } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Control, FieldValues, Path } from 'react-hook-form';

import { FormField, FormItem, FormMessage } from '@/components/ui/form';
import { formatFileSize } from '@/lib/string';
import { cn } from '@/lib/ui/utils';

const MAX_FILE_SIZE_MB = 25;
const ACCEPTED_FILE_TYPES = {
  'text/csv': ['.csv'],
} as const;

interface FileDetails {
  name: string;
  size: number;
}

interface CSVUploadPanelProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  onFileUpload?: (file: File) => void;
}

const CSVUploadPanel = <T extends FieldValues>({
  name,
  control,
  onFileUpload,
}: CSVUploadPanelProps<T>) => {
  const [fileDetails, setFileDetails] = useState<FileDetails | null>(null);

  const handleFileUpload = useCallback(
    (file: File) => {
      setFileDetails({ name: file.name, size: file.size });
      onFileUpload?.(file);
    },
    [onFileUpload]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        handleFileUpload(acceptedFiles[0]);
      }
    },
    [handleFileUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: ACCEPTED_FILE_TYPES,
    multiple: false,
    onDrop,
  });

  const renderUploadPrompt = () => (
    <div className="flex w-full flex-col items-center justify-center gap-4 text-center">
      <CloudUpload className="text-button h-10 w-10" color="var(--brand-500)" />
      <div>
        <h5 className="font-semibold">
          Drag and drop a CSV file here, or browse
        </h5>
        <div className="as-body-03 mt-2 items-center justify-center">
          <div>Supported Format: CSV</div>
          <div>Maximum size: {MAX_FILE_SIZE_MB}MB</div>
        </div>
      </div>
    </div>
  );

  const renderFileInfo = () => {
    if (!fileDetails) return null;
    const fileSizeInMB = formatFileSize(fileDetails.size);

    return (
      <>
        <div className="text-sm font-medium text-gray-700">Uploaded File:</div>
        <div className="text-sm text-gray-600">
          <span>{fileDetails.name}</span> - <span>{fileSizeInMB}</span>
        </div>
      </>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { onChange } }) => (
        <FormItem>
          <div className="flex flex-col rounded-lg">
            <div
              {...getRootProps()}
              className={cn(
                'cursor-pointer rounded-lg border-2 border-dashed p-4 transition-colors',
                isDragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-zinc-300 bg-white'
              )}
            >
              <input
                {...getInputProps({
                  onChange: (event) => {
                    const files = event.target.files;
                    if (files?.length) {
                      const file = files[0];
                      onChange(file);
                      handleFileUpload(file);
                    }
                  },
                })}
              />
              <div className="flex h-[160px] flex-col items-center justify-center">
                {fileDetails ? renderFileInfo() : renderUploadPrompt()}
              </div>
            </div>
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default CSVUploadPanel;
