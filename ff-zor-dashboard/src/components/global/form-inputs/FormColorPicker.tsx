import 'react-color-palette/css';

import { Circle } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import {
  Control,
  ControllerRenderProps,
  FieldValues,
  Path,
} from 'react-hook-form';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/ui/utils';

interface FormInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  description?: string;
  className?: string;
  disabled?: boolean;
}

const FormColorPicker = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder,
  description,
  className,
  disabled,
}: FormInputProps<T>) => {
  const handleColorChange = (
    field: ControllerRenderProps<T, Path<T>>,
    newColor: string
  ) => {
    field.onChange(newColor);
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn(className)}>
          {label && (
            <FormLabel className={disabled ? 'text-typo-gray-quaternary' : ''}>
              {label}
            </FormLabel>
          )}
          <FormControl>
            {disabled ? (
              <div className="flex cursor-pointer items-center gap-2 border-none text-typo-gray-secondary">
                <p>{field.value || '-'}</p>
                {field.value && (
                  <Circle fill={field.value} color={field.value} size={15} />
                )}
              </div>
            ) : (
              <div className="relative flex items-center">
                <Input
                  type="text"
                  value={field.value || ''}
                  placeholder={placeholder}
                  onChange={(e) => field.onChange(e.target.value)}
                />
                <Popover>
                  <PopoverTrigger asChild>
                    <div className="absolute right-3 cursor-pointer rounded-full font-medium">
                      <Circle
                        fill={field.value || '#000000'}
                        color={field.value || '#000000'}
                        size={15}
                      />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <HexColorPicker
                      color={field.value || '#000000'}
                      onChange={(newColor) =>
                        handleColorChange(field, newColor)
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormColorPicker;
