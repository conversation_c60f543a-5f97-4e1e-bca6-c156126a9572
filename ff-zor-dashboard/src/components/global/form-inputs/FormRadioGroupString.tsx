// FormRadioGroup.tsx returns boolean values, this one returns string values

import { Control, FieldValues, Path } from 'react-hook-form';

import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

type RadioOption = {
  label: string;
  value: string;
};

type FormRadioGroupProps<T extends FieldValues> = {
  name: Path<T>;
  label?: string;
  control: Control<T>;
  disabled?: boolean;
  className?: string;
  options: RadioOption[];
  defaultValue?: string;
};

const FormRadioGroupString = <T extends FieldValues>({
  name,
  label,
  control,
  disabled,
  className,
  options,
}: FormRadioGroupProps<T>) => {
  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel htmlFor={name} className={'as-title-03 as-strong'}>
              {label}
            </FormLabel>
          )}
          {disabled ? (
            <div className="cursor-pointer border-none text-typo-gray-secondary">
              {field.value !== undefined
                ? options.find((opt) => opt.value === field.value)?.label || '-'
                : '-'}
            </div>
          ) : (
            <RadioGroup
              className="flex flex-row gap-4"
              value={field.value}
              onValueChange={field.onChange}
            >
              {options.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    id={`${name}-${option.value}`}
                    value={option.value}
                  />
                  <Label htmlFor={`${name}-${option.value}`}>
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export { FormRadioGroupString };
