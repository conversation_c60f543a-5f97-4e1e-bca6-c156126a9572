import { Check, ChevronsUpDown, Search, X } from 'lucide-react';
import * as React from 'react';
import { Control, FieldValues, Path } from 'react-hook-form';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/ui/utils';

export type Option = {
  value: string;
  label: string;
};

interface FormMultiSelectProps<T extends FieldValues> {
  name: Path<T>;
  label?: string;
  control: Control<T>;
  options: Option[];
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
  maxSelections?: number;
}

export const FormMultiSelect = <T extends FieldValues>({
  name,
  label,
  control,
  options,
  disabled = false,
  className,
  placeholder = 'Select options',
  emptyMessage = 'No results found.',
  searchPlaceholder = 'Search...',
  maxSelections,
}: FormMultiSelectProps<T>) => {
  // state
  const [open, setOpen] = React.useState(false);

  // render functions

  const renderSelectedOptions = (selectedOptions: Option[]) => {
    if (selectedOptions.length === 0) return '-';
    return selectedOptions.map((option) => option.label).join(', ');
  };

  const renderBadges = (
    selectedOptions: Option[],
    removeValue: (value: string) => void
  ) => {
    const handleKeyDown = (optionValue: string) => (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        removeValue(optionValue);
      }
    };

    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleClick = (optionValue: string) => (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      removeValue(optionValue);
    };

    return (
      <div className="flex flex-wrap gap-1">
        {selectedOptions.map((option) => (
          <Badge key={option.value} className="mr-1 px-1.5">
            {option.label}
            <div
              className="focus:ring-ring ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-offset-2"
              onKeyDown={handleKeyDown(option.value)}
              onMouseDown={handleMouseDown}
              onClick={handleClick(option.value)}
            >
              <X className="h-3 w-3" />
            </div>
          </Badge>
        ))}
      </div>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const selectedValues = (field.value || []) as string[];
        const selectedOptions = options.filter((option) =>
          selectedValues.includes(option.value)
        );

        const handleSelect = (value: string) => {
          const newValues = selectedValues.includes(value)
            ? selectedValues.filter((v) => v !== value)
            : [...selectedValues, value];

          if (maxSelections && newValues.length > maxSelections) return;
          field.onChange(newValues);
        };

        const removeValue = (value: string) => {
          field.onChange(selectedValues.filter((v) => v !== value));
        };

        const handleSelectOption = (value: string) => () => {
          handleSelect(value);
        };

        return (
          <FormItem className={cn(className)}>
            {label && (
              <FormLabel
                className={disabled ? 'text-typo-gray-quaternary' : ''}
              >
                {label}
              </FormLabel>
            )}
            <FormControl>
              {disabled ? (
                <div className="cursor-pointer border-none text-typo-gray-secondary">
                  {renderSelectedOptions(selectedOptions)}
                </div>
              ) : (
                <Popover open={open} onOpenChange={setOpen} modal>
                  <PopoverTrigger asChild>
                    <Button
                      size="lg"
                      role="combobox"
                      variant="outline"
                      className={cn(
                        'h-auto w-full justify-between px-2 py-2',
                        !selectedValues.length && 'text-muted-foreground'
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Search className="h-4 w-4" />
                        {selectedValues.length > 0
                          ? renderBadges(selectedOptions, removeValue)
                          : placeholder}
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                    <Command>
                      <CommandInput
                        placeholder={searchPlaceholder}
                        className="h-9"
                      />
                      <CommandList>
                        <CommandEmpty>{emptyMessage}</CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-auto">
                          {options.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.label}
                              onSelect={handleSelectOption(option.value)}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  selectedValues.includes(option.value)
                                    ? 'opacity-100'
                                    : 'opacity-0'
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              )}
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};
