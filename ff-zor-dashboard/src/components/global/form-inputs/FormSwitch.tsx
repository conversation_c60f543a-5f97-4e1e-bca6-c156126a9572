import { Control, FieldValues, Path } from 'react-hook-form';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/ui/utils';

interface FormSwitchProps<T extends FieldValues> {
  name: Path<T>;
  type?: string;
  label?: string;
  control: Control<T>;
  className?: string;
  hideOnOff?: boolean;
  custom_label?: string;
  onChangeHandler?: (isChanged: boolean) => void;
  disabled?: boolean;
}

const FormSwitch = <T extends FieldValues>({
  name,
  label,
  control,
  className,
  hideOnOff = false,
  custom_label,
  onChangeHandler,
  disabled = false,
}: FormSwitchProps<T>) => {
  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem
          className={cn('flex items-center space-x-1 space-y-2', className)}
        >
          {label && <FormLabel>{label}</FormLabel>}
          <div className="flex items-center gap-2">
            <FormControl>
              <Switch
                id={name}
                checked={field.value}
                disabled={disabled}
                onCheckedChange={(e) => {
                  field.onChange(e);
                  if (onChangeHandler) {
                    const isChanged = e !== field.value;
                    onChangeHandler(isChanged);
                  }
                }}
              />
            </FormControl>
            {custom_label ? (
              <FormLabel htmlFor={name}>{custom_label}</FormLabel>
            ) : (
              !hideOnOff && <FormLabel>{field.value ? 'On' : 'Off'}</FormLabel>
            )}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormSwitch;
