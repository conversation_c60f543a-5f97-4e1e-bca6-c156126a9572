import { Control, FieldValues, Path } from 'react-hook-form';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/ui/utils';

interface FormTextAreaProps<T extends FieldValues> {
  name: Path<T>;
  type?: string;
  label: string;
  control: Control<T>;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
}

const FormTextArea = <T extends FieldValues>({
  name,
  label,
  control,
  disabled,
  className,
  placeholder,
}: FormTextAreaProps<T>) => {
  return (
    <FormField
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem className={cn('w-full space-y-1', className)}>
          {label && (
            <FormLabel className={disabled ? 'text-typo-gray-quaternary' : ''}>
              {label}
            </FormLabel>
          )}
          <FormControl>
            {disabled ? (
              <div className="cursor-pointer border-none text-typo-gray-secondary">
                {field.value ? field.value : '-'}
              </div>
            ) : (
              <Textarea
                {...field}
                className="min-h-24 resize-none"
                spellCheck
                placeholder={placeholder}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormTextArea;
