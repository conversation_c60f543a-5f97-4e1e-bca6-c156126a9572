import { Draggable, Droppable } from '@hello-pangea/dnd';
import { type CSSProperties } from 'react';

import { cn } from '@/lib/ui/utils';
import { type PipeCard } from '@/pages/dashboard/data-center/PipelineTab';

import PipelineCard from './PipelineCard';

interface PipelineListProps {
  index: number;
  items: PipeCard[];
  expanded: boolean;
}

const LIST_STYLES = {
  base: {
    width: '100%',
    minWidth: '300px',
  },
  default: {
    background: 'white',
  },
  dragging: {
    background: 'lightblue',
  },
} as const;

const getListStyle = (isDraggingOver: boolean): CSSProperties => ({
  ...LIST_STYLES.base,
  ...(isDraggingOver ? LIST_STYLES.dragging : LIST_STYLES.default),
});

export const PipelineList = ({ items, index, expanded }: PipelineListProps) => {
  const containerClasses = cn(
    'duration-300',
    'overflow-auto',
    'transition-all',
    'h-[calc(100vh-350px)]',
    {
      hidden: expanded,
      'w-full min-w-[300px]': !expanded,
    }
  );

  return (
    <Droppable key={index} droppableId={String(index)}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          style={getListStyle(snapshot.isDraggingOver)}
          className={containerClasses}
          {...provided.droppableProps}
        >
          {items.map((item, itemIndex) => (
            <Draggable key={item.id} draggableId={item.id} index={itemIndex}>
              {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  {...provided.draggableProps}
                  {...provided.dragHandleProps}
                >
                  <PipelineCard item={item} isDragging={snapshot.isDragging} />
                </div>
              )}
            </Draggable>
          ))}
          {provided.placeholder}
        </div>
      )}
    </Droppable>
  );
};
