import { Button } from '@/components/ui/button';
import { linkOptions } from '@/constants/options';
import { cn } from '@/lib/ui/utils';

interface BadgeListProps {
  linkType: string | undefined;
  className?: string;
}

const BadgeList = ({ linkType, className }: BadgeListProps) => {
  const option = linkOptions.find((option) => option.value === linkType);
  const { label, color } = option || { label: '', color: '' };

  return (
    <Button
      style={{
        color,
        borderColor: color,
        backgroundColor: `${color}40`,
      }}
      className={cn('rounded-full border', className)}
      type="button"
    >
      {label}
    </Button>
  );
};

export default BadgeList;
