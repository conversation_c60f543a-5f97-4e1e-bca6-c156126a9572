import { OrganizationSwitcher, useOrganizationList } from '@clerk/clerk-react';

const CustomOrganizationSwitcher = () => {
  const { isLoaded, userMemberships, userInvitations } = useOrganizationList({
    userMemberships: { infinite: true, keepPreviousData: true },
    userInvitations: { infinite: true, keepPreviousData: true },
  });

  if (!isLoaded) {
    return null;
  }

  const appearance = {
    elements: {
      rootBox: {
        width: '100%',
        display: 'flex',
      },
      organizationSwitcherTrigger: {
        width: '100%',
        padding: '8px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        '&:hover': {
          background: 'var(--brand-500)',
        },
      },
      organizationSwitcherPopoverCard: {
        display:
          userMemberships.count >= 1 || userInvitations.count >= 1
            ? 'block'
            : 'none',
      },
      organizationPreviewMainIdentifier__organizationSwitcherTrigger: {
        color: 'var(--foreground)',
        fontSize: '14px',
      },
      organizationSwitcherPopoverFooter: {
        display: 'none',
      },
      organizationSwitcherPopoverActionButton__createOrganization: {
        display: 'none',
      },
      organizationSwitcherPopoverActionButton__manageOrganization: {
        display: 'none',
      },
    },
  } as const;

  return <OrganizationSwitcher hidePersonal appearance={appearance} />;
};

export default CustomOrganizationSwitcher;
