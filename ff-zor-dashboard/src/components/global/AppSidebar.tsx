import { useOrganization, useOrganizationList } from '@clerk/clerk-react';
import { Edit, ExternalLink, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import AddLinkDialog from '@/components/dialogs/AddLinkDialog';
import EditLinksDialog from '@/components/dialogs/EditLinksDialog';
import { Button } from '@/components/ui/button';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { SIDEBAR_MENU_ITEMS } from '@/constants/ui/sidebar';
import { useGetLinks } from '@/hooks/links/useGetLinks';
import useLinksStore from '@/stores/useLinksStore';

import Logo from './Logo';
import CustomOrganizationSwitcher from './OrganizationSwitcher';

const AppSidebar = () => {
  const location = useLocation();
  const { organization } = useOrganization();
  const { userMemberships } = useOrganizationList({
    userMemberships: { infinite: true, keepPreviousData: true },
  });
  const [addLinkOpen, setAddLinkOpen] = useState(false);
  const [editLinksOpen, setEditLinksOpen] = useState(false);

  const {
    result: linksResult,
    isLoading: isLinksLoading,
    isError: isLinksError,
    isSuccess: isLinksSuccess,
  } = useGetLinks();

  const { setLinks, setLoading, setError, getVisibleLinks } = useLinksStore();

  // Get user role for filtering links
  const userRole = userMemberships.data?.[0]?.role || '';

  // Check if user can manage links (only non-consultant and non-zee users)
  const canManageLinks =
    userRole &&
    !userRole.includes('partner_consultant') &&
    !userRole.includes('zee');

  // Update store when API call completes
  useEffect(() => {
    setLoading(isLinksLoading);
  }, [isLinksLoading, setLoading]);

  useEffect(() => {
    if (isLinksSuccess && linksResult?.data) {
      setLinks(linksResult.data);
    }
  }, [isLinksSuccess, linksResult, setLinks]);

  useEffect(() => {
    if (isLinksError) {
      setError('Failed to load links');
    }
  }, [isLinksError, setError]);

  // Get filtered links based on user role
  const visibleLinks = getVisibleLinks(userRole);

  return (
    <Sidebar className="z-20">
      <SidebarHeader className="max-xl:mt-5">
        <Logo width={130} height={34} />
      </SidebarHeader>
      <SidebarSeparator />
      <div className="flex py-3">
        <CustomOrganizationSwitcher />
      </div>
      <SidebarSeparator className="mb-3" />
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {SIDEBAR_MENU_ITEMS.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton
                    asChild
                    isActive={location.pathname === item.path}
                    disabled={!organization}
                  >
                    <Link to={item.path}>
                      <item.icon />
                      <span>{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Custom Links Section */}
        {organization && visibleLinks.length > 0 && (
          <>
            <SidebarSeparator />
            <SidebarGroup>
              <SidebarGroupLabel>Quick Links</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {visibleLinks.map((link) => (
                    <SidebarMenuItem key={link.id}>
                      <SidebarMenuButton asChild>
                        <a
                          rel="noopener noreferrer"
                          href={
                            link.link.startsWith('http')
                              ? link.link
                              : `https://${link.link}`
                          }
                          target="_blank"
                          className="flex items-center gap-2"
                        >
                          <ExternalLink className="size-4" />
                          <span>{link.title}</span>
                        </a>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}
        <SidebarSeparator />
      </SidebarContent>
      <SidebarFooter>
        {canManageLinks && (
          <div className="flex flex-col gap-2 p-2">
            <Button
              size="sm"
              variant="ghost"
              disabled={!organization}
              className="flex items-center justify-between gap-2 hover:bg-primary hover:text-white"
              onClick={() => setAddLinkOpen(true)}
            >
              Add Link
              <Plus className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              disabled={!organization}
              className="flex items-center justify-between gap-2 hover:bg-primary hover:text-white"
              onClick={() => setEditLinksOpen(true)}
            >
              Edit Links
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        )}
      </SidebarFooter>

      {/* Dialogs */}
      <AddLinkDialog open={addLinkOpen} setOpen={setAddLinkOpen} />
      <EditLinksDialog open={editLinksOpen} setOpen={setEditLinksOpen} />
    </Sidebar>
  );
};

export default AppSidebar;
