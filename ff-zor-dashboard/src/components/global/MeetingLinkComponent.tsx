import { Check, Copy, Edit2, Loader2, X } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from '@/hooks/ui/use-toast';
import { useGetZorMeetingLink } from '@/hooks/zor/useGetZorMeetingLink';
import { useUpdateZorMeetingLink } from '@/hooks/zor/useUpdateZorMeetingLink';
import logger from '@/services/logger.service';
import useZorMeetingLinkStore from '@/stores/useZorMeetingLinkStore';

const MeetingLinkComponent = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState('');

  const { meetingLink, setMeetingLink } = useZorMeetingLinkStore();
  const displayLink = meetingLink || 'No meeting link set';

  const {
    result: meetingLinkResult,
    isError: isGetError,
    isSuccess: isGetSuccess,
    isLoading: isGetLoading,
    isRefetching,
    refetch,
  } = useGetZorMeetingLink();

  const {
    mutate: updateMeetingLink,
    isError: isUpdateError,
    isSuccess: isUpdateSuccess,
    isLoading: isUpdateLoading,
  } = useUpdateZorMeetingLink();

  const isLoading = isGetLoading || isRefetching;

  useEffect(() => {
    if (isGetSuccess && meetingLinkResult?.data) {
      if (meetingLinkResult.data.meeting_link) {
        setMeetingLink(meetingLinkResult.data.meeting_link);
      }
    }
  }, [isGetSuccess, meetingLinkResult, setMeetingLink]);

  useEffect(() => {
    if (isGetError) {
      toast({
        title: 'Error loading meeting link',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isGetError]);

  useEffect(() => {
    if (isUpdateError) {
      toast({
        title: 'Error updating meeting link',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isUpdateError]);

  useEffect(() => {
    if (isUpdateSuccess) {
      toast({
        title: 'Meeting link updated successfully',
        variant: 'default',
      });
      setIsEditing(false);
      refetch();
    }
  }, [isUpdateSuccess, refetch]);

  const handleEditClick = () => {
    setEditValue(meetingLink || '');
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (editValue.trim()) {
      try {
        await updateMeetingLink({ meeting_link: editValue.trim() });
      } catch (error) {
        logger.error('Failed to update meeting link', error as Error, {
          meetingLink: editValue.trim(),
        });
      }
    }
  };

  const handleCancel = () => {
    setEditValue('');
    setIsEditing(false);
  };

  const handleCopy = async () => {
    if (!meetingLink) {
      toast({
        title: 'No meeting link to copy',
        description: 'Please set a meeting link first',
        variant: 'destructive',
      });
      return;
    }

    try {
      await navigator.clipboard.writeText(meetingLink);
      toast({
        title: 'Meeting link copied to clipboard',
        variant: 'default',
      });
    } catch (error) {
      logger.error('Failed to copy meeting link to clipboard', error as Error, {
        meetingLink,
      });
      toast({
        title: 'Failed to copy link',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">Meeting Link:</span>
        <Input
          value={editValue}
          className="h-8 w-64 text-sm"
          placeholder="Enter meeting link"
          onChange={(e) => setEditValue(e.target.value)}
        />
        <Button
          size="sm"
          variant="outline"
          onClick={handleSave}
          disabled={isUpdateLoading}
          className="h-8 w-8 p-0"
        >
          {isUpdateLoading ? (
            <Loader2 className="size-3 animate-spin" />
          ) : (
            <Check className="size-3" />
          )}
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={handleCancel}
          disabled={isUpdateLoading}
          className="h-8 w-8 p-0"
        >
          <X className="size-3" />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">Meeting Link:</span>
      {isLoading ? (
        <Loader2 className="size-3 animate-spin" />
      ) : (
        <span
          className={`text-sm ${meetingLink ? 'text-blue-600' : 'text-gray-400'}`}
        >
          {displayLink}
        </span>
      )}
      <Button
        size="sm"
        variant="outline"
        onClick={handleEditClick}
        className="h-6 w-6 p-0"
        disabled={isLoading}
      >
        <Edit2 className="size-3" />
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={handleCopy}
        className="h-6 w-6 p-0"
        disabled={!meetingLink || isLoading}
      >
        <Copy className="size-3" />
      </Button>
    </div>
  );
};

export default MeetingLinkComponent;
