import { User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import { cn } from '@/lib/ui/utils';
import { PipeCard } from '@/pages/dashboard/data-center/PipelineTab';

interface PipelineCardProps {
  item: PipeCard;
  isDragging?: boolean;
}

const PipelineCard = ({ item, isDragging = false }: PipelineCardProps) => {
  const navigate = useNavigate();
  const { name, state, avatar, consultant, last_online } = item;

  const handleCardClick = () => {
    navigate(`/dashboard/zee-profile/${item.id}`);
  };

  return (
    <div
      onClick={handleCardClick}
      className={cn(
        'flex cursor-pointer items-start space-x-4 border-b border-secondary p-4 transition-colors hover:bg-gray-50',
        isDragging && 'rounded-lg border-2 border-primary bg-white shadow-lg'
      )}
    >
      <div className="rounded-full bg-muted">
        {avatar ? (
          <img
            src={avatar}
            alt={`${name}'s avatar`}
            className="h-10 w-10 rounded-full"
          />
        ) : (
          <User className="m-2" />
        )}
      </div>
      <div className="space-y-2">
        <div className="as-body-02 font-medium text-typo-primary">{name}</div>
        <div className="text-sm text-typo-gray-tertiary">
          <span className="font-medium text-typo-gray-secondary">State: </span>
          {state || 'N/A'}
        </div>
        {consultant && (
          <div className="text-sm text-typo-gray-tertiary">
            <span className="font-medium text-typo-gray-secondary">
              Consultant:{' '}
            </span>
            {consultant.full_name}
          </div>
        )}
        <div className="text-sm text-typo-gray-tertiary">
          <span className="font-medium text-typo-gray-secondary">
            Last time logged on:{' '}
          </span>
          {last_online || 'Never online'}
        </div>
      </div>
    </div>
  );
};

export default PipelineCard;
