import { Download, Eye, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { NoteAttachment } from '@/types/note.type';

import { DocumentDetailIcon } from '../common/DocumentDetailIcon';

interface NotesAttachmentsTableProps {
  data: NoteAttachment[];
}

const NotesAttachmentsTable = ({ data }: NotesAttachmentsTableProps) => {
  const [downloadingId, setDownloadingId] = useState<string | null>(null);

  const handleDownload = async (url: string, name: string, id: string) => {
    try {
      setDownloadingId(id);

      const response = await fetch(url);
      if (!response.ok) throw new Error('Download failed');

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = blobUrl;
      link.setAttribute('download', name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('Error downloading file:', error);
      window.open(url, '_blank');
    } finally {
      setDownloadingId(null);
    }
  };

  const handleViewDocument = (url: string) => {
    if (!url) return;
    window.open(url, '_blank');
  };

  const createDownloadHandler =
    (url: string, name: string, id: string) => () => {
      handleDownload(url, name, id);
    };

  const createViewHandler = (url: string) => () => {
    handleViewDocument(url);
  };

  const renderMobileView = () => (
    <div className="space-y-4 md:hidden">
      {data.map((item) => (
        <div
          key={item.id}
          className="flex flex-col gap-4 rounded-xl border border-border bg-white p-4"
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <p className="mt-1 text-sm capitalize text-typo-gray-secondary">
                {item.note_type.replace('-', ' ')}
              </p>
              <p className="mt-1 text-xs text-typo-gray-tertiary">
                by {item.note_author_name}
              </p>
            </div>
          </div>
          <div className="flex items-center justify-between gap-4">
            <DocumentDetailIcon
              url={item.file_url}
              name={item.file_name}
              size={item.file_size}
            />
            <div className="flex items-center gap-1">
              <Button
                size="icon"
                variant="outline"
                disabled={downloadingId === item.id}
                onClick={createDownloadHandler(
                  item.file_url,
                  item.file_name,
                  item.id
                )}
              >
                {downloadingId === item.id ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Download className="size-4" />
                )}
              </Button>
              <Button
                size="icon"
                variant="outline"
                onClick={createViewHandler(item.file_url)}
              >
                <Eye className="size-4" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderDesktopView = () => (
    <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted max-md:hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="as-title-04 px-4">File</TableHead>
            <TableHead className="as-title-04 px-4">Note Type</TableHead>
            <TableHead className="as-title-04 px-4">Uploaded By</TableHead>
            <TableHead className="as-title-04 px-4 text-right">
              Action
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="bg-white">
          {data.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={4}
                className="text-center text-muted-foreground"
              >
                No Notes Attachments
              </TableCell>
            </TableRow>
          ) : (
            data.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="px-4">
                  <DocumentDetailIcon
                    url={item.file_url}
                    name={item.file_name}
                    size={item.file_size}
                  />
                </TableCell>
                <TableCell className="px-4">
                  <p className="text-sm capitalize">
                    {item.note_type.replace('-', ' ')}
                  </p>
                </TableCell>
                <TableCell className="px-4">
                  <p className="text-sm">{item.note_author_name}</p>
                </TableCell>
                <TableCell className="px-4">
                  <div className="float-right flex items-center gap-1">
                    <Button
                      size="icon"
                      variant="outline"
                      disabled={downloadingId === item.id}
                      onClick={createDownloadHandler(
                        item.file_url,
                        item.file_name,
                        item.id
                      )}
                    >
                      {downloadingId === item.id ? (
                        <Loader2 className="size-4 animate-spin" />
                      ) : (
                        <Download className="size-4" />
                      )}
                    </Button>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={createViewHandler(item.file_url)}
                    >
                      <Eye className="size-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <>
      {renderMobileView()}
      {renderDesktopView()}
    </>
  );
};

export { NotesAttachmentsTable };
