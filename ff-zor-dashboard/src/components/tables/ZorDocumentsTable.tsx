import { Download, Eye, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ZorDocument } from '@/types/zor.document.type';

import { DocumentDetailIcon } from '../common/DocumentDetailIcon';

interface ZorDocumentsTableProps {
  data: ZorDocument[];
}

const ZorDocumentsTable = ({ data }: ZorDocumentsTableProps) => {
  const [downloadingId, setDownloadingId] = useState<string | null>(null);

  const handleDownload = async (url: string, name: string, id: string) => {
    try {
      setDownloadingId(id);

      const response = await fetch(url);
      if (!response.ok) throw new Error('Download failed');

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = blobUrl;
      link.setAttribute('download', name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error('Error downloading file:', error);
      window.open(url, '_blank');
    } finally {
      setDownloadingId(null);
    }
  };

  const handleViewDocument = (url: string) => {
    if (!url) return;
    window.open(url, '_blank');
  };

  const createDownloadHandler =
    (url: string, name: string, id: string) => () => {
      handleDownload(url, name, id);
    };

  const createViewHandler = (url: string) => () => {
    handleViewDocument(url);
  };

  const renderMobileView = () => (
    <div className="space-y-4 md:hidden">
      {data.map((item) => (
        <div
          key={item.id}
          className="flex flex-col gap-4 rounded-xl border border-border bg-white p-4"
        >
          <p className="as-title-03 as-strong">{item.name}</p>
          <div className="flex items-center justify-between gap-4">
            <DocumentDetailIcon
              url={item.url}
              name={item.name}
              size={item.size}
            />
            <div className="flex items-center gap-1">
              <Button
                size="icon"
                variant="outline"
                disabled={downloadingId === item.id}
                onClick={createDownloadHandler(item.url, item.name, item.id)}
              >
                {downloadingId === item.id ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Download className="size-4" />
                )}
              </Button>
              <Button
                size="icon"
                variant="outline"
                onClick={createViewHandler(item.url)}
              >
                <Eye className="size-4" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderDesktopView = () => (
    <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted max-md:hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="as-title-04 px-4">Name</TableHead>
            <TableHead className="as-title-04">File</TableHead>
            <TableHead className="as-title-04 px-4 text-right">
              Action
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="bg-white">
          {data.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={5}
                className="text-center text-muted-foreground"
              >
                No Documents
              </TableCell>
            </TableRow>
          ) : (
            data.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="px-4 text-typo-gray-secondary">
                  {item.name}
                </TableCell>
                <TableCell className="px-4">
                  <DocumentDetailIcon
                    url={item.url}
                    name={item.name}
                    size={item.size}
                  />
                </TableCell>
                <TableCell className="px-4">
                  <div className="float-right flex items-center gap-1">
                    <Button
                      size="icon"
                      variant="outline"
                      disabled={downloadingId === item.id}
                      onClick={createDownloadHandler(
                        item.url,
                        item.name,
                        item.id
                      )}
                    >
                      {downloadingId === item.id ? (
                        <Loader2 className="size-4 animate-spin" />
                      ) : (
                        <Download className="size-4" />
                      )}
                    </Button>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={createViewHandler(item.url)}
                    >
                      <Eye className="size-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <>
      {renderMobileView()}
      {renderDesktopView()}
    </>
  );
};

export { ZorDocumentsTable };
