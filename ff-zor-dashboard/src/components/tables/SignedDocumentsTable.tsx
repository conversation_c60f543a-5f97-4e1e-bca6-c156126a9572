import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { formatDate } from '@/lib/date';
import { SignedDocument } from '@/types/document.type';

interface SignedDocumentsTableProps {
  data: SignedDocument[];
}

const SignedDocumentsTable = ({ data }: SignedDocumentsTableProps) => {
  const handleDocumentClick = (url: string) => {
    window.open(url, '_blank');
  };

  const createDocumentClickHandler = (url: string) => () => {
    handleDocumentClick(url);
  };

  return (
    <>
      <div className="space-y-4 md:hidden">
        {data.map((item: SignedDocument) => (
          <div
            key={item.id}
            className="flex flex-col gap-4 rounded-xl border border-border bg-white p-4"
          >
            <p className="as-title-03 as-strong">{item.name}</p>
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <p className="as-body-04 text-muted-foreground">Stage</p>
                <p className="as-body-04">{item.stage_name}</p>
              </div>
              <div className="flex items-center justify-between">
                <p className="as-body-04 text-muted-foreground">Checklist</p>
                <p className="as-body-04">{item.checklist}</p>
              </div>
              <div className="flex items-center justify-between">
                <p className="as-body-04 text-muted-foreground">Signed At</p>
                <p className="as-body-04">{formatDate(item.signedAt)}</p>
              </div>
            </div>
            <div
              className="cursor-pointer text-primary hover:underline"
              onClick={createDocumentClickHandler(item.url)}
            >
              View Document
            </div>
          </div>
        ))}
      </div>
      <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted max-md:hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="as-title-04 px-4">Name</TableHead>
              <TableHead className="as-title-04">Stage</TableHead>
              <TableHead className="as-title-04">Checklist</TableHead>
              <TableHead className="as-title-04">Signed At</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="bg-white">
            {data.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={4}
                  className="text-center text-muted-foreground"
                >
                  No Documents
                </TableCell>
              </TableRow>
            )}
            {data.map((item: SignedDocument) => (
              <TableRow key={item.id}>
                <TableCell className="px-4">
                  <div
                    className="cursor-pointer text-typo-gray-secondary hover:text-primary"
                    onClick={createDocumentClickHandler(item.url)}
                  >
                    {item.name}
                  </div>
                </TableCell>
                <TableCell>{item.stage_name}</TableCell>
                <TableCell>{item.checklist}</TableCell>
                <TableCell>{formatDate(item.signedAt)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </>
  );
};

export { SignedDocumentsTable };
