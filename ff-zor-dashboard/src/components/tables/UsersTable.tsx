import { useOrganization, useUser } from '@clerk/clerk-react';
import { Edit, Loader2, X } from 'lucide-react';
import { useEffect, useState } from 'react';

import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { UserPermissionOptions, UserTypeOptions } from '@/constants/options';
import { useRevokeOrgInvitation } from '@/hooks/auth/useSendOrgInvitation';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import logger from '@/services/logger.service';

export interface OrganizationMembershipResource {
  id: string;
  name: string;
  email: string;
  status?: 'pending' | 'accepted';
  imageUrl: string;
  userType: 'leadership' | 'partner';
  permissionLevel: 'owner' | 'administrator' | 'consultant';
}

interface UsersTableProps {
  memberships: OrganizationMembershipResource[];
  canRevokeInvitations?: boolean;
  onRefetch?: () => Promise<void>;
}

const UsersTable = ({
  memberships,
  canRevokeInvitations = false,
  onRefetch,
}: UsersTableProps) => {
  const [users, setUsers] = useState(memberships);
  const [isSaving, setIsSaving] = useState(false);
  const [editingUser, setEditingUser] = useState('');
  const [editingUserType, setEditingUserType] = useState('');
  const [editingUserPermission, setEditingUserPermission] = useState('');
  const [revokingInvitationId, setRevokingInvitationId] = useState<string>('');

  const { user: me } = useUser();
  const {
    isLoaded,
    memberships: organizationMemberships,
    organization,
  } = useOrganization({
    memberships: { infinite: true, keepPreviousData: true },
  });

  const {
    mutate: revokeOrgInvitation,
    isError: isRevokeError,
    isSuccess: isRevokeSuccess,
  } = useRevokeOrgInvitation();

  useEffect(() => {
    setUsers(memberships);
  }, [memberships]);

  useEffect(() => {
    if (editingUserType && editingUserPermission) {
      if (
        editingUserType === 'leadership' &&
        editingUserPermission === 'consultant'
      ) {
        setEditingUserPermission('');
      } else if (
        editingUserType === 'partner' &&
        editingUserPermission === 'owner'
      ) {
        setEditingUserPermission('');
      }
    }
  }, [editingUserType, editingUserPermission]);

  useEffect(() => {
    if (isRevokeSuccess) {
      toast({
        title: 'Invitation revoked',
        description: 'The invitation has been successfully revoked.',
      });
      setRevokingInvitationId('');
      if (onRefetch) {
        onRefetch();
      }
    }
  }, [isRevokeSuccess, onRefetch]);

  useEffect(() => {
    if (isRevokeError) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Failed to revoke invitation. Please try again later.',
      });
    }
  }, [isRevokeError]);

  const handleSave = async () => {
    if (!isLoaded) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again later',
      });
      return;
    }

    if (!editingUserPermission) {
      toast({
        variant: 'destructive',
        title: 'Permission Required',
        description: 'Please select a user permission before saving.',
      });
      return;
    }

    const user = organizationMemberships?.data?.find(
      (membership) => membership.id === editingUser
    );

    if (!user) return;

    setIsSaving(true);
    try {
      await user.update({
        role: `org:${editingUserType}_${editingUserPermission}`,
      });

      toast({
        title: 'User updated',
        description: 'User has been updated successfully',
      });
    } catch (error) {
      logger.error(
        'Failed to update user role in organization',
        error as Error,
        {
          userId: editingUser,
          role: `org:${editingUserType}_${editingUserPermission}`,
        }
      );
      toast({
        variant: 'destructive',
        title: 'Permission Denied',
        description: "You do not have permission to update this user's role",
      });
    } finally {
      setIsSaving(false);
      resetEditingState();
    }
  };

  const resetEditingState = () => {
    setEditingUser('');
    setEditingUserType('');
    setEditingUserPermission('');
  };

  const handleRevokeInvitation = async (
    user: OrganizationMembershipResource
  ) => {
    if (!organization || !me?.id) {
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Organization or user information not found.',
      });
      return;
    }

    setRevokingInvitationId(user.id);
    try {
      await revokeOrgInvitation({
        invitationId: user.id,
        orgId: organization.id,
        requestingUserId: me.id,
      });
    } catch (error) {
      logger.error('Failed to revoke organization invitation', error as Error, {
        invitationId: user.id,
        orgId: organization.id,
        requestingUserId: me.id,
      });
    } finally {
      setRevokingInvitationId('');
    }
  };

  const handleEdit = (user: OrganizationMembershipResource) => {
    setEditingUser(user.id);
    setEditingUserType(user.userType);
    setEditingUserPermission(user.permissionLevel);
  };

  const handleEditClick =
    (user: OrganizationMembershipResource) => (): void => {
      handleEdit(user);
    };

  const getFilteredPermissionOptions = () => {
    if (!editingUserType) return UserPermissionOptions;
    return editingUserType === 'leadership'
      ? UserPermissionOptions.filter((option) => option.value !== 'consultant')
      : UserPermissionOptions.filter((option) => option.value !== 'owner');
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>User type</TableHead>
          <TableHead>User permission</TableHead>
          <TableHead>Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {users.map((user) => (
          <TableRow key={user.id}>
            <TableCell className="font-medium">
              <div className="flex items-center justify-start gap-2">
                <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-full border">
                  {user.imageUrl ? (
                    <img src={user.imageUrl} alt={user.name} />
                  ) : (
                    <EmptyAvatar className="!h-full !w-full" />
                  )}
                </div>
                <p
                  className={cn({
                    'text-muted-foreground': user.name === 'N/A',
                  })}
                >
                  {user.name}
                </p>
              </div>
            </TableCell>
            <TableCell>{user.email}</TableCell>
            <TableCell>
              <Select
                value={
                  editingUser === user.id ? editingUserType : user.userType
                }
                disabled={editingUser !== user.id}
                defaultValue={user.userType}
                onValueChange={setEditingUserType}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder="Select User Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {UserTypeOptions.map((type, index) => (
                      <SelectItem value={type.value} key={`type_${index}`}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </TableCell>
            <TableCell>
              <Select
                value={
                  editingUser === user.id
                    ? editingUserPermission
                    : user.permissionLevel
                }
                disabled={editingUser !== user.id}
                defaultValue={user.permissionLevel}
                onValueChange={setEditingUserPermission}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder="Select User Permission" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {getFilteredPermissionOptions().map((permission, index) => (
                      <SelectItem
                        value={permission.value}
                        key={`permission_${index}`}
                      >
                        {permission.label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </TableCell>
            <TableCell className="min-w-40">
              {me?.primaryEmailAddress?.emailAddress ===
              user.email ? null : user?.status === 'pending' ? (
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground">Invitation sent</p>
                  {canRevokeInvitations && (
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => handleRevokeInvitation(user)}
                      disabled={revokingInvitationId === user.id}
                      className="h-8 w-8"
                    >
                      {revokingInvitationId === user.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <X className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                </div>
              ) : editingUser !== user.id ? (
                <Button
                  size="icon"
                  variant="outline"
                  onClick={handleEditClick(user)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={isSaving || !editingUserPermission}
                  >
                    {isSaving && <Loader2 className="size-4 animate-spin" />}
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={resetEditingState}
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default UsersTable;
