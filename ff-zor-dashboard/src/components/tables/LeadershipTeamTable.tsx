import { Edit, Loader2, Trash2 } from 'lucide-react';

import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/ui/utils';
import { ZorMember } from '@/types/zor.member.type';

interface LeadershipTeamTableProps {
  data: ZorMember[];
  isUpdating?: boolean;
  updatingMemberId?: string | null;
  onEdit?: (item: ZorMember) => void;
  onDelete?: (item: ZorMember) => void;
  onTogglePublish?: (item: ZorMember, isPublic: boolean) => void;
}

const LeadershipTeamTable = ({
  data,
  isUpdating,
  updatingMemberId,
  onEdit,
  onDelete,
  onTogglePublish,
}: LeadershipTeamTableProps) => {
  const handleEditClick = (item: ZorMember) => () => {
    onEdit?.(item);
  };

  const handleDeleteClick = (item: ZorMember) => () => {
    onDelete?.(item);
  };

  const handleTogglePublish = (item: ZorMember) => (checked: boolean) => {
    onTogglePublish?.(item, checked);
  };
  return (
    <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="as-title-04 px-4">Name</TableHead>
            <TableHead className="as-title-04">Position in Company</TableHead>
            <TableHead className="as-title-04">Tag</TableHead>
            <TableHead className="as-title-04">Publish Profile</TableHead>
            <TableHead className="as-title-04 px-4 text-right">
              Action
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="bg-white">
          {data.length === 0 && (
            <TableRow>
              <TableCell
                colSpan={5}
                className="text-center text-muted-foreground"
              >
                No Leadership Team Members
              </TableCell>
            </TableRow>
          )}
          {data.map((item: ZorMember) => (
            <TableRow key={item.id}>
              <TableCell className="px-4 text-typo-gray-secondary">
                <div className="flex items-center justify-start gap-2">
                  <div className="h-10 w-10 flex-shrink-0 overflow-hidden rounded-full border">
                    {item.photo_url ? (
                      <img src={item.photo_url} alt={item.full_name} />
                    ) : (
                      <EmptyAvatar className="!h-full !w-full" />
                    )}
                  </div>
                  <p>{item.full_name}</p>
                </div>
              </TableCell>
              <TableCell className="text-typo-gray-secondary">
                {item.position}
              </TableCell>
              <TableCell className="text-typo-gray-secondary">
                <Badge
                  variant="outline"
                  className={cn(
                    item.is_added && 'border-fuchsia-strong text-fuchsia-strong'
                  )}
                >
                  {item.is_added ? 'Added' : 'User'}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={item.is_public}
                    onCheckedChange={handleTogglePublish(item)}
                  />
                  {updatingMemberId === item.id && isUpdating && (
                    <Loader2 className="size-4 animate-spin" />
                  )}
                </div>
              </TableCell>
              <TableCell className="flex items-center justify-end gap-1 px-4">
                {item.is_added && (
                  <>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={handleEditClick(item)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="icon"
                      variant="destructive"
                      onClick={handleDeleteClick(item)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default LeadershipTeamTable;
