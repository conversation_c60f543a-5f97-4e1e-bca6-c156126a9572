import Slider from 'rc-slider';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON>, { Area, Point } from 'react-easy-crop';
import { BsArrowClockwise, BsDashLg, BsPlusLg, BsXLg } from 'react-icons/bs';

import { Button } from '@/components/ui/button';
import { getCroppedImg } from '@/lib/ui/images';

interface ImageEditorProps {
  imageURL: string;
  onSaveImage: (newImage: File | null) => void;
}

const ImageEditor = ({ imageURL, onSaveImage }: ImageEditorProps) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [croppedArea, setCroppedArea] = useState<Area>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  const onCropComplete = (_: Area, croppedAreaPixels: Area): void => {
    setCroppedArea(croppedAreaPixels);
  };

  const handleCloseClick = (): void => {
    onSaveImage(null);
  };

  const handleZoomChange = (value: number | number[]): void => {
    setZoom(typeof value === 'number' ? value : value[0]);
  };

  const onSaveBtnClicked = async (): Promise<void> => {
    if (imageURL && croppedArea) {
      const croppedImage = await getCroppedImg(
        imageURL,
        croppedArea,
        rotate * 90
      );
      if (croppedImage?.file) {
        const imageFile = new File(
          [croppedImage.file],
          `img-${Date.now()}.png`,
          {
            type: 'image/png',
          }
        );
        onSaveImage(imageFile);
      }
    }
  };

  const handleZoomDecrease = (): void => {
    setZoom(Math.max(0, zoom - 0.1));
  };

  const handleZoomIncrease = (): void => {
    setZoom(Math.min(10, zoom + 0.1));
  };

  const handleRotate = (): void => {
    setRotate(rotate + 1);
  };

  return (
    <div className="fixed left-0 top-0 z-50 flex min-h-screen w-screen items-center justify-center bg-black bg-opacity-30 px-3">
      <div className="xs:w-full flex w-[640px] flex-col items-center">
        <div className="flex h-[64px] w-full items-center justify-between rounded-t-xl bg-white px-4">
          <BsXLg className="cursor-pointer" onClick={handleCloseClick} />
          <Button type="button" onClick={onSaveBtnClicked}>
            Save
          </Button>
        </div>
        <div className="relative h-[480px] w-full overflow-hidden bg-black">
          <EasyCropper
            crop={crop}
            zoom={zoom}
            image={imageURL}
            aspect={1 / 1}
            rotation={rotate * 90}
            onCropChange={setCrop}
            onZoomChange={setZoom}
            onCropComplete={onCropComplete}
          />
        </div>
        <div className="flex h-[64px] w-full gap-3 rounded-b-xl bg-white px-4">
          <div
            className="border-gray-5 flex cursor-pointer items-center gap-2 border-r px-3 py-2"
            onClick={handleRotate}
          >
            <BsArrowClockwise />
            Rotate
          </div>
          <div className="flex w-full items-center gap-4">
            <BsDashLg className="cursor-pointer" onClick={handleZoomDecrease} />
            <Slider
              min={0.5}
              max={5}
              step={0.1}
              value={zoom}
              onChange={handleZoomChange}
            />
            <BsPlusLg className="cursor-pointer" onClick={handleZoomIncrease} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageEditor;
