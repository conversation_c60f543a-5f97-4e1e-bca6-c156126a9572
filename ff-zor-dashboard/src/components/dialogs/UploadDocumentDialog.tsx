import { useOrganization } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import UploadZorDocumentFormSchema from '@/components/forms/schemas/UploadZorDocumentFormSchema';
import FileUploadPanel from '@/components/global/form-inputs/FileUploadPanel';
import FormInput from '@/components/global/form-inputs/FormInput';
import FormSwitch from '@/components/global/form-inputs/FormSwitch';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { uploadToStorage } from '@/helpers/storage.helper';
import { toast } from '@/hooks/ui/use-toast';
import { useCreateZorDocument } from '@/hooks/zor-document/useCreateZorDocument';
import logger from '@/services/logger.service';
import useZorDocumentStore from '@/stores/useZorDocumentStore';

interface UploadDocumentDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const UploadDocumentDialog = ({ open, setOpen }: UploadDocumentDialogProps) => {
  const uploadForm = useForm<z.infer<typeof UploadZorDocumentFormSchema>>({
    resolver: zodResolver(UploadZorDocumentFormSchema),
  });

  const { organization } = useOrganization();
  const { zorDocuments, setZorDocuments } = useZorDocumentStore();

  const [isUploading, setIsUploading] = useState(false);

  const {
    mutate: createZorDocument,
    result: createZorDocumentResult,
    isError: isCreateZorDocumentError,
    isSuccess: isCreateZorDocumentSuccess,
    isLoading: isCreateZorDocumentLoading,
  } = useCreateZorDocument();

  const handleSubmit = async (
    values: z.infer<typeof UploadZorDocumentFormSchema>
  ): Promise<void> => {
    try {
      const uploadedDocument = await uploadToStorage({
        file: values.file,
        bucketName: 'documents',
        folderName: organization?.id ?? '',
        setIsUploading,
      });

      if (!uploadedDocument) {
        return;
      }

      createZorDocument({
        file_name: values.file_name,
        file_url: uploadedDocument.file_url,
        file_size: uploadedDocument.file_size,
        for_stage: values.for_stage ?? false,
        for_franchisee: values.for_franchisee ?? false,
        for_consultant: values.for_consultant ?? false,
      });
    } catch (error) {
      logger.error(
        'Failed to handle document upload submission',
        error as Error,
        { fileName: values.file_name }
      );

      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    uploadForm.reset();
    setOpen(false);
  };

  useEffect(() => {
    if (isCreateZorDocumentError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isCreateZorDocumentError]);

  useEffect(() => {
    if (isCreateZorDocumentSuccess && createZorDocumentResult?.data) {
      setZorDocuments([...(zorDocuments ?? []), createZorDocumentResult.data]);
      setOpen(false);

      uploadForm.reset();
    }
  }, [
    createZorDocumentResult,
    isCreateZorDocumentSuccess,
    setOpen,
    setZorDocuments,
  ]);

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) uploadForm.reset();
      }}
    >
      <DialogContent className="w-[480px] bg-white">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
        </DialogHeader>

        <Form {...uploadForm}>
          <form
            onSubmit={uploadForm.handleSubmit(handleSubmit)}
            className="space-y-2"
          >
            <FileUploadPanel name="file" control={uploadForm.control} />
            <FormInput
              type="text"
              name="file_name"
              label="Title"
              control={uploadForm.control}
            />
            <div className="flex items-center justify-between">
              <Label htmlFor="for_stage" className="text-typo-gray-tertiary">
                Stage Link Document
              </Label>
              <FormSwitch
                name="for_stage"
                control={uploadForm.control}
                hideOnOff
              />
            </div>
            <div className="flex items-center justify-between">
              <Label
                htmlFor="for_franchisee"
                className="text-typo-gray-tertiary"
              >
                Add to Franchisees Dashboard
              </Label>
              <FormSwitch
                name="for_franchisee"
                control={uploadForm.control}
                hideOnOff
              />
            </div>
            <div className="flex items-center justify-between pb-5">
              <Label
                htmlFor="for_consultant"
                className="text-typo-gray-tertiary"
              >
                Add to Consultant Dashboard
              </Label>
              <FormSwitch
                name="for_consultant"
                control={uploadForm.control}
                hideOnOff
              />
            </div>

            <DialogFooter>
              <div className="flex w-full justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isUploading || isCreateZorDocumentLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isUploading || isCreateZorDocumentLoading}
                >
                  {(isUploading || isCreateZorDocumentLoading) && (
                    <Loader2 className="size-4 animate-spin" />
                  )}
                  Upload
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default UploadDocumentDialog;
