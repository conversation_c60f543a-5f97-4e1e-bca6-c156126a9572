import InviteUserForm from '@/components/forms/profile/InviteUserForm';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface InviteUserDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSuccess?: () => Promise<void>;
}

const InviteUserDialog = ({
  open,
  setOpen,
  onSuccess,
}: InviteUserDialogProps) => {
  const handleCloseDialog = (): void => {
    setOpen(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) handleCloseDialog();
      }}
    >
      <DialogContent className="gap-0 p-0">
        <DialogHeader className="border-b border-border p-4">
          <DialogTitle>Invite user</DialogTitle>
        </DialogHeader>
        <InviteUserForm closeDialog={handleCloseDialog} onSuccess={onSuccess} />
      </DialogContent>
    </Dialog>
  );
};

export default InviteUserDialog;
