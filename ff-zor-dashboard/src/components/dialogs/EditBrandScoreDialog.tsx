import BrandScoreForm from '@/components/forms/profile/BrandScoreForm';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface EditBrandScoreDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  percentage: number;
  setPercentage: React.Dispatch<React.SetStateAction<number>>;
  zeeId: string;
  hasExistingScore: boolean;
}

const EditBrandScoreDialog = ({
  open,
  setOpen,
  percentage,
  setPercentage,
  zeeId,
  hasExistingScore,
}: EditBrandScoreDialogProps) => {
  const handleCloseDialog = (): void => {
    setOpen(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) handleCloseDialog();
      }}
    >
      <DialogContent className="gap-0 p-0">
        <DialogHeader className="border-b border-border p-4">
          <DialogTitle>Brand match score</DialogTitle>
        </DialogHeader>
        <BrandScoreForm
          closeDialog={handleCloseDialog}
          percentage={percentage}
          setPercentage={setPercentage}
          zeeId={zeeId}
          hasExistingScore={hasExistingScore}
        />
      </DialogContent>
    </Dialog>
  );
};

export default EditBrandScoreDialog;
