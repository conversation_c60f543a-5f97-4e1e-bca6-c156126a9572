import { useOrganization } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import UploadFddFormSchema from '@/components/forms/schemas/UploadFddFormSchema';
import FileUploadPanel from '@/components/global/form-inputs/FileUploadPanel';
import FormInput from '@/components/global/form-inputs/FormInput';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { uploadToStorage } from '@/helpers/storage.helper';
import { useCreateFdd } from '@/hooks/fdd/useCreateFdd';
import { toast } from '@/hooks/ui/use-toast';
import logger from '@/services/logger.service';
import useFddStore from '@/stores/useFddStore';

interface UploadFDDDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const UploadFDDDialog = ({ open, setOpen }: UploadFDDDialogProps) => {
  const uploadForm = useForm<z.infer<typeof UploadFddFormSchema>>({
    resolver: zodResolver(UploadFddFormSchema),
  });

  const { setFdd } = useFddStore();
  const { organization } = useOrganization();

  const [isUploading, setIsUploading] = useState(false);

  const {
    mutate: createFdd,
    result: createFddResult,
    isError: isCreateFddError,
    isSuccess: isCreateFddSuccess,
    isLoading: isCreateFddLoading,
  } = useCreateFdd();

  const handleSubmit = async (
    values: z.infer<typeof UploadFddFormSchema>
  ): Promise<void> => {
    try {
      const uploadedDocument = await uploadToStorage({
        file: values.file,
        bucketName: 'fdds',
        folderName: organization?.id ?? '',
        setIsUploading,
      });

      if (!uploadedDocument) {
        return;
      }

      createFdd({
        file_name: values.file_name,
        file_url: uploadedDocument.file_url,
        file_size: uploadedDocument.file_size,
      });
    } catch (error) {
      logger.error('Failed to handle FDD upload submission', error as Error, {
        fileName: values.file_name,
      });

      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    if (isCreateFddError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isCreateFddError]);

  useEffect(() => {
    if (isCreateFddSuccess && createFddResult?.data) {
      setFdd(createFddResult.data);
      setOpen(false);
    }
  }, [createFddResult, isCreateFddSuccess, setFdd, setOpen]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="bg-white sm:max-w-[480px]"
        aria-describedby="undefined"
      >
        <DialogHeader>
          <DialogTitle className="font-medium">Upload FDD</DialogTitle>
        </DialogHeader>
        <Form {...uploadForm}>
          <form
            onSubmit={uploadForm.handleSubmit(handleSubmit)}
            className="w-full space-y-2"
          >
            <FileUploadPanel name="file" control={uploadForm.control} />
            <FormInput
              type="text"
              name="file_name"
              label="Title"
              control={uploadForm.control}
            />
            <Button
              disabled={isUploading || isCreateFddLoading}
              className="w-full"
              type="submit"
            >
              {(isUploading || isCreateFddLoading) && (
                <Loader2 className="size-4 animate-spin" />
              )}
              Upload
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default UploadFDDDialog;
