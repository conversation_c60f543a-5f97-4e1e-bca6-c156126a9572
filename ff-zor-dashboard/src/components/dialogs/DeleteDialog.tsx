import { Loader2 } from 'lucide-react';

import DeleteIcon from '@/assets/icons/delete.svg';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface DeleteDialogProps {
  open: boolean;
  label1?: string;
  label2?: string;
  isLoading?: boolean;
  setOpen: (open: boolean) => void;
  onCancel: () => void;
  onDelete: () => void;
}

const DeleteDialog = ({
  open,
  label1,
  label2,
  isLoading,
  setOpen,
  onDelete,
  onCancel,
}: DeleteDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[480px] bg-white">
        <DialogHeader>
          <DialogTitle>Delete Confirmation</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center gap-6 border-b border-t border-border px-5 py-6">
          <img
            src={DeleteIcon}
            alt="Delete Icon"
            className="h-[100px] w-[150px]"
          />
          <div className="as-title-02 as-strong w-80 text-center">
            Are you sure you want to delete the
            {label1 && <span className="text-primary"> {label1}</span>}
            {label2 && <span> {label2}</span>}
          </div>
        </div>

        <DialogFooter>
          <div className="flex w-full justify-between">
            <Button variant="outline" onClick={onCancel} type="button">
              Cancel
            </Button>
            <Button disabled={isLoading} onClick={onDelete} type="button">
              {isLoading && <Loader2 className="size-4 animate-spin" />}
              Delete
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteDialog;
