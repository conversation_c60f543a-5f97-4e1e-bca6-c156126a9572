import LeadershipUserForm from '@/components/forms/profile/LeadershipUserForm';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface AddLeadershipUserProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const AddLeadershipUser = ({ open, setOpen }: AddLeadershipUserProps) => {
  const handleCloseDialog = (): void => {
    setOpen(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) handleCloseDialog();
      }}
    >
      <DialogContent className="w-[480px] bg-white">
        <DialogHeader>
          <DialogTitle>Add New User</DialogTitle>
        </DialogHeader>
        <LeadershipUserForm closeDialog={handleCloseDialog} />
      </DialogContent>
    </Dialog>
  );
};

export default AddLeadershipUser;
