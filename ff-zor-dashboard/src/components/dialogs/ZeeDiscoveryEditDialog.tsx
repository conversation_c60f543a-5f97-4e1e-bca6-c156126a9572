import { <PERSON><PERSON><PERSON><PERSON>, Circle<PERSON>, Link2, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

interface ZeeDiscoveryEditDialogProps {
  open: boolean;
  label1?: string;
  label2?: string;
  linkeId?: string;
  isLoading?: boolean;
  onSave: () => void;
  setOpen: (open: boolean) => void;
  onCancel: () => void;
}

const ZeeDiscoveryEditDialog = ({
  open,
  isLoading,
  onSave,
  setOpen,
  onCancel,
}: ZeeDiscoveryEditDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="w-[480px] bg-white">
        <DialogHeader>
          <DialogTitle className="text-sm text-typo-gray-tertiary">
            Do you want to remove?
          </DialogTitle>
        </DialogHeader>
        <div>
          <p className="text-[12px] text-typo-gray-tertiary">
            You can still reassign someone
          </p>
        </div>
        <div className="relative flex items-center">
          <span className="absolute left-3 text-gray-500">
            <Link2 />
          </span>
          <Input
            type="text"
            placeholder="exampel.com/linkre"
            className="pl-10"
          />
        </div>

        <DialogFooter>
          <div className="flex w-full justify-between">
            <Button variant="outline" onClick={onCancel} type="button">
              <CircleX />
              Cancel
            </Button>
            <Button
              variant="outline"
              disabled={isLoading}
              onClick={onSave}
              type="button"
            >
              {isLoading && <Loader2 className="size-4 animate-spin" />}
              <CircleCheck />
              Save
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ZeeDiscoveryEditDialog;
