import InviteCandidateForm from '@/components/forms/profile/InviteCandidateForm';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface InviteCandidateDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

const InviteCandidateDialog = ({
  open,
  setOpen,
}: InviteCandidateDialogProps) => {
  const handleCloseDialog = (): void => {
    setOpen(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen) handleCloseDialog();
      }}
    >
      <DialogContent className="gap-0 p-0">
        <DialogHeader className="border-b border-border p-4">
          <DialogTitle>Invite Candidate</DialogTitle>
        </DialogHeader>
        <InviteCandidateForm closeDialog={handleCloseDialog} />
      </DialogContent>
    </Dialog>
  );
};

export default InviteCandidateDialog;
