import { Bot, Download, FileText, Paperclip } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Attachment, Note, NoteCategory } from '@/types/note.type';

interface NoteCardProps {
  note: Note;
  onEdit: (noteId: string, content: string) => void;
  onDelete: (noteId: string) => void;
}

const NoteCard = ({ note }: NoteCardProps) => {
  // TODO: Implement edit and delete functionality

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);

    // Format: "Sep 15, 2023 at 2:30 PM"
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    };

    return date.toLocaleDateString('en-US', options).replace(',', ' at');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = () => {
    // For now, just return a generic file icon
    // In the future, we could use the extension to show different icons
    return <FileText size={16} className="text-gray-500" />;
  };

  const handleDownload = (attachment: Attachment) => {
    // Create a temporary link element and trigger download
    const link = document.createElement('a');
    link.href = attachment.file_url;
    link.download = attachment.file_name;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getCategoryBadge = (category: NoteCategory, automated?: boolean) => {
    const categoryStyles = {
      calls: 'bg-blue-100 text-blue-800 border-blue-200',
      meeting: 'bg-green-100 text-green-800 border-green-200',
      'manual-email': 'bg-green-100 text-green-800 border-green-200',
      'system-emails': 'bg-gray-100 text-gray-800 border-gray-200',
      firefiles: 'bg-orange-100 text-orange-800 border-orange-200',
      general: 'bg-slate-100 text-slate-800 border-slate-200',
    };

    const categoryDisplayNames = {
      calls: 'Call',
      meeting: 'Meeting',
      'manual-email': 'Manual Email',
      'system-emails': 'System Email',
      firefiles: 'Firefiles',
      general: 'General',
    };

    return (
      <span
        className={`inline-flex items-center rounded-full border px-3 py-1 text-sm font-medium ${categoryStyles[category]}`}
      >
        {automated && <span className="mr-1">🤖</span>}
        {category === 'calls' && '📞'}
        {category === 'meeting' && '🤝'}
        {category === 'manual-email' && '✉️'}
        {category === 'system-emails' && '📧'}
        {category === 'firefiles' && '🔥'}
        {category === 'general' && '📝'}
        <span className="ml-1">{categoryDisplayNames[category]}</span>
      </span>
    );
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((part) => part.charAt(0))
      .join('')
      .toUpperCase();
  };

  return (
    <div className="group rounded-xl border border-border bg-white p-6">
      {/* Header with date, category, and user info */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-500">
            {formatDateTime(note.created_at)}
          </span>
          {getCategoryBadge(note.category, note.is_automated)}
        </div>

        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={note.is_automated ? undefined : note.author_avatar}
              alt={note.is_automated ? 'System' : note.author_name}
            />
            <AvatarFallback className="bg-gray-100 text-xs text-gray-600">
              {note.is_automated ? (
                <Bot size={16} />
              ) : note.author_name ? (
                getInitials(note.author_name)
              ) : (
                'U'
              )}
            </AvatarFallback>
          </Avatar>
          <div className="text-left">
            <p className="text-sm font-medium text-gray-900">
              {note.is_automated
                ? 'System'
                : note.author_name || 'Unknown User'}
            </p>
            <span className="inline-flex items-center rounded-full border border-gray-200 bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800">
              {note.is_automated ? 'Automated' : note.author_role || 'User'}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mt-3">
        <p className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700">
          {note.content}
        </p>
      </div>

      {/* Attachments Section */}
      {note.attachments && note.attachments.length > 0 && (
        <div className="mt-6 border-t border-gray-100 pt-4">
          <div className="mb-3 flex items-center gap-2">
            <Paperclip size={16} className="text-gray-500" />
            <h4 className="text-sm font-medium text-gray-900">
              Attachments ({note.attachments.length}):
            </h4>
          </div>
          <div className="space-y-2">
            {note.attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between rounded-lg bg-gray-50 p-3 transition-colors hover:bg-gray-100"
              >
                <div className="flex items-center gap-3">
                  {getFileIcon()}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {attachment.file_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      ({formatFileSize(attachment.file_size)})
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => handleDownload(attachment)}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
                  title={`Download ${attachment.file_name}`}
                >
                  <Download size={16} />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional info footer */}
      {note.updated_at !== note.created_at && (
        <div className="mt-3 border-t border-gray-100 pt-3">
          <p className="text-xs text-gray-400">
            Updated: {formatDateTime(note.updated_at)}
          </p>
        </div>
      )}
    </div>
  );
};

export default NoteCard;
