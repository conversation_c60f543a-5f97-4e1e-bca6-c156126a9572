interface StateStatusBadgeProps {
  count: number;
  label:
    | 'territories'
    | 'sold'
    | 'reserved'
    | 'pending'
    | 'territories available';
}

const StateStatusBadge = ({ count, label }: StateStatusBadgeProps) => {
  return (
    <div className="flex items-center gap-1 rounded-md border border-border bg-white px-3.5 py-1">
      <p className="as-strong text-gray-700">{count}</p>
      <p className="as-body-03 text-muted-foreground">
        {label
          .split(' ')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')}
      </p>
    </div>
  );
};

export default StateStatusBadge;
