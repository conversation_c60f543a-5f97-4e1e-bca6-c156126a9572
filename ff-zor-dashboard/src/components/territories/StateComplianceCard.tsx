import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { US_STATES } from '@/constants/territories/states';
import { useSetStateCompliance } from '@/hooks/territories/states-compliance/useSetStateCompliance';
import { toast } from '@/hooks/ui/use-toast';
import useTerritoryStore from '@/stores/useTerritoryStore';

interface StateComplianceCardProps {
  state: string;
  status: {
    compliant: boolean;
    active: boolean;
  };
}

const StateComplianceCard = ({ state, status }: StateComplianceCardProps) => {
  const { statesCompliance, setStatesCompliance } = useTerritoryStore();

  const {
    mutate: setStateCompliance,
    result: setStateComplianceResult,
    isError: isSetStateComplianceError,
    isLoading: isSetStateComplianceLoading,
    isSuccess: isSetStateComplianceSuccess,
  } = useSetStateCompliance();

  useEffect(() => {
    if (isSetStateComplianceError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isSetStateComplianceError]);

  useEffect(() => {
    if (isSetStateComplianceSuccess) {
      const newStatesCompliance = {
        ...statesCompliance,
        [state]: {
          active: setStateComplianceResult?.data.is_active || false,
          compliant: setStateComplianceResult?.data.is_compliant || false,
        },
      };
      setStatesCompliance(newStatesCompliance);
    }
  }, [setStateComplianceResult, isSetStateComplianceSuccess]);

  return (
    <div className="space-y-4 rounded-lg border border-border bg-white p-2">
      <div className="flex items-center justify-between">
        <p className="as-title-03 as-strong">
          {US_STATES[state as keyof typeof US_STATES]}
        </p>
        {isSetStateComplianceLoading && (
          <Loader2 className="size-4 animate-spin" />
        )}
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Checkbox
            id={`${state}-compliant`}
            checked={status.compliant}
            onCheckedChange={(checked) => {
              setStateCompliance({
                state,
                active: checked == false ? false : status.active,
                compliant: checked === true,
              });
            }}
          />
          <Label htmlFor={`${state}-compliant`}>Compliant</Label>
        </div>
        <div className="flex items-center gap-2">
          <Switch
            id={`${state}-active`}
            checked={status.active}
            disabled={!status.compliant}
            onCheckedChange={(checked) => {
              setStateCompliance({
                state,
                active: checked,
                compliant: status.compliant,
              });
            }}
          />
          <Label htmlFor={`${state}-active`}>Active</Label>
        </div>
      </div>
    </div>
  );
};

export default StateComplianceCard;
