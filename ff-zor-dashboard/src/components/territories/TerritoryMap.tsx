import { GoogleMap, useLoadScript } from '@react-google-maps/api';
import { useEffect, useRef } from 'react';

import { getTerritoryColor } from '@/lib/territories';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { Territory } from '@/types/territories/territory.type';

interface TerritoryMapProps {
  territory: Territory;
  onMapLoad?: (map: google.maps.Map) => void;
  onRenderComplete?: () => void;
}

const TerritoryMap = ({
  territory,
  onMapLoad,
  onRenderComplete,
}: TerritoryMapProps) => {
  const mapRef = useRef<google.maps.Map | null>(null);
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: import.meta.env.VITE_PUBLIC_GOOGLE_MAPS_API_KEY,
  });
  const { territorySettings } = useTerritoryStore();
  const territoryColor = getTerritoryColor(territory.status, territorySettings);

  useEffect(() => {
    if (!isLoaded || !mapRef.current || !territory.geom) return;

    const map = mapRef.current;
    const bounds = new google.maps.LatLngBounds();

    // Create polygon for the territory
    const polygon = new google.maps.Polygon({
      paths:
        territory.geom.type === 'Polygon'
          ? territory.geom.coordinates[0].map((coord: number[]) => ({
              lat: coord[1],
              lng: coord[0],
            }))
          : territory.geom.coordinates[0][0].map((coord: number[]) => ({
              lat: coord[1],
              lng: coord[0],
            })),
      strokeColor: territoryColor,
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: territoryColor,
      fillOpacity: 0.35,
    });

    polygon.setMap(map);

    // Fit map to territory bounds
    polygon.getPath().forEach((latLng) => {
      bounds.extend(latLng);
    });
    map.fitBounds(bounds);

    // Add some padding to the bounds
    const listener = google.maps.event.addListenerOnce(map, 'idle', () => {
      const currentBounds = map.getBounds();
      if (currentBounds) {
        const ne = currentBounds.getNorthEast();
        const sw = currentBounds.getSouthWest();
        const padding = {
          lat: (ne.lat() - sw.lat()) * 0.1,
          lng: (ne.lng() - sw.lng()) * 0.1,
        };
        map.fitBounds(
          new google.maps.LatLngBounds(
            { lat: sw.lat() - padding.lat, lng: sw.lng() - padding.lng },
            { lat: ne.lat() + padding.lat, lng: ne.lng() + padding.lng }
          )
        );
      }
      onRenderComplete?.();
    });

    return () => {
      google.maps.event.removeListener(listener);
      polygon.setMap(null);
    };
  }, [isLoaded, territory, onRenderComplete]);

  if (!isLoaded) return null;

  return (
    <GoogleMap
      mapContainerStyle={{ width: '100%', height: '400px' }}
      options={{
        disableDefaultUI: true,
        zoomControl: false,
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: false,
      }}
      onLoad={(map) => {
        mapRef.current = map;
        onMapLoad?.(map);
      }}
    />
  );
};

export default TerritoryMap;
