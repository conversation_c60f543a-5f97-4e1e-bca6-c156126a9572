import { Loader2, MapPin } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { US_STATES } from '@/constants/territories/states';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';

import StateStatusBadge from './StateStatusBadge';
import TerritoryItem from './territory-item';

interface TerritoriesByStateProps {
  isEditTab?: boolean;
  loadingTerritories: boolean;
}

const TerritoriesByState = ({
  isEditTab = false,
  loadingTerritories,
}: TerritoriesByStateProps) => {
  const [expandedStates, setExpandedStates] = useState<Record<string, boolean>>(
    {}
  );

  const { territories } = useTerritoryStore();

  if (!territories) return null;

  const territoriesByState = territories.reduce(
    (acc, territory) => {
      const state = territory.state;
      if (!acc[state]) {
        acc[state] = [];
      }
      acc[state].push(territory);
      return acc;
    },
    {} as Record<string, typeof territories>
  );

  const stateStats = Object.entries(territoriesByState).map(
    ([state, stateTerritories]) => {
      const total = stateTerritories.length;
      const sold = stateTerritories.filter((t) => t.status === 'SOLD').length;
      const reserved = stateTerritories.filter(
        (t) => t.status === 'RESERVED'
      ).length;
      const pending = stateTerritories.filter(
        (t) => t.status === 'PENDING'
      ).length;
      const available = stateTerritories.filter(
        (t) => t.status === 'AVAILABLE'
      ).length;

      return {
        state,
        total,
        sold,
        pending,
        reserved,
        available,
      };
    }
  );

  const toggleState = (state: string) => {
    setExpandedStates((prev) => ({
      ...prev,
      [state]: !prev[state],
    }));
  };

  const handleToggleStateClick = (state: string) => (): void => {
    toggleState(state);
  };

  return (
    <div className="space-y-4">
      {loadingTerritories ? (
        <div className="flex w-full justify-center py-5">
          <Loader2 className="size-4 animate-spin" />
        </div>
      ) : (
        stateStats.map(({ state, total, sold, reserved, pending }) => (
          <div
            key={state}
            className="overflow-hidden rounded-xl border border-border"
          >
            <div
              className={cn(
                'flex items-center justify-between bg-zinc-50 px-6 py-3.5',
                expandedStates[state] && 'bg-brand-50'
              )}
            >
              <div className="flex items-center gap-2">
                <MapPin
                  className={cn(
                    'h-5 w-5 text-zinc-400',
                    expandedStates[state] && 'text-brand-500'
                  )}
                />
                <p className="as-title-01 as-strong text-gray-700">
                  {US_STATES[state as keyof typeof US_STATES]}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <StateStatusBadge count={total} label="territories" />
                <StateStatusBadge count={sold} label="sold" />
                <StateStatusBadge count={reserved} label="reserved" />
                <StateStatusBadge count={pending} label="pending" />
                <Button type="button" onClick={handleToggleStateClick(state)}>
                  {expandedStates[state] ? 'Hide' : 'View'}
                </Button>
              </div>
            </div>
            {expandedStates[state] && (
              <>
                {territoriesByState[state].map((territory) => (
                  <TerritoryItem
                    key={territory.id}
                    territory={territory}
                    isEditTab={isEditTab}
                  />
                ))}
              </>
            )}
          </div>
        ))
      )}
    </div>
  );
};

export default TerritoriesByState;
