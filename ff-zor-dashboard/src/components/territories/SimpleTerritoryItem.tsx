import { ChevronDown, ChevronUp, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { US_STATES } from '@/constants/territories/states';
import { DEFAULT_CENSUS_DATA_FIELDS } from '@/constants/territories/variables';
import { apiManager } from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { formatDecimal, formatValue } from '@/lib/string';
import { getTerritoryColor, isDecimal } from '@/lib/territories';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { CensusFieldKey } from '@/types/territories/census.field.type';
import { Territory } from '@/types/territories/territory.type';

import { Badge } from '../ui/badge';

interface SimpleTerritoryItemProps {
  territory: Territory;
}

const SimpleTerritoryItem = ({ territory }: SimpleTerritoryItemProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [demographic, setDemographic] = useState<Territory | null>(null);
  const [loading, setLoading] = useState(false);
  const { territorySettings } = useTerritoryStore();

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  useEffect(() => {
    if (isExpanded && !demographic && !loading) {
      const fetchDemographic = async () => {
        setLoading(true);
        try {
          const response = await apiManager.get(
            `${TERRITORIES_API_URL}/${territory.id}`,
            {
              baseURL: configuration.TERRITORIES_API_URL,
              headers: { 'Content-Type': 'application/json' },
            }
          );

          if (response.status !== 200) {
            toast({
              title: 'Error loading demographic data',
              variant: 'destructive',
            });
          } else {
            setDemographic(response.data.data);
          }
        } catch (error) {
          console.error('Error loading demographic data:', error);
          toast({
            title: 'Error loading demographic data',
            variant: 'destructive',
          });
        } finally {
          setLoading(false);
        }
      };

      fetchDemographic();
    }
  }, [isExpanded, demographic, loading, territory.id]);

  return (
    <div>
      <div className="flex items-center justify-between gap-6 border-t border-border px-6 py-3 max-md:flex-col max-md:items-start max-md:gap-3 max-md:p-3">
        <div className="flex flex-wrap items-center gap-2 truncate">
          <p className="as-body-02 as-strong text-gray-700">
            Territory {territory.id.toString().padStart(5, '0')}
          </p>
          <p className="as-body-03 text-muted-foreground">|</p>
          <p className="as-body-02 as-strong text-gray-700">
            Zip: {territory.zcta_codes.join(', ')}
          </p>
          <p className="as-body-03 text-muted-foreground">|</p>
          <p className="as-body-02 as-strong text-gray-700">
            {US_STATES[territory.state as keyof typeof US_STATES] ||
              territory.state}
          </p>
        </div>
        <div className="flex items-center gap-2 max-md:w-full max-md:justify-between">
          <Badge color={getTerritoryColor(territory.status, territorySettings)}>
            {territory.status}
          </Badge>
          <Button size="icon" variant="outline" onClick={handleToggleExpanded}>
            {isExpanded ? <ChevronUp /> : <ChevronDown />}
          </Button>
        </div>
      </div>
      {isExpanded && (
        <div className="bg-muted p-4">
          <div className="rounded-lg border border-border bg-white p-4">
            {loading ? (
              <div className="flex w-full items-center justify-center">
                <Loader2 className="size-4 animate-spin" />
              </div>
            ) : (
              demographic && (
                <div className="flex flex-wrap gap-4">
                  {DEFAULT_CENSUS_DATA_FIELDS.map(({ id, label }) => (
                    <div key={id} className="flex w-60 flex-col gap-1">
                      <p className="as-title-04 text-muted-foreground">
                        {label}
                      </p>
                      <p className="as-title-03 as-strong text-gray-700">
                        {id.includes('age') && id.includes('median')
                          ? formatDecimal(demographic[id as CensusFieldKey])
                          : formatValue(demographic[id as CensusFieldKey])}
                      </p>
                    </div>
                  ))}
                  {demographic.extra_census_data &&
                    demographic.extra_census_data.length > 0 && (
                      <>
                        {demographic.extra_census_data.map(
                          (item, index) =>
                            item.label && (
                              <div
                                key={index}
                                className="flex w-60 flex-col gap-1"
                              >
                                <p className="as-title-04 text-muted-foreground">
                                  {item.label}
                                </p>
                                <p className="as-title-03 as-strong text-gray-700">
                                  {isDecimal(item.code)
                                    ? formatDecimal(item.value)
                                    : formatValue(item.value)}
                                </p>
                              </div>
                            )
                        )}
                      </>
                    )}
                </div>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleTerritoryItem;
