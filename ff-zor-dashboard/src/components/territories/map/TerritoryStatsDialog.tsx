import { Loader2, User<PERSON><PERSON>, X } from 'lucide-react';
import { useEffect, useState } from 'react';

import { TerritoryStatusBadge } from '@/components/territories/territory-item/TerritoryStatusBadge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { US_STATES } from '@/constants/territories/states';
import { DEFAULT_CENSUS_DATA_FIELDS } from '@/constants/territories/variables';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { formatDecimal, formatValue } from '@/lib/string';
import { isDecimal } from '@/lib/territories';
import { getTerritoryColor } from '@/lib/territories';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { CensusFieldKey } from '@/types/territories/census.field.type';
import { Territory } from '@/types/territories/territory.type';

interface TerritoryStatsDialogProps {
  territory: Territory;
  onCloseDialog: () => void;
}

const TerritoryStatsDialog = ({
  territory,
  onCloseDialog,
}: TerritoryStatsDialogProps) => {
  const { territorySettings } = useTerritoryStore();
  const [demographic, setDemographic] = useState<Territory | null>(null);
  const [loading, setLoading] = useState(true);
  const assignedUser =
    territory.status == 'PENDING'
      ? territory.requested_by
      : territory.assigned_to;

  useEffect(() => {
    const fetchDemographic = async () => {
      const response = await apiManager.get(
        `${TERRITORIES_API_URL}/${territory.id}`,
        {
          baseURL: configuration.TERRITORIES_API_URL,
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (response.status === 200) {
        setDemographic(response.data.data);
      } else {
        toast({
          title: 'Error loading data',
          description: 'Failed to load demographic information.',
          variant: 'destructive',
        });
      }

      setLoading(false);
    };

    fetchDemographic();
  }, [territory.id]);
  return (
    <div className="absolute left-1/2 top-1/2 z-50 flex max-h-[calc(100%-40px)] max-w-md -translate-x-1/2 -translate-y-1/2 flex-col gap-4 rounded-lg border border-border bg-white p-4 shadow-lg">
      <div className="flex items-center justify-between gap-5">
        <div className="overflow-hidden">
          <p className="as-title-01 truncate text-gray-700">{`Territory ${territory.id.toString().padStart(5, '0')} - Zip ${territory.zcta_codes.join(',')}`}</p>
          <p className="as-title-01 text-gray-700">
            {US_STATES[territory.state as keyof typeof US_STATES]}
          </p>
        </div>
        <Button
          size="icon"
          variant="outline"
          className="aspect-square"
          onClick={onCloseDialog}
        >
          <X />
        </Button>
      </div>
      <div className="flex gap-8">
        <div className="space-y-1">
          <p className="as-title-04 text-muted-foreground">Status</p>
          <Badge color={getTerritoryColor(territory.status, territorySettings)}>
            {territory.status}
          </Badge>
        </div>
        <div className="space-y-1">
          <p className="as-title-04 text-muted-foreground">Assigned to</p>
          {assignedUser && territory.status != 'AVAILABLE' ? (
            <div className="flex items-center gap-2">
              <Avatar className="h-5 w-5">
                <AvatarImage src={assignedUser.photo_url} />
                <AvatarFallback>
                  <UserIcon className="h-3 w-3" />
                </AvatarFallback>
              </Avatar>
              <p className="as-title-04 text-gray-700">
                {assignedUser.full_name}
              </p>
            </div>
          ) : (
            <p className="as-title-04 text-gray-700">N/A</p>
          )}
        </div>
      </div>
      <div
        className={`overflow-y-auto transition-all duration-500 ease-in-out ${
          loading ? 'max-h-20' : 'max-h-[1000px]'
        }`}
      >
        {loading ? (
          <div className="flex justify-center gap-3 rounded-lg border border-border bg-muted p-4">
            <Loader2 className="size-4 animate-spin text-muted-foreground" />
          </div>
        ) : (
          demographic && (
            <div className="flex flex-col gap-3 rounded-lg border border-border bg-muted p-4">
              {DEFAULT_CENSUS_DATA_FIELDS.map(({ id, label }) => (
                <div
                  key={id}
                  className="flex items-center justify-between gap-5"
                >
                  <p className="as-title-04 text-muted-foreground">{label}</p>
                  <p className="as-title-04 text-gray-700">
                    {id.includes('age') && id.includes('median')
                      ? formatDecimal(demographic?.[id as CensusFieldKey])
                      : formatValue(demographic?.[id as CensusFieldKey])}
                  </p>
                </div>
              ))}
              {demographic.extra_census_data &&
                demographic.extra_census_data.length > 0 &&
                demographic.extra_census_data.map(
                  (item, index) =>
                    item.label && (
                      <div
                        key={index}
                        className="flex items-center justify-between gap-5"
                      >
                        <p className="as-title-04 text-muted-foreground">
                          {item.label}
                        </p>
                        <p className="as-title-04 text-gray-700">
                          {isDecimal(item.code)
                            ? formatDecimal(item.value)
                            : formatValue(item.value)}
                        </p>
                      </div>
                    )
                )}
            </div>
          )
        )}
      </div>

      <div className="w-fit">
        <TerritoryStatusBadge
          count={territory.likes_count}
          label="LIKED"
          showIcon
        />
      </div>
    </div>
  );
};

export default TerritoryStatsDialog;
