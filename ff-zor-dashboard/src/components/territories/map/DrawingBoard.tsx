import { Polygon } from '@react-google-maps/api';
import * as turf from '@turf/turf';
import {
  Feature,
  GeoJsonProperties,
  MultiPolygon as GeoMultiPolygon,
  Polygon as GeoPolygon,
} from 'geojson';
import { Combine, Pencil, RotateCcw, Save, X } from 'lucide-react';
import { Fragment, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { TERRITORIES_API_URL, ZIP_CODES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { POLYGON_COLORS } from '@/constants/territories/colors';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { AggregatedStats, calculateAggregatedStats } from '@/lib/territories';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { AreaInfo } from '@/types/territories/area.info.type';
import { Territory } from '@/types/territories/territory.type';

import AreaInfoCard from './AreaInfoCard';

interface DrawingBoardProps {
  zoom: number;
  bounds: {
    ne: {
      lat: number;
      lng: number;
    };
    sw: {
      lat: number;
      lng: number;
    };
  };
}

const DrawingBoard = ({ zoom, bounds }: DrawingBoardProps) => {
  const { mapTerritories, setMapTerritories } = useTerritoryStore();

  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const [hoveredArea, setHoveredArea] = useState<AreaInfo | null>(null);
  const [mergedPolygons, setMergedPolygons] = useState<
    Feature<GeoPolygon | GeoMultiPolygon, GeoJsonProperties>[]
  >([]);
  const [selectedZipCodes, setSelectedZipCodes] = useState<AreaInfo[]>([]);
  const [availableZipCodes, setAvailableZipCodes] = useState<AreaInfo[]>([]);
  const [neighbouringZipCodes, setNeighbouringZipCodes] = useState<AreaInfo[]>(
    []
  );
  const [hoveredMergedArea, setHoveredMergedArea] = useState<number | null>(
    null
  );
  const [mergedPolygonStats, setMergedPolygonStats] = useState<
    AggregatedStats[]
  >([]);

  const mergeAreas = (array1: AreaInfo[], array2: AreaInfo[]): AreaInfo[] => {
    // Step 1: Combine the arrays
    const merged = [...array1, ...array2];

    // Step 2: Remove duplicates based on zcta_code using a Set
    const seen = new Set();
    const uniqueMerged = merged.filter((item) => {
      if (seen.has(item.zcta_code)) {
        return false; // Skip duplicate
      } else {
        seen.add(item.zcta_code); // Add to the set
        return true; // Keep the first occurrence
      }
    });

    return uniqueMerged;
  };

  const removeAreas = (array1: AreaInfo[], array2: AreaInfo[]): AreaInfo[] => {
    const filtered = array1.filter(
      (item1) => !array2.some((item2) => item1.zcta_code === item2.zcta_code)
    );
    return filtered;
  };

  const fetchAvailableZipCodes = async () => {
    const response = await apiManager.get(ZIP_CODES_API_URL, {
      baseURL: configuration.TERRITORIES_API_URL,
      headers: { 'Content-Type': 'application/json' },
      params: {
        minLat: bounds.sw.lat,
        maxLat: bounds.ne.lat,
        minLng: bounds.sw.lng,
        maxLng: bounds.ne.lng,
      },
    });

    if (response.status !== 200) {
      return;
    }

    const originalAreas: AreaInfo[] = response.data.data;

    // 1. Count how many times each state appears
    const stateFrequency = originalAreas.reduce(
      (acc, area) => {
        acc[area.state] = (acc[area.state] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    // 2. Group AreaInfo by zcta_code
    const groupedAreasByZcta = originalAreas.reduce(
      (acc, area) => {
        if (!acc[area.zcta_code]) acc[area.zcta_code] = [];
        acc[area.zcta_code].push(area);
        return acc;
      },
      {} as Record<string, AreaInfo[]>
    );

    // 3. For each group, keep the AreaInfo with the most common state
    const uniqueAreasByZcta: AreaInfo[] = Object.values(groupedAreasByZcta).map(
      (group) =>
        group.reduce((a, b) =>
          stateFrequency[a.state] >= stateFrequency[b.state] ? a : b
        )
    );
    setAvailableZipCodes(uniqueAreasByZcta);
  };

  const updateNeighbouringZipCodes = async (
    zipCodes: AreaInfo[],
    selectedZipCode: AreaInfo
  ) => {
    const newZipParam =
      zipCodes?.map((area: AreaInfo) => area.zcta_code).join(',') ?? '';
    const response = await apiManager.get(ZIP_CODES_API_URL, {
      baseURL: configuration.TERRITORIES_API_URL,
      headers: { 'Content-Type': 'application/json' },
      params: {
        selectedZipCodes: newZipParam,
      },
    });

    if (response.status !== 200) {
      return;
    }
    const filteredAreas: AreaInfo[] = response.data.data.filter(
      (area: AreaInfo) => selectedZipCode.state == area.state
    );

    const newNeighbouringZipCodes = removeAreas(
      mergeAreas(filteredAreas, neighbouringZipCodes),
      availableZipCodes
    );
    setNeighbouringZipCodes(newNeighbouringZipCodes);
  };

  const handleZipCodeClick = (areaInfo: AreaInfo) => {
    const isAlreadySelected = selectedZipCodes.some(
      (zip) => zip.zcta_code === areaInfo.zcta_code
    );

    if (isAlreadySelected) {
      setSelectedZipCodes((prev) =>
        prev.filter((zip) => zip.zcta_code !== areaInfo.zcta_code)
      );
      return;
    }
    setSelectedZipCodes((prev) => [...prev, areaInfo]);
    updateNeighbouringZipCodes([...selectedZipCodes, areaInfo], areaInfo);
  };

  const handleZipCodeClickWrapper = (areaInfo: AreaInfo) => () => {
    handleZipCodeClick(areaInfo);
  };

  const handleMouseOut = () => {
    setHoveredArea(null);
  };

  const handleMouseOver = (areaInfo: AreaInfo) => () => {
    setHoveredArea(areaInfo);
  };

  const handleStartDrawing = () => {
    setIsDrawing(true);
  };

  const handleExitDrawing = () => {
    setIsDrawing(false);
    handleReset();
  };

  const handleMerge = () => {
    if (selectedZipCodes.length < 2) {
      return;
    }

    // Check if all selected zip codes have the same state
    const firstState = selectedZipCodes[0].state;
    const allSameState = selectedZipCodes.every(
      (zip) => zip.state === firstState
    );

    if (!allSameState) {
      toast({
        title: 'Invalid Selection',
        description: 'All selected zip codes must be from the same state',
        variant: 'destructive',
      });
      return;
    }

    // Convert selected zip codes to GeoJSON features
    const features = turf.featureCollection(
      selectedZipCodes.flatMap((areaInfo) => {
        return areaInfo.geom.type === 'MultiPolygon'
          ? areaInfo.geom.coordinates.map((coordinates) =>
              turf.polygon([coordinates[0]])
            )
          : turf.polygon([areaInfo.geom.coordinates[0]]);
      })
    );

    // Merge all features
    const merged = turf.union(features);

    // Convert merged GeoJSON back to Google Maps coordinates
    if (merged) {
      setMergedPolygons([...mergedPolygons, merged]);
      setMergedPolygonStats([
        ...mergedPolygonStats,
        calculateAggregatedStats(selectedZipCodes),
      ]);
      setSelectedZipCodes([]);
    }
  };

  const handleReset = () => {
    setSelectedZipCodes([]);
    setMergedPolygons([]);
    setMergedPolygonStats([]);
    setNeighbouringZipCodes([]);
    setHoveredMergedArea(null);
  };

  const handleSave = async () => {
    setIsSaving(true);
    const payload = {
      territories: mergedPolygonStats.map((stats, index) => ({
        state: stats.state,
        zcta_codes: stats.zctaCodes,
        geom: mergedPolygons[index].geometry,
        population: stats.population,
        birth_rate: stats.birth_rate,
        median_age: stats.median_age,
        median_household_income: stats.median_household_income,
        median_home_value: stats.median_home_value,
        pop_over_40: stats.pop_over_40,
      })),
    };

    const response = await apiManager.post(TERRITORIES_API_URL, payload, {
      baseURL: configuration.TERRITORIES_API_URL,
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status !== 200) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    } else {
      const createdTerritories = response.data.data;
      const updatedTerritories = createdTerritories.map(
        (territory: Territory) => {
          const extraCensusData =
            mergedPolygonStats.find(
              (item2) => (item2.zctaCodes = territory.zcta_codes)
            )?.extraCensusData || [];

          const center = turf.centroid(territory.geom);
          const [lng, lat] = center.geometry.coordinates;
          return {
            ...territory,
            centroid: {
              type: 'Point',
              coordinates: [lng, lat],
            },
            extra_census_data: extraCensusData,
          };
        }
      );

      setMapTerritories([...(mapTerritories ?? []), ...updatedTerritories]);
    }

    setIsSaving(false);
    handleReset();
  };

  useEffect(() => {
    if (zoom > 8 && isDrawing) {
      fetchAvailableZipCodes();
    } else {
      setAvailableZipCodes([]);
      setNeighbouringZipCodes([]);
    }
  }, [zoom, bounds, isDrawing]);

  const renderAvailableZipCode = (areaInfo: AreaInfo) => {
    let multiPolygonCoordinates: number[][][] = [];

    if (areaInfo.geom.type === 'MultiPolygon') {
      multiPolygonCoordinates = areaInfo.geom.coordinates.map(
        (coordinate) => coordinate[0]
      );
    } else {
      multiPolygonCoordinates = [areaInfo.geom.coordinates[0]];
    }

    return (
      <Fragment key={areaInfo.zcta_code}>
        {multiPolygonCoordinates.map((coordinates, index) => {
          return (
            <Polygon
              key={`${areaInfo.zcta_code}-${index}`}
              paths={coordinates.map(([lng, lat]) => ({
                lat,
                lng,
              }))}
              options={{
                zIndex: 10,
                fillColor: selectedZipCodes.some(
                  (zip) => zip.zcta_code === areaInfo.zcta_code
                )
                  ? POLYGON_COLORS.SELECTED
                  : hoveredArea?.zcta_code === areaInfo.zcta_code
                    ? POLYGON_COLORS.HOVERED
                    : POLYGON_COLORS.DEFAULT,
                fillOpacity: 0.5,
                strokeWeight: 1,
                strokeOpacity: 0.8,
              }}
              onClick={handleZipCodeClickWrapper(areaInfo)}
              onMouseOut={handleMouseOut}
              onMouseOver={handleMouseOver(areaInfo)}
            />
          );
        })}
      </Fragment>
    );
  };
  const renderNeighbouringZipCode = (areaInfo: AreaInfo) => {
    let multiPolygonCoordinates: number[][][] = [];

    if (areaInfo.geom.type === 'MultiPolygon') {
      multiPolygonCoordinates = areaInfo.geom.coordinates.map(
        (coordinate) => coordinate[0]
      );
    } else {
      multiPolygonCoordinates = [areaInfo.geom.coordinates[0]];
    }

    return (
      <Fragment key={areaInfo.zcta_code}>
        {multiPolygonCoordinates.map((coordinates, index) => {
          return (
            <Polygon
              key={`${areaInfo.zcta_code}-${index}`}
              paths={coordinates.map(([lng, lat]) => ({
                lat,
                lng,
              }))}
              options={{
                zIndex: 10,
                fillColor: selectedZipCodes.some(
                  (zip) => zip.zcta_code === areaInfo.zcta_code
                )
                  ? POLYGON_COLORS.SELECTED
                  : hoveredArea?.zcta_code === areaInfo.zcta_code
                    ? POLYGON_COLORS.HOVERED
                    : POLYGON_COLORS.DEFAULT,
                fillOpacity: 0.5,
                strokeWeight: 1,
                strokeOpacity: 0.8,
              }}
              onClick={() => handleZipCodeClick(areaInfo)}
              onMouseOut={() => setHoveredArea(null)}
              onMouseOver={() => setHoveredArea(areaInfo)}
            />
          );
        })}
      </Fragment>
    );
  };

  const renderMergedPolygon = (
    polygon: Feature<GeoPolygon | GeoMultiPolygon>,
    index: number
  ) => {
    let multiPolygonCoordinates: number[][][] = [];

    if (polygon.geometry.type === 'MultiPolygon') {
      multiPolygonCoordinates = polygon.geometry.coordinates.map(
        (coordinates) => coordinates[0]
      );
    } else {
      multiPolygonCoordinates = [polygon.geometry.coordinates[0]];
    }

    return (
      <Fragment key={`merged-polygon-${index}`}>
        {multiPolygonCoordinates.map((coordinates, idx) => {
          return (
            <Polygon
              key={`${polygon.geometry.type}-${idx}`}
              paths={coordinates.map(([lng, lat]) => ({
                lat,
                lng,
              }))}
              options={{
                zIndex: 20,
                fillColor: POLYGON_COLORS.MERGED,
                fillOpacity: 0.7,
                strokeWeight: 2,
                strokeColor: POLYGON_COLORS.MERGED,
                strokeOpacity: 1,
              }}
              onMouseOut={() => setHoveredMergedArea(null)}
              onMouseOver={() => setHoveredMergedArea(index)}
            />
          );
        })}
      </Fragment>
    );
  };

  return (
    <>
      {isDrawing &&
        availableZipCodes.map((areaInfo) => renderAvailableZipCode(areaInfo))}
      {mergedPolygons.map((polygon, index) =>
        renderMergedPolygon(polygon, index)
      )}
      <div className="absolute bottom-4 left-0 right-0 z-10 flex justify-center gap-2">
        {!isDrawing ? (
          <Button size="sm" onClick={handleStartDrawing}>
            <Pencil className="h-4 w-4" />
            Start Drawing
          </Button>
        ) : (
          <>
            <Button size="sm" variant="outline" onClick={handleExitDrawing}>
              <X className="h-4 w-4" />
              Exit
            </Button>
            <Button
              size="sm"
              variant="outline"
              disabled={selectedZipCodes.length < 2}
              onClick={handleMerge}
            >
              <Combine className="h-4 w-4" />
              Merge
            </Button>
            <Button
              size="sm"
              variant="outline"
              disabled={isSaving || mergedPolygons.length === 0}
              onClick={handleSave}
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button size="sm" variant="outline" onClick={handleReset}>
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>
          </>
        )}
      </div>
      {hoveredArea &&
        selectedZipCodes.length === 0 &&
        hoveredMergedArea === null && (
          <div className="absolute left-1/2 top-4 -translate-x-1/2">
            <AreaInfoCard areaInfos={[hoveredArea]} />
          </div>
        )}
      {selectedZipCodes.length > 0 && hoveredMergedArea === null && (
        <div className="absolute left-1/2 top-4 -translate-x-1/2">
          <AreaInfoCard areaInfos={selectedZipCodes} />
        </div>
      )}
      {hoveredMergedArea !== null && (
        <div className="absolute left-1/2 top-4 -translate-x-1/2">
          <AreaInfoCard stats={mergedPolygonStats[hoveredMergedArea]} />
        </div>
      )}
      {neighbouringZipCodes.length > 0 &&
        neighbouringZipCodes.map((areaInfo) =>
          renderNeighbouringZipCode(areaInfo)
        )}
    </>
  );
};

export default DrawingBoard;
