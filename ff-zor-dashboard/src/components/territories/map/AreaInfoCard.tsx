import {
  FaBirthdayCake,
  FaDollarSign,
  FaHome,
  FaUserFriends,
  FaUsers,
} from 'react-icons/fa';

import { DEFAULT_CENSUS_DATA_FIELDS } from '@/constants/territories/variables';
import { formatDecimal, formatValue } from '@/lib/string';
import {
  AggregatedStats,
  calculateAggregatedStats,
  isDecimal,
} from '@/lib/territories';
import { AreaInfo } from '@/types/territories/area.info.type';
import { CensusFieldKey } from '@/types/territories/census.field.type';

interface AreaInfoCardProps {
  stats?: AggregatedStats;
  areaInfos?: AreaInfo[];
}

const getIconForVariable = (label: string) => {
  const lowercasedLabel = label.toLowerCase();
  if (lowercasedLabel.includes('population')) {
    return <FaUsers className="text-xs text-green-600" />;
  }
  if (lowercasedLabel.includes('over') || lowercasedLabel.includes('under')) {
    return <FaUserFriends className="text-xs text-blue-600" />;
  }

  if (lowercasedLabel.includes('income')) {
    return <FaDollarSign className="text-xs text-emerald-600" />;
  }

  if (lowercasedLabel.includes('age') && lowercasedLabel.includes('median')) {
    return <FaBirthdayCake className="text-xs text-purple-600" />;
  }

  return <FaUsers className="text-xs text-green-600" />;
};

const AreaInfoCard = ({ stats, areaInfos }: AreaInfoCardProps) => {
  if (!stats && areaInfos) {
    stats = calculateAggregatedStats(areaInfos);
  }
  if (!stats) return;

  return (
    <div className="rounded-lg bg-white p-2 shadow-md transition-all hover:shadow-lg">
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2 border-b pb-1">
          <FaHome className="text-sm text-blue-600" />
          <h2 className="truncate text-sm font-semibold text-gray-800">
            {stats?.zctaCodes.join(', ')}
          </h2>
        </div>

        <div className="flex flex-wrap gap-x-4 gap-y-1">
          {DEFAULT_CENSUS_DATA_FIELDS.map(({ id, label }) => (
            <div className="flex items-center gap-1">
              {getIconForVariable(label)}
              <div>
                <div className="text-xs text-gray-500">{label}</div>
                <div className="text-xs font-medium">
                  {id.includes('age') && id.includes('median')
                    ? formatDecimal(stats[id as CensusFieldKey])
                    : formatValue(stats[id as CensusFieldKey])}
                </div>
              </div>
            </div>
          ))}

          {stats?.extraCensusData && stats.extraCensusData.length > 0 && (
            <>
              {stats.extraCensusData.map(
                (item, index) =>
                  item.label && (
                    <div key={index} className="flex items-center gap-1">
                      {getIconForVariable(item.label)}
                      <div>
                        <div className="text-xs text-gray-500">
                          {item.label}
                        </div>
                        <div className="text-xs font-medium">
                          {isDecimal(item.code)
                            ? formatDecimal(item.value)
                            : formatValue(item.value)}
                        </div>
                      </div>
                    </div>
                  )
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AreaInfoCard;
