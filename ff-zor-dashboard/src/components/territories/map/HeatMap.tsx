import { HeatmapLayer } from '@react-google-maps/api';
import { useCallback, useEffect, useState } from 'react';

import { HEATMAP_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import apiManager from '@/helpers/api.manager';

interface HeatMapProps {
  zoom: number;
  bounds: {
    ne: {
      lat: number;
      lng: number;
    };
    sw: {
      lat: number;
      lng: number;
    };
  };
  radius?: number;
  opacity?: number;
}

type ZipPolygon = {
  zcta_code: string;
  weight: number;
  isMedian: boolean;
  geom: GeoJSON.Polygon | GeoJSON.MultiPolygon;
};

const HeatMap = ({ zoom, bounds }: HeatMapProps) => {
  const [heatMapData, setHeatMapData] = useState<
    google.maps.visualization.WeightedLocation[]
  >([]);

  const getDynamicPointDensity = () => {
    if (zoom < 5) return 1;
    if (zoom < 10) return 2;
    if (zoom < 15) return 4;
    return 10;
  };

  // Approximate area of a polygon in square kilometers using the shoelace formula on a sphere
  const calculatePolygonArea = (
    geometry: GeoJSON.Polygon | GeoJSON.MultiPolygon
  ): number => {
    const EARTH_RADIUS_KM = 6371;

    const toRadians = (deg: number) => (deg * Math.PI) / 180;

    const calcRingArea = (coords: [number, number][]): number => {
      let area = 0;

      for (let i = 0, len = coords.length; i < len - 1; i++) {
        const [lng1, lat1] = coords[i];
        const [lng2, lat2] = coords[i + 1];

        area +=
          toRadians(lng2 - lng1) *
          (2 + Math.sin(toRadians(lat1)) + Math.sin(toRadians(lat2)));
      }

      return Math.abs((area * EARTH_RADIUS_KM * EARTH_RADIUS_KM) / 2);
    };

    if (geometry.type === 'Polygon') {
      return calcRingArea(
        geometry.coordinates[0].map(([lng, lat]) => [lng, lat])
      );
    }

    // MultiPolygon → sum all outer rings
    return geometry.coordinates.reduce((sum, poly) => {
      return sum + calcRingArea(poly[0].map(([lng, lat]) => [lng, lat]));
    }, 0);
  };

  const isPointInPolygon = (
    point: { lat: number; lng: number },
    polygon: [number, number][] // [lng, lat]
  ): boolean => {
    let inside = false;
    const x = point.lng;
    const y = point.lat;

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0],
        yi = polygon[i][1];
      const xj = polygon[j][0],
        yj = polygon[j][1];

      const intersect =
        yi > y !== yj > y &&
        x < ((xj - xi) * (y - yi)) / (yj - yi + Number.EPSILON) + xi;

      if (intersect) inside = !inside;
    }

    return inside;
  };

  const generateSampledPoints = (
    geometry: GeoJSON.Polygon | GeoJSON.MultiPolygon,
    numPoints = 30
  ): { lat: number; lng: number }[] => {
    const bounds = getPolygonBounds(geometry);
    const points: { lat: number; lng: number }[] = [];

    const steps = Math.sqrt(numPoints); // rows & cols
    const latStep = (bounds.maxLat - bounds.minLat) / steps;
    const lngStep = (bounds.maxLng - bounds.minLng) / steps;
    const distance = lngStep < latStep ? lngStep : latStep;
    const latNum = (bounds.maxLat - bounds.minLat) / distance;
    const lngNum = (bounds.maxLng - bounds.minLng) / distance;

    // Extract outer rings (skip holes)
    const outerPolygons: [number, number][][] =
      geometry.type === 'Polygon'
        ? [
            geometry.coordinates[0].map(
              ([lng, lat]) => [lng, lat] as [number, number]
            ),
          ]
        : geometry.coordinates.map((poly) =>
            poly[0].map(([lng, lat]) => [lng, lat] as [number, number])
          );

    for (let i = 0; i < latNum; i++) {
      for (let j = 0; j < lngNum; j++) {
        const lat = bounds.minLat + i * distance + distance / 2;
        const lng = bounds.minLng + j * distance + distance / 2;

        const inside = outerPolygons.some((poly) =>
          isPointInPolygon({ lat, lng }, poly)
        );

        if (inside) {
          points.push({ lat, lng });
        }
      }
    }

    return points;
  };

  const getPolygonBounds = (
    geometry: GeoJSON.Polygon | GeoJSON.MultiPolygon
  ): { minLat: number; maxLat: number; minLng: number; maxLng: number } => {
    const coords =
      geometry.type === 'Polygon'
        ? geometry.coordinates.flat()
        : geometry.coordinates.flat(2);

    const lats = coords.map(([, lat]) => lat);
    const lngs = coords.map(([lng]) => lng);

    return {
      minLat: Math.min(...lats),
      maxLat: Math.max(...lats),
      minLng: Math.min(...lngs),
      maxLng: Math.max(...lngs),
    };
  };

  // Calculate dynamic radius based on zoom level
  const getDynamicRadius = () => {
    const minZoom = 1;
    const maxZoom = 20;

    const clampedZoom = Math.max(minZoom, Math.min(zoom, maxZoom));

    if (clampedZoom <= 10) {
      // Original behavior for low zoom levels
      const minRadius = 9;
      const maxRadius = 20;
      const scale = (clampedZoom - minZoom) / (10 - minZoom); // Scale within [1, 10]
      const radius = minRadius + Math.pow(scale, 2) * (maxRadius - minRadius);
      return Math.round(radius);
    } else if (clampedZoom <= 15) {
      // Strongly increase radius for high zoom levels
      const baseRadius = 10;
      const extraZoomFactor = clampedZoom - 10;
      const boostedRadius = baseRadius + extraZoomFactor * 30;
      return Math.round(boostedRadius);
    } else {
      return 250;
    }
  };

  // Calculate dynamic opacity based on data weights
  const getDynamicOpacity = useCallback(() => {
    if (heatMapData.length === 0) return 0.6;

    // Calculate average weight of the data points
    const avgWeight =
      heatMapData.reduce((sum, point) => sum + point.weight, 0) /
      heatMapData.length;

    // Adjust opacity based on average weight
    // Higher weights = higher opacity
    return Math.min(0.8, Math.max(0.4, avgWeight * 0.1));
  }, [heatMapData]);

  const fetchHeatMapData = async () => {
    const response = await apiManager.get(HEATMAP_API_URL, {
      baseURL: configuration.TERRITORIES_API_URL,
      headers: { 'Content-Type': 'application/json' },
      params: {
        minLat: bounds.sw.lat,
        maxLat: bounds.ne.lat,
        minLng: bounds.sw.lng,
        maxLng: bounds.ne.lng,
      },
    });

    if (response.status !== 200) {
      return;
    }

    const rawZips: ZipPolygon[] = response.data.data || [];
    if (rawZips.length === 0) return;

    const allPoints: google.maps.visualization.WeightedLocation[] = [];
    const isMedian = rawZips[0].isMedian; // ✅ Assume all share the same value

    // If median, calculate min/max for normalization
    let normalize = (value: number) => value;
    if (isMedian) {
      const weights = rawZips.map((zip) => zip.weight);
      const min = Math.min(...weights);
      const max = Math.max(...weights);
      normalize = (value: number) =>
        max === min ? 1 : (value - min) / (max - min);
    }

    for (const zip of rawZips) {
      const area = calculatePolygonArea(zip.geom);
      const numPoints = Math.max(
        10,
        Math.round(area * getDynamicPointDensity())
      );
      const samples = generateSampledPoints(zip.geom, numPoints);

      let weightPerPoint: number;

      if (isMedian) {
        const norm = normalize(zip.weight);
        weightPerPoint = Math.pow(norm, 3) * 5; // boost visual difference
      } else {
        weightPerPoint = zip.weight / (samples.length / 2);
      }

      for (const pt of samples) {
        allPoints.push({
          location: new google.maps.LatLng(pt.lat, pt.lng),
          weight: weightPerPoint,
        });
      }
    }
    setHeatMapData(allPoints);
  };

  useEffect(() => {
    fetchHeatMapData();
  }, [zoom, bounds]);

  return (
    <HeatmapLayer
      data={heatMapData}
      options={{
        radius: getDynamicRadius(),
        opacity: getDynamicOpacity(),
        gradient: [
          'rgba(0, 0, 255, 0)',
          'rgba(0, 0, 255, 1)',
          'rgba(0, 255, 255, 1)',
          'rgba(0, 255, 0, 1)',
          'rgba(255, 255, 0, 1)',
          'rgba(255, 0, 0, 1)',
        ],
      }}
    />
  );
};

export default HeatMap;
