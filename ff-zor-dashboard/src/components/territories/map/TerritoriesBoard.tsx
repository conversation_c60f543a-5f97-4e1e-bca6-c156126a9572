import { Marker, Polygon } from '@react-google-maps/api';
import { Fragment, useEffect, useState } from 'react';

import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { POLYGON_HOVER_COLORS } from '@/constants/territories/colors';
import generateMarkerSvg from '@/constants/territories/icons';
import apiManager from '@/helpers/api.manager';
import { useToast } from '@/hooks/ui/use-toast';
import { getTerritoryColor } from '@/lib/territories';
import logger from '@/services/logger.service';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { Territory } from '@/types/territories/territory.type';

import TerritoryStatsDialog from './TerritoryStatsDialog';

interface TerritoriesBoardProps {
  zoom: number;
  bounds: {
    ne: {
      lat: number;
      lng: number;
    };
    sw: {
      lat: number;
      lng: number;
    };
  };
}

const TerritoriesBoard = ({ zoom, bounds }: TerritoriesBoardProps) => {
  const [hoveredTerritory, setHoveredTerritory] = useState<Territory | null>(
    null
  );
  const [selectedTerritory, setSelectedTerritory] = useState<Territory | null>(
    null
  );

  const { mapTerritories, setMapTerritories, territorySettings } =
    useTerritoryStore();
  const { toast } = useToast();

  const handleTerritoryClick = (territory: Territory) => (): void => {
    setSelectedTerritory(territory);
  };

  const handleTerritoryMouseOut = (): void => {
    setHoveredTerritory(null);
  };

  const handleTerritoryMouseOver = (territory: Territory) => (): void => {
    setHoveredTerritory(territory);
  };

  const fetchTerritories = async () => {
    try {
      const response = await apiManager.get(TERRITORIES_API_URL, {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
        params: {
          minLat: bounds.sw.lat,
          maxLat: bounds.ne.lat,
          minLng: bounds.sw.lng,
          maxLng: bounds.ne.lng,
        },
      });

      if (response.status !== 200) {
        toast({
          title: 'Error loading territories',
          description: 'Failed to load territory data for this area',
          variant: 'destructive',
        });
        return;
      }

      setMapTerritories(response.data.data);
    } catch (error) {
      logger.error('Failed to fetch territories', error as Error, {
        bounds,
        zoom,
      });

      toast({
        title: 'Error loading territories',
        description:
          'Unable to connect to territory service. Please try again later.',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    fetchTerritories();
  }, [zoom, bounds]);

  const renderTerritory = (territory: Territory, index: number) => {
    let multiPolygonCoordinates: number[][][] = [];
    const territoryColor = getTerritoryColor(
      territory.status,
      territorySettings
    );

    if (territory.geom.type === 'MultiPolygon') {
      multiPolygonCoordinates = territory.geom.coordinates.map(
        (coordinates) => coordinates[0]
      );
    } else {
      multiPolygonCoordinates = [territory.geom.coordinates[0]];
    }

    return (
      <Fragment key={`territory-${territory.id}-${index}`}>
        {multiPolygonCoordinates.map((coordinates, idx) => {
          return (
            <Polygon
              key={`${territory.geom.type}-${idx}`}
              paths={coordinates.map(([lng, lat]) => ({
                lat,
                lng,
              }))}
              options={{
                zIndex: 20,
                fillColor:
                  hoveredTerritory?.id === territory.id
                    ? POLYGON_HOVER_COLORS[territory.status]
                    : territoryColor,
                fillOpacity: 0.7,
                strokeWeight: 2,
                strokeColor: '#1a1a1a',
                strokeOpacity: 1,
              }}
              onClick={handleTerritoryClick(territory)}
              onMouseOut={handleTerritoryMouseOut}
              onMouseOver={handleTerritoryMouseOver(territory)}
            />
          );
        })}
      </Fragment>
    );
  };

  const renderTerritoryCentroid = (territory: Territory, index: number) => {
    return (
      <Marker
        key={`territory-centroid-${territory.id}-${index}`}
        icon={{
          url: generateMarkerSvg(
            getTerritoryColor(territory.status, territorySettings)
          ),
          scaledSize: new google.maps.Size(30, 37),
        }}
        position={{
          lat: territory.centroid.coordinates[1],
          lng: territory.centroid.coordinates[0],
        }}
        onClick={handleTerritoryClick(territory)}
      />
    );
  };

  return (
    <>
      {zoom > 7 &&
        mapTerritories?.map((territory, index) =>
          renderTerritory(territory, index)
        )}
      {mapTerritories?.map((territory, index) =>
        renderTerritoryCentroid(territory, index)
      )}
      {selectedTerritory && (
        <TerritoryStatsDialog
          territory={selectedTerritory}
          onCloseDialog={() => setSelectedTerritory(null)}
        />
      )}
    </>
  );
};

export default TerritoriesBoard;
