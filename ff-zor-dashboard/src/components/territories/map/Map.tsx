import { GoogleMap, LoadScript } from '@react-google-maps/api';
import { useEffect, useState } from 'react';

import DrawingBoard from './DrawingBoard';
import HeatMap from './HeatMap';
import TerritoriesBoard from './TerritoriesBoard';

interface MapProps {
  zoom?: number;
  center: { lat: number; lng: number };
  isEdit?: boolean;
  isHeatmap?: boolean;
}

const useDebounce = <T,>(value: T, delay: number = 500): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};

const Map = ({
  zoom = 4,
  center,
  isEdit = false,
  isHeatmap = false,
}: MapProps) => {
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [options, setOptions] = useState<google.maps.MapOptions>({
    zoomControl: true,
    disableDefaultUI: true,
  });
  const [currentZoom, setCurrentZoom] = useState<number>(zoom);
  const [currentCenter, setCurrentCenter] = useState<{
    lat: number;
    lng: number;
  }>(center);
  const [currentBounds, setCurrentBounds] = useState<{
    ne: { lat: number; lng: number };
    sw: { lat: number; lng: number };
  }>({
    ne: { lat: 0, lng: 0 },
    sw: { lat: 0, lng: 0 },
  });
  const debouncedZoom = useDebounce(currentZoom, 100);
  const debouncedBounds = useDebounce(currentBounds, 100);

  return (
    <LoadScript
      libraries={['visualization']}
      googleMapsApiKey={
        import.meta.env.VITE_PUBLIC_GOOGLE_MAPS_API_KEY as string
      }
    >
      <GoogleMap
        zoom={currentZoom}
        center={currentCenter}
        options={options}
        mapContainerStyle={{ width: '100%', height: '100%', borderRadius: 8 }}
        onLoad={(mapInstance) => {
          setMap(mapInstance);

          const zoom = mapInstance.getZoom();
          if (zoom) setCurrentZoom(zoom);

          const center = mapInstance.getCenter()?.toJSON();
          if (center) setCurrentCenter(center);

          setOptions({
            ...options,
            zoomControlOptions: {
              position: google.maps.ControlPosition.LEFT_BOTTOM,
            },
          });
        }}
        onIdle={() => {
          if (map) {
            // Handle zoom and center changes
            const newZoom = map.getZoom() ?? zoom;
            const newCenter = map.getCenter()?.toJSON() as {
              lat: number;
              lng: number;
            };

            // Check for significant changes in zoom and center
            const hasSignificantZoomChange =
              Math.abs(currentZoom - newZoom) > 0.5;
            const hasSignificantCenterChange =
              Math.abs(newCenter.lat - currentCenter.lat) > 0.01 ||
              Math.abs(newCenter.lng - currentCenter.lng) > 0.01;

            // Update zoom and center if needed
            if (hasSignificantZoomChange) {
              setCurrentZoom(newZoom);
            }
            if (hasSignificantCenterChange) {
              setCurrentCenter(newCenter);
            }

            // Handle bounds changes
            const bounds = map.getBounds();
            if (bounds) {
              const newBounds = {
                ne: bounds.getNorthEast().toJSON(),
                sw: bounds.getSouthWest().toJSON(),
              };

              const hasSignificantBoundsChange =
                Math.abs(newBounds.ne.lat - currentBounds.ne.lat) > 0.01 ||
                Math.abs(newBounds.ne.lng - currentBounds.ne.lng) > 0.01 ||
                Math.abs(newBounds.sw.lat - currentBounds.sw.lat) > 0.01 ||
                Math.abs(newBounds.sw.lng - currentBounds.sw.lng) > 0.01;

              if (hasSignificantBoundsChange) {
                setCurrentBounds(newBounds);
              }
            }
          }
        }}
      >
        {isEdit && (
          <DrawingBoard zoom={debouncedZoom} bounds={debouncedBounds} />
        )}
        {isHeatmap && <HeatMap zoom={debouncedZoom} bounds={debouncedBounds} />}
        <TerritoriesBoard zoom={debouncedZoom} bounds={debouncedBounds} />
      </GoogleMap>
    </LoadScript>
  );
};

export default Map;
