import { Loader2, UserIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { US_STATES } from '@/constants/territories/states';
import { getNextStatusOptions } from '@/constants/territories/status-dropdown-options';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { useGetAll<PERSON>ees } from '@/hooks/zee/useGetAllZees';
import { getTerritoryColor } from '@/lib/territories';
import useTerritoryStore from '@/stores/useTerritoryStore';
import useZeeStore from '@/stores/useZeeStore';
import { Territory } from '@/types/territories/territory.type';

interface AssignUserDialogProps {
  open: boolean;
  territory: Territory;
  onCloseDialog: () => void;
}

const AssignUserDialog = ({
  open,
  territory,
  onCloseDialog,
}: AssignUserDialogProps) => {
  const {
    territories,
    setTerritories,
    allTerritories,
    setAllTerritories,
    territorySettings,
  } = useTerritoryStore();

  const [isAssigning, setIsAssigning] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | undefined>(
    undefined
  );
  const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(
    territory.status || 'AVAILABLE'
  );
  const { zees, setZees } = useZeeStore();
  const { result, isError, isSuccess } = useGetAllZees();

  useEffect(() => {
    if (isSuccess && result?.data) {
      setZees(result.data);
    }
  }, [isSuccess, result, setZees]);

  useEffect(() => {
    if (isError) {
      toast({
        title: 'Failed to load users',
        description: 'Please try again later.',
        variant: 'destructive',
      });
    }
  }, [isError]);

  const handleAssignUser = async () => {
    if (!selectedUser) {
      toast({
        title: 'Please select a user',
        variant: 'destructive',
      });
      return;
    }
    if (selectedStatus != 'RESERVED' && selectedStatus != 'SOLD') {
      toast({
        title: 'Invalid Status Selected',
        description: 'Please select either "RESERVED" or "SOLD" to proceed.',
        variant: 'destructive',
      });
      return;
    }

    const assignedUser = zees?.find((user) => user.id == selectedUser);

    setIsAssigning(true);
    const response = await apiManager.patch(
      `${TERRITORIES_API_URL}/${territory.id}`,
      {
        assigned_to: assignedUser?.id,
        status: selectedStatus,
      },
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status !== 200) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    } else {
      const assignedUser = zees?.find((user) => user.id == selectedUser);
      setTerritories(
        territories?.map((t) =>
          t.id === territory.id
            ? { ...t, assigned_to: assignedUser, status: selectedStatus }
            : t
        ) as Territory[]
      );
      setAllTerritories(
        allTerritories?.map((t) =>
          t.id === territory.id
            ? { ...t, assigned_to: assignedUser, status: selectedStatus }
            : t
        ) as Territory[]
      );
      onCloseDialog();
    }
    setIsAssigning(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) onCloseDialog();
      }}
    >
      <DialogContent className="flex flex-col gap-0 p-0">
        <DialogHeader className="border-b border-border p-4">
          <DialogTitle>Assign user</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4 p-5">
          <div className="flex flex-col gap-3 rounded-lg border border-border bg-muted p-4">
            <div className="flex items-center justify-between gap-5">
              <p className="as-title-04 flex-shrink-0 text-muted-foreground">
                Zip Code
              </p>
              <p className="as-title-04 flex-1 truncate">
                {territory.zcta_codes.join(', ')}
              </p>
            </div>
            <div className="flex items-center justify-between gap-5">
              <p className="as-title-04 text-muted-foreground">
                Territory Number
              </p>
              <p className="as-title-04">
                {territory.id.toString().padStart(5, '0')}
              </p>
            </div>
            <div className="flex items-center justify-between gap-5">
              <p className="as-title-04 text-muted-foreground">State</p>
              <p className="as-title-04">
                {US_STATES[territory.state as keyof typeof US_STATES]}
              </p>
            </div>
            <div className="flex items-center justify-between gap-5">
              <p className="as-title-04 text-muted-foreground">Status</p>
              <Popover
                open={statusDropdownOpen}
                onOpenChange={setStatusDropdownOpen}
              >
                <PopoverTrigger asChild>
                  <div className={`flex w-24 justify-start`}>
                    <Badge
                      color={getTerritoryColor(
                        selectedStatus,
                        territorySettings
                      )}
                      className="w-full cursor-pointer justify-center"
                    >
                      {selectedStatus}
                    </Badge>
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-28 space-y-1 p-2">
                  {getNextStatusOptions('PENDING').map((statusOption) => (
                    <Badge
                      color={getTerritoryColor(statusOption, territorySettings)}
                      className="w-full cursor-pointer justify-center text-center"
                      onClick={() => {
                        setSelectedStatus(statusOption);
                        setStatusDropdownOpen(false);
                      }}
                    >
                      {statusOption}
                    </Badge>
                  ))}
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-center justify-between gap-5">
              <p className="as-title-04 text-muted-foreground">Assigned to</p>
              {territory.assigned_to ? (
                <div className="flex items-center gap-2">
                  <Avatar className="h-5 w-5">
                    <AvatarImage src={territory.assigned_to.photo_url} />
                    <AvatarFallback>
                      <UserIcon className="h-3 w-3" />
                    </AvatarFallback>
                  </Avatar>
                  <p className="as-title-04 text-gray-700">
                    {territory.assigned_to.full_name}
                  </p>
                </div>
              ) : (
                <p className="as-title-04 text-gray-700">N/A</p>
              )}
            </div>
          </div>
          <div className="space-y-1">
            <p className="as-title-03 text-gray-700">User</p>
            <SearchableSelect
              value={selectedUser}
              options={
                zees?.map((user) => ({
                  value: user.id,
                  label: user.full_name,
                })) || []
              }
              placeholder="Select a user"
              onValueChange={(value) => {
                setSelectedUser(value);
              }}
            />
          </div>
        </div>
        <div className="flex items-center justify-between border-t border-border px-5 py-4">
          <Button variant="outline" onClick={onCloseDialog}>
            Cancel
          </Button>
          <Button disabled={isAssigning} onClick={handleAssignUser}>
            {isAssigning && <Loader2 className="size-4 animate-spin" />}
            Assign
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssignUserDialog;
