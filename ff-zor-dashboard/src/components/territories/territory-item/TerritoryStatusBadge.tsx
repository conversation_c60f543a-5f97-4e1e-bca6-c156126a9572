import { Heart } from 'lucide-react';

import { cn } from '@/lib/ui/utils';
import { TerritoryStatus } from '@/types/territories/territory.status.type';

interface TerritoryStatusBadgeProps {
  count?: number;
  label: TerritoryStatus;
  showIcon?: boolean;
  color?: string;
}

const TerritoryStatusBadge = ({
  count,
  label,
  showIcon = false,
  color,
}: TerritoryStatusBadgeProps) => {
  return (
    <div
      className={cn(
        'flex h-9 items-center justify-center gap-1 rounded-md bg-white px-3.5 py-1',
        !color && 'border'
      )}
      style={{ backgroundColor: color }}
    >
      {showIcon && <Heart className="size-4 text-gray-700" stroke="black" />}
      {count && <p className="as-strong text-gray-700">{count}</p>}
      <p
        className={cn(
          'as-body-03',
          color ? 'text-white' : 'text-muted-foreground'
        )}
      >
        {label.charAt(0).toUpperCase() + label.slice(1).toLowerCase()}
      </p>
    </div>
  );
};

export { TerritoryStatusBadge };
