import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { DEFAULT_CENSUS_DATA_FIELDS } from '@/constants/territories/variables';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { formatDecimal, formatValue } from '@/lib/string';
import { isDecimal } from '@/lib/territories';
import { CensusFieldKey } from '@/types/territories/census.field.type';
import { Territory } from '@/types/territories/territory.type';

interface DemographicTabProps {
  territory: Territory;
}

const DemographicTab = ({ territory }: DemographicTabProps) => {
  const [demographic, setDemographic] = useState<Territory | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDemographic = async () => {
      const response = await apiManager.get(
        `${TERRITORIES_API_URL}/${territory.id}`,
        {
          baseURL: configuration.TERRITORIES_API_URL,
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (response.status !== 200) {
        toast({
          title: 'Something went wrong',
          description: 'Please try again later',
          variant: 'destructive',
        });
      } else {
        setDemographic(response.data.data);
        setLoading(false);
      }
    };

    fetchDemographic();
  }, []);

  if (loading) {
    return (
      <div className="flex w-full items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    demographic && (
      <div className="flex flex-wrap gap-4">
        {DEFAULT_CENSUS_DATA_FIELDS.map(({ id, label }) => (
          <div className="flex w-60 flex-col gap-1">
            <p className="as-title-04 text-muted-foreground">{label}</p>
            <p className="as-title-03 as-strong text-gray-700">
              {id.includes('age') && id.includes('median')
                ? formatDecimal(demographic[id as CensusFieldKey])
                : formatValue(demographic[id as CensusFieldKey])}
            </p>
          </div>
        ))}
        {demographic.extra_census_data &&
          demographic.extra_census_data.length > 0 && (
            <>
              {demographic.extra_census_data.map(
                (item, index) =>
                  item.label && (
                    <div key={index} className="flex w-60 flex-col gap-1">
                      <p className="as-title-04 text-muted-foreground">
                        {item.label}
                      </p>
                      <p className="as-title-03 as-strong text-gray-700">
                        {isDecimal(item.code)
                          ? formatDecimal(item.value)
                          : formatValue(item.value)}
                      </p>
                    </div>
                  )
              )}
            </>
          )}
      </div>
    )
  );
};

export default DemographicTab;
