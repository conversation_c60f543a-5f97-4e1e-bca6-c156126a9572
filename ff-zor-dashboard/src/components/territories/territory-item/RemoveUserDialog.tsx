import { Check<PERSON><PERSON>cle2, <PERSON>ader2, X, XCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface RemoveUserDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  loading: boolean;
}

const RemoveUserDialog = ({
  isOpen,
  onOpenChange,
  onConfirm,
  loading,
}: RemoveUserDialogProps) => {
  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button size="icon" variant="outline" className="h-4 w-4">
          <X className="h-3 w-3 text-red-400" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="space-y-4 rounded-lg border border-border p-4">
        <div className="space-y-1">
          <p className="as-title-03 as-strong text-gray-700">
            Do you want to remove?
          </p>
          <p className="as-title-04 text-muted-foreground">
            You can still reassign someone
          </p>
        </div>
        <div className="flex items-center justify-between border-t border-border pt-4">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            <XCircle className="h-3 w-3" />
            Cancel
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-3 w-3 animate-spin" />
                Removing...
              </>
            ) : (
              <>
                <CheckCircle2 className="h-3 w-3" />
                Proceed
              </>
            )}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export { RemoveUserDialog };
