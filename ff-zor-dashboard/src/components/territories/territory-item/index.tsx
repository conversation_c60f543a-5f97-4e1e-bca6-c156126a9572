import {
  Check<PERSON><PERSON>cle2,
  Ch<PERSON>ronDown,
  ChevronUp,
  Trash2,
  X<PERSON><PERSON>cle,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { US_STATES } from '@/constants/territories/states';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { getTerritoryColor } from '@/lib/territories';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { Territory } from '@/types/territories/territory.type';

import TerritoryDetailView from './TerritoryDetailView';
import { TerritoryStatusBadge } from './TerritoryStatusBadge';

interface TerritoryItemProps {
  territory: Territory;
  isEditTab?: boolean;
}

const TerritoryItem = ({
  territory,
  isEditTab = false,
}: TerritoryItemProps) => {
  const { territories, setTerritories, territorySettings } =
    useTerritoryStore();

  const [isExpanded, setIsExpanded] = useState(false);
  const [isRemovingTerritory, setIsRemovingTerritory] = useState(false);
  const [openRemoveTerritoryDialog, setOpenRemoveTerritoryDialog] =
    useState(false);

  const handleRemoveTerritory = async () => {
    setIsRemovingTerritory(true);
    const response = await apiManager.delete(
      `${TERRITORIES_API_URL}/${territory.id}`,
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status !== 200) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    } else {
      setTerritories(
        territories?.map((t) =>
          t.id === territory.id ? { ...t, is_deleted: true } : t
        ) as Territory[]
      );
      setOpenRemoveTerritoryDialog(false);
    }
    setIsRemovingTerritory(false);
  };

  const handleToggleExpandClick = (): void => {
    setIsExpanded(!isExpanded);
  };

  const handleCancelRemoveClick = (): void => {
    setOpenRemoveTerritoryDialog(false);
  };

  return (
    <div>
      <div className="flex items-center justify-between border-t border-border px-6 py-3">
        <div
          className={cn(
            'flex items-center gap-2',
            territory.is_deleted && 'line-through'
          )}
        >
          <p className="as-body-02 as-strong text-gray-700">
            Territory {territory.id.toString().padStart(5, '0')}
          </p>
          <p className="as-body-03 text-muted-foreground">|</p>
          <p className="as-body-02 as-strong truncate text-gray-700">
            Zip: {territory.zcta_codes.join(', ')}
          </p>
          <p className="as-body-03 text-muted-foreground">|</p>
          <p className="as-body-02 as-strong text-gray-700">
            {US_STATES[territory.state as keyof typeof US_STATES]}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {territory.is_deleted && <Badge variant="destructive">Deleted</Badge>}
          {!territory.is_deleted && (
            <>
              {territory.likes_count > 0 && (
                <TerritoryStatusBadge
                  count={territory.likes_count}
                  label="LIKED"
                />
              )}
              <TerritoryStatusBadge
                label={territory.status}
                color={getTerritoryColor(territory.status, territorySettings)}
              />
            </>
          )}
          {!territory.is_deleted && !isEditTab && (
            <Button
              size="icon"
              variant="outline"
              onClick={handleToggleExpandClick}
            >
              {isExpanded ? <ChevronUp /> : <ChevronDown />}
            </Button>
          )}
          {!territory.is_deleted && isEditTab && (
            <Popover
              open={openRemoveTerritoryDialog}
              onOpenChange={setOpenRemoveTerritoryDialog}
            >
              <PopoverTrigger asChild>
                <Button size="icon" variant="destructive">
                  <Trash2 />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="space-y-4 rounded-lg border border-border p-4">
                <div className="space-y-1">
                  <p className="as-title-03 as-strong text-gray-700">
                    Do you want to remove?
                  </p>
                  <p className="as-title-04 text-muted-foreground">
                    Once you remove this territory, you can't undo it.
                  </p>
                </div>
                <div className="flex items-center justify-between border-t border-border pt-4">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelRemoveClick}
                  >
                    <XCircle className="h-3 w-3" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleRemoveTerritory}
                    disabled={isRemovingTerritory}
                  >
                    <CheckCircle2 className="h-3 w-3" />
                    {isRemovingTerritory ? 'Removing...' : 'Proceed'}
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>
      {isExpanded && !isEditTab && (
        <TerritoryDetailView territory={territory} />
      )}
    </div>
  );
};

export default TerritoryItem;
