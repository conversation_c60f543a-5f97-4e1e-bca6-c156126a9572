import { Loader2, UserIcon } from 'lucide-react';
import { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { getNextStatusOptions } from '@/constants/territories/status-dropdown-options';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { getTerritoryColor } from '@/lib/territories';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { Territory } from '@/types/territories/territory.type';

import AssignUserDialog from './AssignUserDialog';
import { RemoveUserDialog } from './RemoveUserDialog';

const MAX_VISIBLE = 15;

interface CandidatesTabProps {
  territory: Territory;
}

const CandidatesTab = ({ territory }: CandidatesTabProps) => {
  const { territories, setTerritories, territorySettings } =
    useTerritoryStore();

  const [showAll, setShowAll] = useState(false);
  const [isRemovingUser, setIsRemovingUser] = useState(false);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [openRemoveUserDialog, setOpenRemoveUserDialog] = useState(false);
  const [openAssignUserDialog, setOpenAssignUserDialog] = useState(false);
  const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);

  const assignedUser =
    territory.status == 'PENDING'
      ? territory.requested_by
      : territory.assigned_to;

  const likers = territory.territory_likers || [];
  const hiddenCount = Math.max(0, likers.length - MAX_VISIBLE);
  const visibleLikes = showAll ? likers : likers.slice(0, MAX_VISIBLE);

  const handleRemoveUser = async () => {
    setIsRemovingUser(true);
    const response = await apiManager.patch(
      `${TERRITORIES_API_URL}/${territory.id}`,
      {
        assigned_to: null,
        status: 'AVAILABLE',
      },
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status === 200) {
      setTerritories(
        territories?.map((t) =>
          t.id === territory.id
            ? { ...t, status: 'AVAILABLE', assigned_to: null }
            : t
        ) as Territory[]
      );
      toast({ title: 'User Removed' });
    } else {
      toast({ title: 'Failed to remove user', variant: 'destructive' });
    }
    setIsRemovingUser(false);
    setOpenRemoveUserDialog(false);
  };

  const handleStatusChange = async (
    previousStatus: Territory['status'],
    newStatus: Territory['status']
  ) => {
    const isAvailable = newStatus !== 'AVAILABLE';

    // Determine assigned_to ID for API call
    const assigned_id = isAvailable
      ? previousStatus === 'PENDING'
        ? territory.requested_by?.id
        : previousStatus === 'RESERVED'
          ? territory.assigned_to?.id
          : null
      : null;
    setStatusDropdownOpen(false);
    setIsLoadingStatus(true);
    const response = await apiManager.patch(
      `${TERRITORIES_API_URL}/${territory.id}`,
      { status: newStatus, assigned_to: assigned_id },
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status === 200) {
      const isAvailable = newStatus !== 'AVAILABLE';

      // Determine assigned_to ID for API call
      const assigned_user = isAvailable
        ? previousStatus === 'PENDING'
          ? territory.requested_by
          : previousStatus === 'RESERVED'
            ? territory.assigned_to
            : null
        : null;
      setTerritories(
        territories?.map((t) =>
          t.id === territory.id
            ? { ...t, status: newStatus, assigned_to: assigned_user }
            : t
        ) as Territory[]
      );
      toast({
        title: 'Status Updated',
        description: `Territory is now ${newStatus}`,
      });
      setStatusDropdownOpen(false);
    } else {
      toast({
        title: 'Failed to update status',
        variant: 'destructive',
      });
    }
    setIsLoadingStatus(false);
  };

  return (
    <div>
      <div className="flex gap-5 border-b border-border pb-4">
        <div className="w-60 space-y-1">
          <p className="as-title-04 text-muted-foreground">Status</p>
          <Popover
            open={
              statusDropdownOpen &&
              getNextStatusOptions(territory.status).length > 0
            }
            onOpenChange={setStatusDropdownOpen}
          >
            <PopoverTrigger asChild>
              <div
                className={`flex w-24 justify-start ${isLoadingStatus ? 'pointer-events-none' : ''}`}
              >
                <Badge
                  color={getTerritoryColor(territory.status, territorySettings)}
                  className="w-full cursor-pointer justify-center"
                >
                  {isLoadingStatus ? (
                    <Loader2 className="size-4 animate-spin" />
                  ) : (
                    territory.status
                  )}
                </Badge>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-28 space-y-1 p-2">
              {getNextStatusOptions(territory.status).map((statusOption) => (
                <Badge
                  color={getTerritoryColor(statusOption, territorySettings)}
                  className="w-full cursor-pointer justify-center text-center"
                  onClick={() =>
                    handleStatusChange(territory.status, statusOption)
                  }
                >
                  {statusOption}
                </Badge>
              ))}
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-1">
          <p className="as-title-04 text-muted-foreground">Assign to</p>
          {assignedUser && territory.status != 'AVAILABLE' ? (
            <div className="flex items-center gap-2">
              <Avatar className="h-5 w-5">
                <AvatarImage src={assignedUser.photo_url} />
                <AvatarFallback>
                  <UserIcon className="h-3 w-3" />
                </AvatarFallback>
              </Avatar>
              <p className="as-title-04 text-gray-700">
                {assignedUser.full_name}
              </p>
              <RemoveUserDialog
                isOpen={openRemoveUserDialog}
                onOpenChange={setOpenRemoveUserDialog}
                onConfirm={handleRemoveUser}
                loading={isRemovingUser}
              />
            </div>
          ) : (
            <Button
              variant="ghost"
              className="px-0 text-primary hover:bg-transparent"
              onClick={() => setOpenAssignUserDialog(true)}
            >
              + Assign
            </Button>
          )}
        </div>
      </div>
      <div className="space-y-2 pt-4">
        <p className="as-title-03 as-strong text-gray-700">
          {likers.length} {likers.length === 1 ? 'Liker' : 'Likers'}
        </p>
        <div className="flex flex-row flex-wrap gap-2">
          {visibleLikes.map((like) => (
            <div key={like.id} className="flex w-40 items-center gap-2">
              <Avatar className="h-5 w-5">
                <AvatarImage src={like.photo_url} />
                <AvatarFallback>
                  <UserIcon className="h-3 w-3" />
                </AvatarFallback>
              </Avatar>
              <p className="as-title-04 as-strong truncate">{like.full_name}</p>
            </div>
          ))}
          {!showAll && hiddenCount > 0 && (
            <Button
              variant="ghost"
              className="px-0 text-primary hover:bg-transparent"
              onClick={() => setShowAll(true)}
            >
              +{hiddenCount} Show more
            </Button>
          )}
        </div>
      </div>
      <AssignUserDialog
        open={openAssignUserDialog}
        territory={territory}
        onCloseDialog={() => setOpenAssignUserDialog(false)}
      />
    </div>
  );
};

export default CandidatesTab;
