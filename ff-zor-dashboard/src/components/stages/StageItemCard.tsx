import { Edit, GripVertical, Loader2, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import DeleteDialog from '@/components/dialogs/DeleteDialog';
import EditStageItemForm from '@/components/forms/stages/EditStageItemForm';
import BadgeList from '@/components/global/BadgeList';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useDeleteStageItem } from '@/hooks/stage-items/useDeleteStageItem';
import { useUpdateStageItem } from '@/hooks/stage-items/useUpdateStageItem';
import { toast } from '@/hooks/ui/use-toast';
import useStageItemStore from '@/stores/stages/useStageItemStore';
import { StageItem } from '@/types/stage.item.type';
import { Stage } from '@/types/stage.type';

type StageItemCardProps = {
  data: StageItem;
  index: number;
  selectedStage: Stage | null;
};

const StageItemCard = ({ data, index, selectedStage }: StageItemCardProps) => {
  const [editCard, setEditCard] = useState<boolean>(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);

  const {
    updateStageItem: updateStageItemFromStore,
    deleteStageItem: deleteStageItemFromStore,
  } = useStageItemStore();

  const {
    mutate: updateStageItem,
    result: updateStageItemResult,
    isError: isUpdateError,
    isSuccess: isUpdateSuccess,
    isLoading: isUpdating,
  } = useUpdateStageItem();

  const {
    mutate: deleteStageItem,
    isError: isDeleteError,
    isSuccess: isDeleteSuccess,
    isLoading: isDeleting,
  } = useDeleteStageItem();

  const handleEdit = () => {
    setEditCard(true);
  };

  const handleEditClick = (): void => {
    handleEdit();
  };

  const handleDeleteClick = (): void => {
    setOpenDeleteDialog(true);
  };

  useEffect(() => {
    if (isDeleteError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
      });
    }
  }, [isDeleteError]);

  useEffect(() => {
    if (isUpdateError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
      });
    }
  }, [isUpdateError]);

  useEffect(() => {
    if (isDeleteSuccess) {
      toast({
        title: 'The stage item has been deleted',
      });
      deleteStageItemFromStore(data.id || '');
      setOpenDeleteDialog(false);
    }
  }, [isDeleteSuccess, deleteStageItemFromStore]);

  useEffect(() => {
    if (isUpdateSuccess) {
      updateStageItemFromStore(
        data.id || '',
        (updateStageItemResult?.data ?? {}) as StageItem
      );
      setEditCard(false);
    }
  }, [data, isUpdateSuccess, updateStageItemResult, updateStageItemFromStore]);

  return (
    <div className="flex w-full items-start justify-between gap-2 rounded-[12px]">
      <div className="flex h-[100px] w-6 flex-col items-center justify-between py-2">
        <div className="text-lg">{index + 1}</div>
        {!editCard && <GripVertical className="h-6 w-6 text-gray-400" />}
      </div>
      {!editCard && (
        <div className="flex w-full flex-row justify-between gap-2 rounded-lg bg-white p-5">
          <div className="flex flex-1 flex-col gap-2">
            <div className="text-sm font-semibold">{data.title}</div>
            <div className="text-sm">{data.description}</div>
          </div>
          <div className="flex flex-col items-end justify-between gap-8">
            <div className="flex cursor-pointer gap-1">
              <div
                className="rounded-md border border-secondary p-2.5"
                onClick={handleEditClick}
              >
                <Edit className="h-4 w-4" />
              </div>
              <div
                className="group rounded-md bg-red-50 p-2.5 hover:bg-red-500"
                onClick={handleDeleteClick}
              >
                <Trash2 className="h-4 w-4 text-red-500 group-hover:text-white" />
              </div>
            </div>
            <div className="flex min-w-72 items-center justify-between gap-3">
              <div className="flex items-center gap-1">
                <Switch
                  id={`is_pipeline_${data.id}`}
                  checked={data.is_pipeline}
                  disabled={isUpdating}
                  className="data-[state=checked]:bg-primary"
                  onCheckedChange={() => {
                    updateStageItem({
                      id: data.id || '',
                      stage_id: data.stage_id || '',
                      is_pipeline: !data.is_pipeline,
                    });
                  }}
                />
                {isUpdating ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Label htmlFor={`is_pipeline_${data.id}`}>Pipeline</Label>
                )}
              </div>
              <BadgeList linkType={data.type} />
            </div>
          </div>
        </div>
      )}
      {editCard && (
        <div className="flex-grow">
          <EditStageItemForm
            data={data}
            stage_id={selectedStage?.id || ''}
            setOpen={setEditCard}
          />
        </div>
      )}

      {openDeleteDialog && (
        <DeleteDialog
          open={openDeleteDialog}
          label1={data.title || ''}
          label2={`from ${selectedStage?.name}?`}
          isLoading={isDeleting}
          setOpen={setOpenDeleteDialog}
          onDelete={() => {
            deleteStageItem({
              stageId: selectedStage?.id || '',
              stageItemId: data.id || '',
            });
          }}
          onCancel={() => setOpenDeleteDialog(false)}
        />
      )}
    </div>
  );
};

export default StageItemCard;
