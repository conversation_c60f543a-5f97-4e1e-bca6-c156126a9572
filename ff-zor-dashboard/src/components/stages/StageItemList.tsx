import {
  DragDropContext,
  Draggable,
  Droppable,
  OnDragEndResponder,
} from '@hello-pangea/dnd';
import { Loader2, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import { AddStageItemForm } from '@/components/forms/stages/AddStageItemForm';
import Divider from '@/components/global/Divider';
import { Button } from '@/components/ui/button';
import { useGetStageItemsByStageId } from '@/hooks/stage-items/useGetStageItemsByStageId';
import { useUpdateStageItem } from '@/hooks/stage-items/useUpdateStageItem';
import { toast } from '@/hooks/ui/use-toast';
import logger from '@/services/logger.service';
import useStageItemStore from '@/stores/stages/useStageItemStore';
import useStageStore from '@/stores/stages/useStageStore';
import { StageItem } from '@/types/stage.item.type';
import { Stage } from '@/types/stage.type';

import StageItemCard from './StageItemCard';

type StageItemListProps = {
  selectedStage: Stage | null;
};

export const StageItemList = ({ selectedStage }: StageItemListProps) => {
  const [addItem, setAddItem] = useState(false);
  const [isSwappingItems, setIsSwappingItems] = useState(false);

  const { stageList } = useStageStore();
  const { stageItems, setStageItems } = useStageItemStore();

  const { mutate: updateStageItem } = useUpdateStageItem();
  const {
    result: stageItemsFromApi,
    isError: isStageItemsError,
    isSuccess: isStageItemsSuccess,
    isLoading: isStageItemsLoading,
    isRefetching: isStageItemsRefetching,
  } = useGetStageItemsByStageId(selectedStage?.id || '');

  const onDragEnd: OnDragEndResponder = (result) => {
    if (!result.destination) {
      return;
    }

    const { source, destination } = result;

    const items = Array.from(stageItems);
    const [removed] = items.splice(source.index, 1);
    items.splice(destination.index, 0, removed);

    const updatedItems = items.map((item, index) => {
      const newItem = { ...item };
      if (index === 0) {
        newItem.previous_stage_item_id = null;
      } else {
        newItem.previous_stage_item_id = items[index - 1].id;
      }
      return newItem;
    });

    const changedItems = updatedItems.filter((item, index) => {
      const originalItem = stageItems[index];
      return (
        originalItem?.id !== item.id ||
        originalItem?.previous_stage_item_id !== item.previous_stage_item_id
      );
    });

    const updateStageItems = async (
      changedItems: StageItem[],
      updatedItems: StageItem[]
    ) => {
      try {
        setIsSwappingItems(true);
        for (const item of changedItems) {
          await updateStageItem({
            id: item.id,
            stage_id: item.stage_id,
            previous_stage_item_id: item.previous_stage_item_id,
          });
        }
      } catch (error) {
        setIsSwappingItems(false);
        logger.error('Failed to update stage items order', error as Error, {
          stageId: selectedStage?.id,
          changedItemsCount: changedItems.length,
        });
        return;
      }

      setStageItems(updatedItems);
      setIsSwappingItems(false);
    };

    updateStageItems(changedItems, updatedItems);
  };

  const openAddItem = () => {
    if (stageList.length == 0) {
      toast({
        title: 'Please create any Stage First',
        variant: 'destructive',
      });
      return;
    }

    setAddItem(true);
  };

  const handleOpenAddItemClick = (): void => {
    openAddItem();
  };

  useEffect(() => {
    if (isStageItemsError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isStageItemsError]);

  useEffect(() => {
    if (isStageItemsSuccess && stageItemsFromApi?.data) {
      const sortedStageItems = (stageItemsFromApi.data ?? []).reduce<
        StageItem[]
      >((acc, curr) => {
        if (curr.previous_stage_item_id === null) {
          acc.push(curr);

          let nextItemId = curr.id;
          while (nextItemId) {
            const nextItem = (stageItemsFromApi?.data ?? []).find(
              (item) => item.previous_stage_item_id === nextItemId
            );
            if (nextItem) {
              acc.push(nextItem);
              nextItemId = nextItem.id;
            } else {
              nextItemId = undefined;
            }
          }
        }
        return acc;
      }, []);

      setStageItems(sortedStageItems);
    }
  }, [stageItemsFromApi, isStageItemsSuccess, setStageItems]);

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="relative rounded-lg border border-secondary bg-secondary">
        <div className="as-title-01 flex items-center gap-2 px-6 py-3 font-medium">
          {selectedStage?.name || 'Title'}
          {isStageItemsRefetching && (
            <Loader2 className="size-4 animate-spin" />
          )}
        </div>

        <Divider className="bg-gray-300" />
        <Droppable droppableId="stages">
          {(droppableProvided, snapshot) => (
            <div
              ref={droppableProvided.innerRef}
              className={`flex flex-col gap-2 p-4 ${snapshot.isDraggingOver ? 'isDraggingOver' : ''}`}
              {...droppableProvided.droppableProps}
            >
              {isStageItemsLoading ? (
                <Loader2 className="my-4 size-4 animate-spin self-center" />
              ) : (
                stageItems?.map((stageData, index) => (
                  <Draggable
                    key={`draggable_${index}`}
                    index={index}
                    draggableId={index.toString()}
                  >
                    {(provided) => (
                      <div
                        key={index}
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <StageItemCard
                          data={stageData}
                          index={index}
                          selectedStage={selectedStage}
                        />
                      </div>
                    )}
                  </Draggable>
                ))
              )}
              {addItem && (
                <AddStageItemForm
                  index={stageItems?.length}
                  setOpen={setAddItem}
                  stage_id={selectedStage?.id || ''}
                  previous_stage_item_id={
                    stageItems?.length > 0
                      ? stageItems[stageItems.length - 1].id || null
                      : null
                  }
                />
              )}
              {!addItem && (
                <div className="flex w-full items-center justify-center">
                  <Button variant="outline" onClick={handleOpenAddItemClick}>
                    <Plus /> Add new item
                  </Button>
                </div>
              )}
              {droppableProvided.placeholder}
            </div>
          )}
        </Droppable>
        {isSwappingItems && (
          <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-gray-50/40">
            <Loader2 className="size-4 animate-spin" />
          </div>
        )}
      </div>
    </DragDropContext>
  );
};
