import { Edit, Loader2, Plus, Trash } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import DeleteDialog from '@/components/dialogs/DeleteDialog';
import { AddStageForm } from '@/components/forms/stages/AddStageForm';
import { EditStageForm } from '@/components/forms/stages/EditStageForm';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { buttonVariants } from '@/components/ui/button';
import { useDeleteStage } from '@/hooks/stages/useDeleteStage';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useStageStore from '@/stores/stages/useStageStore';
import { Stage } from '@/types/stage.type';

type StageListProps = {
  selectedStage: Stage | null;
  setSelectedStage: React.Dispatch<React.SetStateAction<Stage | null>>;
};

const StageList = ({ selectedStage, setSelectedStage }: StageListProps) => {
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [newChildStageOpen, setNewChildStageOpen] = useState<boolean>(false);
  const [editingStage, setEditingStage] = useState<Stage | null>(null);

  const { stageList, deleteStage: deleteStageFromStore } = useStageStore();

  const { isLoading: isDeleteLoading, mutate: deleteStageFromApi } =
    useDeleteStage();

  const clickStage = (stage: Stage) => {
    setSelectedStage(stage);
  };

  const handleStageClick = (stage: Stage) => () => {
    clickStage(stage);
  };

  const handleAddStageClick = () => {
    setNewChildStageOpen(true);
  };

  const handleDeleteStageClick = () => {
    deleteStage();
  };

  const handleEditStageClick = () => {
    if (selectedStage) {
      setEditingStage(selectedStage);
    } else {
      toast({
        title: 'Please select one stage',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteConfirm = () => {
    confirmDelete(selectedStage?.id || '');
  };

  const handleDeleteCancel = () => {
    handleCancel();
  };

  const deleteStage = () => {
    if (selectedStage) {
      setOpenDeleteDialog(true);
    } else {
      toast({
        title: 'Please select one stage',
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    setOpenDeleteDialog(false);
  };

  const confirmDelete = async (_id: string) => {
    const res = await deleteStageFromApi({ id: _id });
    if (res.success) {
      toast({
        title: 'Deleted Stage Succesfully',
      });
      deleteStageFromStore(_id);
      setOpenDeleteDialog(false);
      const updatedList = stageList.filter((stage) => stage.id !== _id);
      setSelectedStage(updatedList[updatedList.length - 1]);
    } else {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
      setOpenDeleteDialog(false);
      return;
    }
  };

  useEffect(() => {
    if (stageList.length === 0) {
      setSelectedStage(null);
    } else {
      setSelectedStage(stageList[0]);
    }
  }, [stageList, setSelectedStage]);

  return (
    <Breadcrumb className="flex w-full flex-wrap justify-between gap-2">
      <BreadcrumbList>
        {/* eslint-disable-next-line no-constant-condition */}
        {false ? (
          <Loader2 className="size-4 animate-spin" />
        ) : (
          <>
            {stageList.map((stage: Stage) => (
              <div key={stage.id} className="flex items-center gap-1">
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  {editingStage?.id === stage.id ? (
                    <EditStageForm
                      stage={stage}
                      setEditingStage={setEditingStage}
                      setSelectedStage={setSelectedStage}
                    />
                  ) : (
                    <div
                      className={cn(
                        'cursor-pointer',
                        selectedStage?.id == stage.id && 'text-primary'
                      )}
                      onClick={handleStageClick(stage)}
                    >
                      {stage.name}
                    </div>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
            {stageList.length > 0 && <BreadcrumbSeparator />}
            {stageList.length > 0 && (
              <BreadcrumbItem>
                <BreadcrumbPage className="text-red-600">End</BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </>
        )}
        <BreadcrumbItem>
          {!newChildStageOpen ? (
            <div
              className="flex cursor-pointer items-center justify-center rounded-sm bg-muted p-2.5"
              onClick={handleAddStageClick}
            >
              <Plus className="h-4 w-4 text-black" />
            </div>
          ) : (
            <AddStageForm
              isFirstStage={stageList.length === 0}
              setNewStageOpen={setNewChildStageOpen}
              setSelectedStage={setSelectedStage}
            />
          )}
        </BreadcrumbItem>
      </BreadcrumbList>
      <div className="flex gap-2">
        <div
          className={cn(
            buttonVariants({ variant: 'outline' }),
            'cursor-pointer text-[12px]'
          )}
          onClick={handleEditStageClick}
        >
          <Edit className="h-4 w-4" /> Edit {selectedStage?.name}
        </div>
        <div
          className={cn(
            buttonVariants({ variant: 'outline' }),
            'cursor-pointer text-[12px]'
          )}
          onClick={handleDeleteStageClick}
        >
          <Trash className="h-4 w-4" /> Delete {selectedStage?.name}
        </div>
      </div>
      {openDeleteDialog && (
        <DeleteDialog
          open={openDeleteDialog}
          label1={selectedStage?.name || ''}
          label2="from your discovery stage ?"
          isLoading={isDeleteLoading}
          setOpen={setOpenDeleteDialog}
          onDelete={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      )}
    </Breadcrumb>
  );
};

export default StageList;
