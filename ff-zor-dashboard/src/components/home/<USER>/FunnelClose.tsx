import { TrendingUp } from 'lucide-react';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useGetFunnelCloseRate } from '@/hooks/home/<USER>';

export default function FunnelClose() {
  const { result, isLoading, isError } = useGetFunnelCloseRate();

  if (isError) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Funnel Close %</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-500">Error</div>
          <p className="text-xs text-muted-foreground">Unable to load data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Funnel Close %</CardTitle>
        <TrendingUp className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {isLoading ? (
            <div className="mb-2 h-8 w-16 animate-pulse rounded bg-gray-200"></div>
          ) : (
            `${result?.data?.close_rate_percentage || 0}%`
          )}
        </div>
        <p className="text-xs text-muted-foreground">
          {isLoading ? (
            <div className="h-3 w-24 animate-pulse rounded bg-gray-200"></div>
          ) : (
            result?.data?.description || 'No data available'
          )}
        </p>
      </CardContent>
    </Card>
  );
}
