import { useMemo } from 'react';

import { useGetTopEngagedCandidates } from '@/hooks/home/<USER>';

import { Badge } from '../ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Skeleton } from '../ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

export default function TopCandidatesTable() {
  // custom hooks
  const { result, isLoading, isError } = useGetTopEngagedCandidates();

  // derived values
  const candidates = useMemo(() => {
    return (
      result?.data?.candidates?.map((c) => ({
        id: c.zee_id,
        name: c.full_name,
        timeOnPlatform: `${c.time_on_platform} days`,
        actions: c.actions,
        stage: c.stage,
      })) ?? []
    );
  }, [result]);
  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Engaged Candidates</CardTitle>
        <CardDescription>
          Most active candidates in the discovery process
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading || isError ? (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Time on Platform</TableHead>
                  <TableHead>Actions</TableHead>
                  <TableHead>Stage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton className="h-4 w-40" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-10" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-6 w-28 rounded-full" />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {isError ? (
              <div className="mt-2 text-sm text-destructive">
                Failed to load candidates
              </div>
            ) : null}
          </>
        ) : candidates.length === 0 ? (
          <div className="text-sm text-muted-foreground">No data available</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Time on Platform</TableHead>
                <TableHead>Actions</TableHead>
                <TableHead>Stage</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {candidates.map((candidate) => (
                <TableRow
                  key={candidate.id}
                  className="hover:bg-muted/50 cursor-pointer"
                >
                  <TableCell className="font-medium">
                    {candidate.name}
                  </TableCell>
                  <TableCell>{candidate.timeOnPlatform}</TableCell>
                  <TableCell>{candidate.actions}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{candidate.stage}</Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
