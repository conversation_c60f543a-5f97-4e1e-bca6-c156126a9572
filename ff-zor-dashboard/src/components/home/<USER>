import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

import { useGetGeographicDistribution } from '@/hooks/home/<USER>';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { Skeleton } from '../ui/skeleton';

const chartConfig = {
  candidates: {
    label: 'Candidates',
    color: 'var(--brand-500)',
  },
  avgDays: {
    label: 'Avg Days',
    color: 'var(--brand-700)',
  },
  completionRate: {
    label: 'Completion Rate',
    color: 'var(--brand-300)',
  },
};

export default function GeographicChart() {
  const { result, isLoading, isError } = useGetGeographicDistribution();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Geographic Distribution</CardTitle>
          <CardDescription>Number of candidates by state</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] w-full">
            <div className="flex h-full items-end gap-2 pb-8">
              {Array.from({ length: 10 }).map((_, index) => (
                <div
                  key={index}
                  className="flex-1"
                  style={{
                    height: `${Math.random() * 60 + 20}%`,
                  }}
                >
                  <Skeleton className="h-full w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError || !result?.data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Geographic Distribution</CardTitle>
          <CardDescription>Failed to load geographic data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] w-full items-center justify-center text-muted-foreground">
            No data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const chartData = result.data.distribution
    .sort((a, b) => b.count - a.count) // Sort by count in descending order
    .slice(0, 10) // Take only top 10 states
    .map((item) => ({
      state: item.state,
      candidates: item.count,
    }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Geographic Distribution</CardTitle>
        <CardDescription>Top 10 states by number of candidates</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="state" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="candidates" fill="var(--brand-500)" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
