import { useGetDiscoveryFunnel } from '@/hooks/home/<USER>';
import { FunnelMetric } from '@/types/home.type';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { ChartContainer } from '../ui/chart';
import { Skeleton } from '../ui/skeleton';

const brandColors = [
  'var(--brand-300)',
  'var(--brand-400)',
  'var(--brand-500)',
  'var(--brand-600)',
  'var(--brand-700)',
  'var(--brand-800)',
];

const chartConfig = {
  candidates: {
    label: 'Candidates',
    color: 'var(--brand-500)',
  },
  avgDays: {
    label: 'Avg Days',
    color: 'var(--brand-700)',
  },
  completionRate: {
    label: 'Completion Rate',
    color: 'var(--brand-300)',
  },
};

export default function DiscoveryFunnelChart() {
  const { result, isLoading, isError } = useGetDiscoveryFunnel();

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Discovery Funnel</CardTitle>
          <CardDescription className="text-destructive">
            Failed to load funnel data. Please try again later.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (isLoading || !result?.data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Discovery Funnel</CardTitle>
          <CardDescription>Loading funnel data...</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-full">
            <div className="space-y-4">
              <div className="mt-6 space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <div className="w-32 bg-gray-200">
                      <Skeleton className="h-5 w-24" />
                    </div>
                    <div className="relative h-8 flex-1 rounded-full bg-gray-200">
                      <Skeleton className="h-8 w-3/4 rounded-full" />
                    </div>
                    <div className="w-16 bg-gray-200">
                      <Skeleton className="ml-auto h-5 w-12" />
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 rounded-lg bg-muted p-4">
                <div className="space-y-2 text-center">
                  <Skeleton className="mx-auto h-8 w-16" />
                  <Skeleton className="mx-auto h-5 w-40" />
                  <Skeleton className="mx-auto h-4 w-48" />
                </div>
              </div>
            </div>
          </ChartContainer>
        </CardContent>
      </Card>
    );
  }

  const funnelData = result.data
    .sort((a: FunnelMetric, b: FunnelMetric) => a.stage_index - b.stage_index)
    .map((metric: FunnelMetric, index: number) => ({
      name: metric.stage_name,
      value: metric.completed_count,
      fill: brandColors[index],
      percentage: `${Math.round(metric.completion_percentage)}%`,
    }));

  const initialValue = funnelData[0]?.value || 0;
  const finalValue = funnelData[funnelData.length - 1]?.value || 0;
  const closeRate = initialValue > 0 ? (finalValue / initialValue) * 100 : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Discovery Funnel</CardTitle>
        <CardDescription>
          Candidate progression through discovery stages
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-full">
          <div className="space-y-4">
            {/* Visual Funnel */}
            <div className="mt-6 space-y-2">
              {funnelData.map(
                (stage: {
                  name: string;
                  value: number;
                  fill: string;
                  percentage: string;
                }) => {
                  const width =
                    initialValue > 0 ? (stage.value / initialValue) * 100 : 0;
                  return (
                    <div
                      key={stage.name}
                      className="flex items-center space-x-3"
                    >
                      <div className="w-32 truncate text-sm font-medium">
                        {stage.name}
                      </div>
                      <div className="relative h-8 flex-1 rounded-full bg-gray-200">
                        <div
                          className="flex h-8 items-center justify-end rounded-full pr-3 text-sm font-medium text-white"
                          style={{
                            width: `${width}%`,
                            backgroundColor: stage.fill,
                            minWidth: '60px',
                          }}
                        >
                          {stage.percentage}
                        </div>
                      </div>
                      <div className="w-16 text-right text-sm text-muted-foreground">
                        {stage.value}
                      </div>
                    </div>
                  );
                }
              )}
            </div>

            {/* Funnel Close Rate */}
            <div className="mt-6 rounded-lg bg-muted p-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {closeRate.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Overall Funnel Close Rate
                </div>
                <div className="mt-1 text-xs text-muted-foreground">
                  {finalValue} closed from {initialValue} initial candidates
                </div>
              </div>
            </div>
          </div>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
