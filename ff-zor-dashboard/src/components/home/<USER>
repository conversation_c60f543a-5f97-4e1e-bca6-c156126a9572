import {
  Bar,
  CartesianGrid,
  Composed<PERSON>hart,
  Line,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from 'recharts';

import { useGetCandidateStageMetrics } from '@/hooks/home/<USER>';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { Skeleton } from '../ui/skeleton';

const chartConfig = {
  candidates: {
    label: 'Candidates',
    color: 'var(--brand-500)',
  },
  avgDays: {
    label: 'Avg Days',
    color: 'var(--brand-700)',
  },
  completionRate: {
    label: 'Completion Rate',
    color: 'var(--brand-300)',
  },
};

export default function CandidateMetricsChart() {
  // data
  const { result, isLoading, isError } = useGetCandidateStageMetrics();

  // derived
  const chartData = (result?.data.metrics ?? []).map((m) => ({
    stage: m.stage_name,
    candidates: m.candidates,
    avgDays: m.avg_days,
    completionRate: m.completion_rate,
  }));

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Candidate Stage Metrics</CardTitle>
          <CardDescription className="text-destructive">
            Failed to load metrics. Please try again later.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Candidate Stage Metrics</CardTitle>
        <CardDescription>
          Candidates per stage with completion rates and timeline
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-[400px] w-full">
            <div className="space-y-4 p-4">
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        ) : chartData.length === 0 ? (
          <div className="h-[100px] text-sm text-muted-foreground">
            No data available
          </div>
        ) : (
          <ChartContainer config={chartConfig} className="h-[400px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="stage"
                  angle={-45}
                  textAnchor="end"
                  height={100}
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar
                  yAxisId="left"
                  dataKey="candidates"
                  fill="var(--brand-500)"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="avgDays"
                  stroke="var(--brand-700)"
                  strokeWidth={2}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="completionRate"
                  stroke="var(--brand-300)"
                  strokeWidth={2}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
