import { ChevronDown } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useGetConsultantsPerformance } from '@/hooks/home/<USER>';

function TableSkeleton() {
  return (
    <>
      {Array.from({ length: 5 }).map((_, i) => (
        <TableRow key={i}>
          <TableCell>
            <Skeleton className="h-5 w-[180px] bg-gray-200" />
          </TableCell>
          <TableCell>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-6 w-[80px] bg-gray-200" />
              <Skeleton className="h-4 w-4 bg-gray-200" />
            </div>
          </TableCell>
          <TableCell>
            <Skeleton className="h-5 w-[40px] bg-gray-200" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-5 w-[40px] bg-gray-200" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-6 w-[60px] bg-gray-200" />
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}

interface NetworkBadgeProps {
  networks: string[];
}

function NetworkBadge({ networks }: NetworkBadgeProps) {
  if (!networks.length) {
    return <Badge variant="outline">Direct</Badge>;
  }

  if (networks.length === 1) {
    return <Badge variant="outline">{networks[0]}</Badge>;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="h-auto p-0 font-normal">
          <Badge variant="outline" className="mr-1">
            {networks[0]}
          </Badge>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit p-2">
        <div className="flex flex-col gap-1">
          {networks.slice(1).map((network, index) => (
            <Badge key={index} variant="outline">
              {network}
            </Badge>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default function ConsultantPerformanceTable() {
  const { result, isLoading, isError } = useGetConsultantsPerformance();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Consultant Performance</CardTitle>
          <CardDescription>
            Top performing consultants by conversion metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <Skeleton className="h-4 w-20 bg-gray-200" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-16 bg-gray-200" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-20 bg-gray-200" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-14 bg-gray-200" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-12 bg-gray-200" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableSkeleton />
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Consultant Performance</CardTitle>
          <CardDescription>
            Top performing consultants by conversion metrics
          </CardDescription>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">Error</div>
            <p className="text-xs text-muted-foreground">Unable to load data</p>
          </CardContent>
        </CardHeader>
      </Card>
    );
  }

  const sortedConsultants =
    result?.data?.consultants.sort(
      (a, b) => b.conversion_rate - a.conversion_rate
    ) || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Consultant Performance</CardTitle>
        <CardDescription>
          Top performing consultants by conversion metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Consultant</TableHead>
              <TableHead>Network</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead>Closed</TableHead>
              <TableHead>Rate</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedConsultants.map((consultant) => (
              <TableRow key={consultant.consultant_id}>
                <TableCell className="font-medium">
                  {consultant.consultant_name}
                </TableCell>
                <TableCell>
                  <NetworkBadge
                    networks={consultant.consultant_firm_affiliated}
                  />
                </TableCell>
                <TableCell>{consultant.total_count}</TableCell>
                <TableCell>{consultant.closed_count}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      consultant.conversion_rate > 40 ? 'default' : 'secondary'
                    }
                  >
                    {consultant.conversion_rate.toFixed(1)}%
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
