import {
  useAuth,
  useOrganization,
  useOrganizationList,
} from '@clerk/clerk-react';
import { useEffect, useRef } from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router-dom';

import MainLayout from '@/layouts/MainLayout';
import { ConsultantProfile } from '@/pages/dashboard/consultant-profile';
import DashboardDataCenter from '@/pages/dashboard/data-center';
import DashboardEmailTemplate from '@/pages/dashboard/email-templates/EmailTemplate';
import DashboardFinancialOverview from '@/pages/dashboard/financial-overview/FinancialOverview';
import DashboardHome from '@/pages/dashboard/Home';
import DashboardInternalManager from '@/pages/dashboard/internal-manager';
import DashboardProfile from '@/pages/dashboard/profile';
import DashboardGeneralSettings from '@/pages/dashboard/settings/Settings';
import DashboardStagesChecklists from '@/pages/dashboard/stages-checklist/StagesChecklists';
import DashboardTerritories from '@/pages/dashboard/territories';
import ZeeProfile from '@/pages/dashboard/zee-profile';
import logger from '@/services/logger.service';

import { LoadingSpinner } from './AuthRoutes';

const TOKEN_TEMPLATE = 'ff-backend-new' as const;
const TOKEN_STORAGE_KEY = 'access-token' as const;

const DashboardRoutes = () => {
  const location = useLocation();

  const { getToken } = useAuth();
  const { organization } = useOrganization();
  const { isLoaded, userMemberships, setActive } = useOrganizationList({
    userMemberships: { infinite: true, keepPreviousData: true },
  });

  // Track previous organization to detect changes
  const previousOrganizationId = useRef<string | null>(null);

  useEffect(() => {
    const updateToken = async () => {
      try {
        const token = await getToken({ template: TOKEN_TEMPLATE });
        if (token) {
          window.localStorage.setItem(TOKEN_STORAGE_KEY, token);
        }
      } catch (error) {
        logger.error('Failed to update access token', error as Error, {
          organizationId: organization?.id,
        });
      }
    };

    updateToken();
  }, [organization]);

  useEffect(() => {
    if (isLoaded && !organization && userMemberships?.data?.length) {
      setActive({
        organization: userMemberships.data[0].organization.id,
      });
    }
  }, [isLoaded, organization, userMemberships, setActive]);

  // Handle organization switching - update token and reload page when organization changes
  useEffect(() => {
    if (organization?.id && isLoaded) {
      // If this is not the first load and organization has changed, update token then reload
      if (
        previousOrganizationId.current !== null &&
        previousOrganizationId.current !== organization.id
      ) {
        const updateTokenAndReload = async () => {
          try {
            // Update token for new organization before reloading
            const token = await getToken({ template: TOKEN_TEMPLATE });
            if (token) {
              window.localStorage.setItem(TOKEN_STORAGE_KEY, token);
            }
          } catch (error) {
            logger.error(
              'Failed to update access token before reload',
              error as Error,
              {
                organizationId: organization?.id,
                previousOrganizationId: previousOrganizationId.current,
              }
            );
          } finally {
            // Reload page regardless of token update success/failure
            window.location.reload();
          }
        };

        updateTokenAndReload();
      }

      // Update the previous organization id
      previousOrganizationId.current = organization.id;
    }
  }, [organization?.id, isLoaded, getToken]);

  if (!isLoaded || userMemberships.isLoading) {
    return <LoadingSpinner />;
  }

  if (
    (!userMemberships.data || userMemberships.data.length === 0) &&
    !location.pathname.includes('/dashboard/profile')
  ) {
    return <Navigate to="/dashboard/profile?tab=information" replace />;
  }

  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/profile" element={<DashboardProfile />} />
        <Route path="/settings" element={<DashboardGeneralSettings />} />
        <Route path="/data-center" element={<DashboardDataCenter />} />
        <Route
          path="/internal-manager"
          element={<DashboardInternalManager />}
        />
        <Route path="/territories" element={<DashboardTerritories />} />
        <Route
          path="/stages-checklists"
          element={<DashboardStagesChecklists />}
        />
        <Route
          path="/financial-overview"
          element={<DashboardFinancialOverview />}
        />
        <Route path="/zee-profile/:id" element={<ZeeProfile />} />
        <Route
          path="/consultant-profile/:consultantId"
          element={<ConsultantProfile />}
        />
        <Route
          path="/email-templates/:id"
          element={<DashboardEmailTemplate />}
        />
      </Routes>
    </MainLayout>
  );
};

export default DashboardRoutes;
