import { ClerkLoaded, useAuth } from '@clerk/clerk-react';
import { Loader2 } from 'lucide-react';
import { Navigate, Route, Routes, useLocation } from 'react-router-dom';

import AuthLayout from '@/layouts/AuthLayout';
import AcceptOrgInvitation from '@/pages/auth/AcceptOrgInvitation';
import ConfirmEmail from '@/pages/auth/ConfirmEmail';
import ForgetPassword from '@/pages/auth/ForgetPassword';
import ResetPassword from '@/pages/auth/ResetPassword';
import SignIn from '@/pages/auth/SignIn';
import SignUp from '@/pages/auth/SignUp';
import SSOCallback from '@/pages/auth/SSOCallback';

export const LoadingSpinner = () => (
  <div className="flex h-screen w-screen items-center justify-center">
    <Loader2 className="size-4 animate-spin" />
  </div>
);

const AuthRoutes = () => {
  const location = useLocation();
  const { isLoaded, isSignedIn } = useAuth();

  if (!isLoaded) {
    return <LoadingSpinner />;
  }

  if (isSignedIn && location.pathname !== '/auth/confirm') {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <AuthLayout>
      <Routes>
        <Route path="/sign-in" element={<SignIn />} />
        <Route path="/sign-up" element={<SignUp />} />
        <Route
          path="/confirm"
          element={
            <ClerkLoaded>
              <ConfirmEmail />
            </ClerkLoaded>
          }
        />
        <Route path="/reset-password" element={<ResetPassword />} />
        <Route path="/forget-password" element={<ForgetPassword />} />
        <Route
          path="/accept-org-invitation"
          element={<AcceptOrgInvitation />}
        />
        <Route path="/sso-callback" element={<SSOCallback />} />
      </Routes>
    </AuthLayout>
  );
};

export default AuthRoutes;
