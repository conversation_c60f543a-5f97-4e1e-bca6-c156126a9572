import {
  <PERSON>Down,
  <PERSON>Up,
  DownloadCloud,
  Loader2,
  UserIcon,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import AssignUserDialog from '@/components/territories/territory-item/AssignUserDialog';
import { RemoveUserDialog } from '@/components/territories/territory-item/RemoveUserDialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import { US_STATES } from '@/constants/territories/states';
import { getNextStatusOptions } from '@/constants/territories/status-dropdown-options';
import apiManager from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { getTerritoryColor } from '@/lib/territories';
import logger from '@/services/logger.service';
import { TerritoryReportService } from '@/services/territoryReport.service';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { Territory } from '@/types/territories/territory.type';

const StatusTab = () => {
  const [loadingTerritories, setLoadingTerritories] = useState(true);
  const [generatingReport, setGeneratingReport] = useState(false);
  const [sortField, setSortField] = useState<keyof Territory | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [statusDropdownOpenId, setStatusDropdownOpenId] = useState<
    number | null
  >(null);
  const [openAssignDialogId, setOpenAssignDialogId] = useState<number | null>(
    null
  );
  const [loadingStatusId, setLoadingStatusId] = useState<number | null>(null);

  const [openRemoveDialogId, setOpenRemoveDialogId] = useState<number | null>(
    null
  );
  const [removingUserId, setRemovingUserId] = useState<number | null>(null);

  const { allTerritories, setAllTerritories, territorySettings } =
    useTerritoryStore();

  const handleRemoveUser = async (territory: Territory) => {
    setRemovingUserId(territory.id);

    const response = await apiManager.patch(
      `${TERRITORIES_API_URL}/${territory.id}`,
      { status: 'AVAILABLE', assigned_to: null },
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status === 200) {
      setAllTerritories(
        allTerritories?.map((t) =>
          t.id === territory.id
            ? { ...t, status: 'AVAILABLE', assigned_to: null }
            : t
        ) as Territory[]
      );
      toast({ title: 'User Removed' });
    } else {
      toast({ title: 'Failed to remove user', variant: 'destructive' });
    }
    setRemovingUserId(null);
    setOpenRemoveDialogId(null);
  };

  const handleSort = (
    field: keyof Territory,
    direction: 'asc' | 'desc',
    dataToSort?: Territory[]
  ) => {
    const data = dataToSort ?? allTerritories;
    if (!data) return;

    setSortField(field);
    setSortDirection(direction);

    const sorted = [...data].sort((a, b) => {
      const aValue = a[field];
      const bValue = b[field];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    setAllTerritories(sorted);
  };

  const handleStatusChange = async (
    territory: Territory,
    previousStatus: Territory['status'],
    newStatus: Territory['status']
  ) => {
    const isAvailable = newStatus !== 'AVAILABLE';

    // Determine assigned_to ID for API call
    const assigned_id = isAvailable
      ? previousStatus === 'PENDING'
        ? territory.requested_by?.id
        : previousStatus === 'RESERVED'
          ? territory.assigned_to?.id
          : null
      : null;
    setStatusDropdownOpenId(null);
    setLoadingStatusId(territory.id);
    const response = await apiManager.patch(
      `${TERRITORIES_API_URL}/${territory.id}`,
      { status: newStatus, assigned_to: assigned_id },
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status === 200) {
      const isAvailable = newStatus !== 'AVAILABLE';

      // Determine assigned_to ID for API call
      const assigned_user = isAvailable
        ? previousStatus === 'PENDING'
          ? territory.requested_by
          : previousStatus === 'RESERVED'
            ? territory.assigned_to
            : null
        : null;

      setAllTerritories(
        allTerritories?.map((t) =>
          t.id === territory.id
            ? { ...t, status: newStatus, assigned_to: assigned_user }
            : t
        ) as Territory[]
      );
      toast({
        title: 'Status Updated',
        description: `Territory is now ${newStatus}`,
      });
    } else {
      toast({
        title: 'Failed to update status',
        variant: 'destructive',
      });
    }
    setLoadingStatusId(null);
  };

  const fetchTerritories = useCallback(async () => {
    try {
      const response = await apiManager.get(TERRITORIES_API_URL, {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.status === 200) {
        setAllTerritories(response.data.data);
        setLoadingTerritories(false);
      }
    } catch (error) {
      logger.error(
        'Failed to fetch territories for status tab',
        error as Error
      );
    }
  }, [setAllTerritories]);

  const fetchDemographic = async (territoryId: number): Promise<Territory> => {
    const response = await apiManager.get(
      `${TERRITORIES_API_URL}/${territoryId}`,
      {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
      }
    );

    if (response.status !== 200) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
    return response.data.data;
  };

  const generateReport = async (territory: Territory) => {
    setGeneratingReport(true);
    try {
      const demographicData = await fetchDemographic(territory.id);
      await TerritoryReportService.generateReport(
        { ...territory, ...demographicData },
        territorySettings
      );
    } catch (error) {
      logger.error('Failed to generate territory report', error as Error, {
        territoryId: territory.id,
        territoryState: territory.state,
      });
    } finally {
      setGeneratingReport(false);
    }
  };

  const handleGenerateReportClick = (territory: Territory) => (): void => {
    generateReport(territory);
  };

  useEffect(() => {
    fetchTerritories();
  }, [fetchTerritories]);

  return (
    <div>
      <div className="mb-4 border-b border-border pb-2 font-medium text-zinc-700">
        Status
      </div>
      {loadingTerritories ? (
        <div className="flex w-full justify-center py-5">
          <Loader2 className="size-4 animate-spin" />
        </div>
      ) : (
        <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="as-title-04 w-[370px] px-4">
                  Zip Code
                </TableHead>
                <TableHead className="as-title-04">Territory Number</TableHead>
                <TableHead className="as-title-04">State</TableHead>
                <TableHead
                  className="as-title-04 cursor-pointer"
                  onClick={() =>
                    handleSort(
                      'status',
                      sortDirection == 'asc' ? 'desc' : 'asc'
                    )
                  }
                >
                  Status{' '}
                  {sortField === 'status' &&
                    (sortDirection === 'asc' ? (
                      <ArrowUp className="ml-1 inline-block h-4 w-4 text-muted-foreground" />
                    ) : (
                      <ArrowDown className="ml-1 inline-block h-4 w-4 text-muted-foreground" />
                    ))}
                </TableHead>
                <TableHead className="as-title-04">Assigned to</TableHead>
                <TableHead className="as-title-04">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white">
              {allTerritories && allTerritories.length > 0 ? (
                allTerritories.map((territory) => {
                  const assignedUser =
                    territory.status == 'PENDING'
                      ? territory.requested_by
                      : territory.assigned_to;

                  return (
                    <TableRow key={territory.id}>
                      <TableCell className="as-title-03 truncate px-4">
                        {territory.zcta_codes.join(', ')}
                      </TableCell>
                      <TableCell className="as-title-03">
                        {territory.id.toString().padStart(5, '0')}
                      </TableCell>
                      <TableCell className="as-title-03">
                        {US_STATES[territory.state as keyof typeof US_STATES]}
                      </TableCell>
                      <TableCell className="as-title-03">
                        <Popover
                          open={
                            statusDropdownOpenId === territory.id &&
                            getNextStatusOptions(territory.status).length > 0
                          }
                          onOpenChange={(open) =>
                            setStatusDropdownOpenId(open ? territory.id : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <div
                              className={`flex w-24 ${loadingStatusId === territory.id ? 'pointer-events-none' : ''}`}
                            >
                              <Badge
                                color={getTerritoryColor(
                                  territory.status,
                                  territorySettings
                                )}
                                className="w-full cursor-pointer justify-center"
                              >
                                {loadingStatusId === territory.id ? (
                                  <Loader2 className="size-4 animate-spin" />
                                ) : (
                                  territory.status
                                )}
                              </Badge>
                            </div>
                          </PopoverTrigger>
                          <PopoverContent className="w-28 space-y-1 p-2">
                            {getNextStatusOptions(territory.status).map(
                              (statusOption) => (
                                <Badge
                                  key={statusOption}
                                  color={getTerritoryColor(
                                    statusOption,
                                    territorySettings
                                  )}
                                  className="w-full cursor-pointer justify-center text-center"
                                  onClick={() =>
                                    handleStatusChange(
                                      territory,
                                      territory.status,
                                      statusOption
                                    )
                                  }
                                >
                                  {statusOption}
                                </Badge>
                              )
                            )}
                          </PopoverContent>
                        </Popover>
                      </TableCell>
                      <TableCell className="as-title-03">
                        {assignedUser && territory.status != 'AVAILABLE' ? (
                          <div className="flex items-center gap-2">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={assignedUser.photo_url} />
                              <AvatarFallback>
                                <UserIcon className="h-3 w-3" />
                              </AvatarFallback>
                            </Avatar>
                            <p className="as-title-03 text-nowrap text-gray-700">
                              {assignedUser.full_name}
                            </p>
                            <RemoveUserDialog
                              isOpen={openRemoveDialogId === territory.id}
                              onOpenChange={(open) =>
                                setOpenRemoveDialogId(
                                  open ? territory.id : null
                                )
                              }
                              onConfirm={() => handleRemoveUser(territory)}
                              loading={removingUserId === territory.id}
                            />
                          </div>
                        ) : (
                          <>
                            <Button
                              variant="ghost"
                              className="px-0 text-primary hover:bg-transparent"
                              onClick={() =>
                                setOpenAssignDialogId(territory.id)
                              }
                            >
                              + Assign
                            </Button>

                            <AssignUserDialog
                              open={openAssignDialogId === territory.id}
                              territory={territory}
                              onCloseDialog={() => setOpenAssignDialogId(null)}
                            />
                          </>
                        )}
                      </TableCell>
                      <TableCell className="as-title-03">
                        <Button
                          variant="outline"
                          onClick={handleGenerateReportClick(territory)}
                          disabled={generatingReport}
                        >
                          <DownloadCloud className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center">
                    No data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default StatusTab;
