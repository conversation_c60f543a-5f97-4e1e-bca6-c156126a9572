import { Maximize2, Minimize2, Search } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import Map from '@/components/territories/map/Map';
import TerritoriesByState from '@/components/territories/TerritoriesByState';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import apiManager from '@/helpers/api.manager';
import { useGetTerritorySettings } from '@/hooks/territories/territories-settings/useGetTerritorySettings';
import { toast } from '@/hooks/ui/use-toast';
import { getFirstMatchingStateCode } from '@/lib/territories';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';

const useDebounce = <T,>(value: T, delay: number = 500): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};

const TerritoriesTab = () => {
  const { setTerritories } = useTerritoryStore();
  const [isHeatMap, setIsHeatMap] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [loadingTerritories, setLoadingTerritories] = useState(true);
  const { setTerritorySettings } = useTerritoryStore();
  const {
    result: territorySettingsResult,
    isSuccess: isTerritorySettingsSuccess,
  } = useGetTerritorySettings();

  useEffect(() => {
    if (isTerritorySettingsSuccess && territorySettingsResult?.data) {
      setTerritorySettings(territorySettingsResult.data);
    }
  }, [
    isTerritorySettingsSuccess,
    territorySettingsResult?.data,
    setTerritorySettings,
  ]);
  const debouncedSearchValue = useDebounce(searchValue);

  const fetchTerritories = useCallback(async () => {
    setLoadingTerritories(true);
    const searchParam = getFirstMatchingStateCode(debouncedSearchValue);
    const response = await apiManager.get(TERRITORIES_API_URL, {
      baseURL: configuration.TERRITORIES_API_URL,
      headers: { 'Content-Type': 'application/json' },
      params: {
        ...(searchParam && {
          search: searchParam,
        }),
      },
    });
    if (response.status !== 200) {
      toast({
        title: 'Error loading territories',
        variant: 'destructive',
      });
      return;
    }
    setTerritories(response.data.data);
    setLoadingTerritories(false);
  }, [debouncedSearchValue, setTerritories]);

  useEffect(() => {
    fetchTerritories();
  }, [fetchTerritories]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const toggleHeatMap = () => {
    setIsHeatMap((prev) => !prev);
  };

  const toggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  return (
    <div className="space-y-6">
      <div
        className={cn(
          'relative rounded-lg transition-all duration-300',
          isExpanded ? 'h-[70vh]' : 'h-80'
        )}
      >
        <Map
          zoom={4}
          center={{ lat: 39.8283, lng: -98.5795 }}
          isHeatmap={isHeatMap}
        />
        <div className="absolute left-2 top-2 flex items-center gap-2 rounded-md bg-white p-3 shadow-md">
          <Switch
            checked={isHeatMap}
            className="toggle toggle-success"
            onCheckedChange={toggleHeatMap}
          />
          <span className="text-gray-700">HEATMAP</span>
        </div>
        <Button className="absolute right-2 top-2" onClick={toggleExpand}>
          {isExpanded ? <Minimize2 /> : <Maximize2 />}
          {isExpanded ? 'Minimize' : 'Expand'}
        </Button>
      </div>
      <div className="flex items-center justify-between">
        <div className="relative flex w-full items-center">
          <Search size={16} className="absolute left-3 text-gray-500" />
          <Input
            type="text"
            value={searchValue}
            className="pl-8"
            placeholder="Search by state or zip"
            onChange={handleSearchChange}
          />
        </div>
      </div>
      <div className="space-y-1.5">
        <TerritoriesByState loadingTerritories={loadingTerritories} />
      </div>
    </div>
  );
};

export default TerritoriesTab;
