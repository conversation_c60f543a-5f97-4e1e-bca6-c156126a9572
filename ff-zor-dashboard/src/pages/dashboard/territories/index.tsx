import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import ComplianceTab from './ComplianceTab';
import EditPolygonsTab from './EditPolygonsTab';
import StatusTab from './StatusTab';
import TerritoriesTab from './TerritoriesTab';
import UsaCensusDataTab from './UsaCensusDataTab';

type TabValue =
  | 'territories'
  | 'status'
  | 'compliance'
  | 'usa-census-data'
  | 'edit-polygons';

const DashboardTerritories = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedTab, setSelectedTab] = useState<TabValue>('territories');

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const newDefaultTab = (queryParams.get('tab') as TabValue) || 'territories';
    setSelectedTab(newDefaultTab);
  }, [location]);

  const handleTabChange = (value: TabValue) => {
    setSelectedTab(value);
    navigate(`/dashboard/territories?tab=${value}`);
  };

  return (
    <div className="space-y-4">
      <div className="flex w-full flex-col justify-between gap-4 md:flex-row">
        <div className="max-w-md">
          <h2 className="as-strong">Territories</h2>
          <p className="as-title-02 mt-2 font-light text-typo-gray-tertiary">
            Information about territories set by you!
          </p>
        </div>
      </div>
      <Tabs
        value={selectedTab}
        onValueChange={(value) => handleTabChange(value as TabValue)}
      >
        <TabsList className="mb-4 w-full md:w-fit">
          <TabsTrigger value="territories" className="w-full">
            Territories
          </TabsTrigger>
          <TabsTrigger value="status" className="w-full">
            Status
          </TabsTrigger>
          <TabsTrigger value="compliance" className="w-full">
            Compliance
          </TabsTrigger>
          <TabsTrigger value="usa-census-data" className="w-full">
            USA Census Data
          </TabsTrigger>
          <TabsTrigger value="edit-polygons" className="w-full">
            Edit Polygons
          </TabsTrigger>
        </TabsList>
        <TabsContent value="territories">
          <TerritoriesTab />
        </TabsContent>
        <TabsContent
          value="status"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <StatusTab />
        </TabsContent>
        <TabsContent value="compliance">
          <ComplianceTab />
        </TabsContent>
        <TabsContent value="usa-census-data">
          <UsaCensusDataTab />
        </TabsContent>
        <TabsContent value="edit-polygons">
          <EditPolygonsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardTerritories;
