import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import StateComplianceCard from '@/components/territories/StateComplianceCard';
import {
  FILLING_STATES,
  NON_REGISTRATION_STATES,
  REGISTRATION_STATES,
} from '@/constants/territories/states';
import { useGetStatesCompliance } from '@/hooks/territories/states-compliance/useGetStatesCompliance';
import { toast } from '@/hooks/ui/use-toast';
import useTerritoryStore from '@/stores/useTerritoryStore';

interface ComplianceSectionProps {
  title: string;
  description: string;
  states: string[];
  statesCompliance: Record<string, { active: boolean; compliant: boolean }>;
  isLoading: boolean;
}

const ComplianceSection = ({
  title,
  description,
  states,
  statesCompliance,
  isLoading,
}: ComplianceSectionProps) => (
  <div className="rounded-xl border border-border bg-muted">
    <div className="flex justify-between border-b border-border px-6 py-3.5">
      <div className="max-w-3xl space-y-1">
        <h2 className="as-title-01 as-strong text-gray-700">{title}</h2>
        <p className="as-body-03 text-muted-foreground">{description}</p>
      </div>
    </div>
    {isLoading ? (
      <div className="flex h-full items-center justify-center py-10">
        <Loader2 className="size-4 animate-spin" />
      </div>
    ) : (
      <div className="grid grid-cols-[repeat(auto-fill,minmax(240px,1fr))] gap-5 p-4">
        {states.map((state) => (
          <StateComplianceCard
            key={state}
            state={state}
            status={statesCompliance[state]}
          />
        ))}
      </div>
    )}
  </div>
);

const ComplianceTab = () => {
  const { statesCompliance, setStatesCompliance } = useTerritoryStore();
  const { result, isLoading, isSuccess, isRefetching, isError } =
    useGetStatesCompliance();

  useEffect(() => {
    if (isError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isError]);

  useEffect(() => {
    if (isSuccess && result?.data) {
      const newCompliance = result.data.reduce<
        Record<string, { active: boolean; compliant: boolean }>
      >((acc, curr) => {
        acc[curr.state] = {
          active: curr.is_active,
          compliant: curr.is_compliant,
        };
        return acc;
      }, {});
      setStatesCompliance({ ...statesCompliance, ...newCompliance });
    }
  }, [result?.data, isSuccess, setStatesCompliance]);

  return (
    <div className="space-y-6">
      {isRefetching && (
        <div className="flex items-center gap-2 text-sm text-zinc-500">
          <Loader2 className="size-4 animate-spin" />
          Refreshing compliance data...
        </div>
      )}
      <ComplianceSection
        title="Non-Registration States"
        description="These states do not require franchise registration or filing but still enforce the Federal Trade Commission (FTC):"
        states={NON_REGISTRATION_STATES}
        statesCompliance={statesCompliance}
        isLoading={isLoading}
      />
      <ComplianceSection
        title="Franchisee Filling States"
        description="These states require filing of the FDD or a notice but do not require formal registration:"
        states={FILLING_STATES}
        statesCompliance={statesCompliance}
        isLoading={isLoading}
      />
      <ComplianceSection
        title="Franchisee Registration States"
        description="These states require the franchisor to register the Franchise Disclosure Document (FDD) before offering or selling franchises:"
        states={REGISTRATION_STATES}
        statesCompliance={statesCompliance}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ComplianceTab;
