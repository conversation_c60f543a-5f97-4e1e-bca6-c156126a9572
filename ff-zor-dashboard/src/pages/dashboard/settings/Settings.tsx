import { PenBoxIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import AimeiPromptsForm from '@/components/forms/general-settings/AimeiPromptsForm';
import DnsSettingsForm from '@/components/forms/general-settings/DnsSettingsForm';
import EmailSettingsForm from '@/components/forms/general-settings/EmailSettingsForm';
import GeneralSettingsForm from '@/components/forms/general-settings/GeneralSettingsForm';
import StagePromptsForm from '@/components/forms/general-settings/StagePromptsForm';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const DashboardGeneralSettings = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const [isEdit, setIsEdit] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<string>('general');

  const handleEdit = () => {
    setIsEdit(selectedTab || null);
  };

  const handelClickTab = (value: string) => {
    setIsEdit(null);
    setSelectedTab(value);

    navigate(`/dashboard/settings?tab=${value}`);
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const newDefaultTab = queryParams.get('tab') || 'general';
    setSelectedTab(newDefaultTab);
  }, [location]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-center md:flex-row md:justify-between">
        <div className="flex flex-col items-center gap-5 md:flex-row">
          <div className="space-y-1">
            <h2 className="as-strong">General Settings</h2>
          </div>
        </div>
        <div className="flex items-center py-2 md:py-0">
          {!isEdit &&
            selectedTab != 'integrations' &&
            selectedTab != 'email-settings' &&
            selectedTab != 'dns-settings' && (
              <Button
                size="sm"
                variant="outline"
                className="w-full md:w-fit"
                onClick={handleEdit}
              >
                <PenBoxIcon /> Edit
              </Button>
            )}
        </div>
      </div>
      <Tabs
        value={selectedTab}
        onValueChange={(value) => handelClickTab(value)}
      >
        <TabsList className="mb-4 w-full md:w-fit">
          <TabsTrigger value="general" className="w-full">
            General
          </TabsTrigger>
          <TabsTrigger value="stage-prompts" className="w-full">
            Stage Actions
          </TabsTrigger>
          <TabsTrigger value="aimei-prompts" className="w-full">
            Aimei Prompts
          </TabsTrigger>
          <TabsTrigger value="email-settings" className="w-full">
            Email Settings
          </TabsTrigger>
          <TabsTrigger value="dns-settings" className="w-full">
            DNS Settings
          </TabsTrigger>
          {/* <TabsTrigger value="integrations" className="w-full">
            Integrations
          </TabsTrigger> */}
        </TabsList>
        <TabsContent
          value="general"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <GeneralSettingsForm isEdit={isEdit} setIsEdit={setIsEdit} />
        </TabsContent>
        <TabsContent
          value="stage-prompts"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <StagePromptsForm isEdit={isEdit} setIsEdit={setIsEdit} />
        </TabsContent>
        <TabsContent
          value="aimei-prompts"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <AimeiPromptsForm isEdit={isEdit} setIsEdit={setIsEdit} />
        </TabsContent>
        <TabsContent
          value="email-settings"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <EmailSettingsForm />
        </TabsContent>
        <TabsContent
          value="dns-settings"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <DnsSettingsForm />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardGeneralSettings;
