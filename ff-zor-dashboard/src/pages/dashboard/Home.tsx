import { CalendarDays } from 'lucide-react';
import { useState } from 'react';

import CandidateMetricsChart from '@/components/home/<USER>';
import ConsultantPerformanceTable from '@/components/home/<USER>';
import DiscoveryFunnel<PERSON>hart from '@/components/home/<USER>';
import GeographicChart from '@/components/home/<USER>';
import KPICards from '@/components/home/<USER>';
import TopCandidatesTable from '@/components/home/<USER>';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function FranchiseDashboard() {
  const [timeRange, setTimeRange] = useState('30');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex w-full flex-col justify-between gap-4 md:flex-row">
        <div className="">
          <h2 className="as-strong">Franchise Discovery Dashboard</h2>
          <p className="as-title-02 mt-2 font-light text-typo-gray-tertiary">
            Monitor candidate activity, funnel performance, and consultant
            contribution
          </p>
        </div>
        <div className="flex items-center">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-48">
              <CalendarDays className="mr-2 h-4 w-4" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 Days</SelectItem>
              <SelectItem value="30">Last 30 Days</SelectItem>
              <SelectItem value="60">Last 60 Days</SelectItem>
              <SelectItem value="90">Last 90 Days</SelectItem>
              <SelectItem value="all">Full Timeline</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <KPICards />

      {/* Main Charts Row */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <CandidateMetricsChart />
        <DiscoveryFunnelChart />
      </div>

      {/* Tables Row */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <TopCandidatesTable />
        <ConsultantPerformanceTable />
      </div>

      <GeographicChart />
    </div>
  );
}
