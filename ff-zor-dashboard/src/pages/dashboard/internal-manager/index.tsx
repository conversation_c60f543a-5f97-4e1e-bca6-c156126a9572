import { Plus, Upload } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import ImportFranchiseesDialog from '@/components/dialogs/ImportFranchiseesDialog';
import UploadDocumentDialog from '@/components/dialogs/UploadDocumentDialog';
import MeetingLinkComponent from '@/components/global/MeetingLinkComponent';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import DocumentsTab from './DocumentsTab';
import FranchiseesTab from './FranchiseesTab';
import LeadershipTab from './LeadershipTab';

const DashboardInternalManager = () => {
  const [uploadOpen, setUploadOpen] = useState<boolean>(false);
  const [importOpen, setImportOpen] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState<string>();

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const newDefaultTab = queryParams.get('tab') || 'documents';
    setSelectedTab(newDefaultTab);
  }, [location]);

  const handelClickTab = (value: string) => {
    setSelectedTab(value);

    navigate(`/dashboard/internal-manager?tab=${value}`);
  };

  const handleUploadClick = (): void => {
    setUploadOpen(true);
  };

  const handleImportClick = (): void => {
    setImportOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-center md:flex-row md:justify-between">
        <div className="flex flex-col items-center gap-5 md:flex-row">
          <div className="space-y-1">
            <h2 className="as-strong">Upload Document</h2>
            <p className="as-body-02 as-strong text-typo-gray-tertiary">
              Documents and attachments that have been uploaded by you.
            </p>
          </div>
        </div>
        <UploadDocumentDialog open={uploadOpen} setOpen={setUploadOpen} />
        <ImportFranchiseesDialog open={importOpen} setOpen={setImportOpen} />
        {selectedTab === 'documents' && (
          <Button className="bg-brand-500 p-5" onClick={handleUploadClick}>
            <Upload />
            Upload Document
          </Button>
        )}
        {selectedTab === 'franchisees' && (
          <Button className="bg-brand-500 p-5" onClick={handleImportClick}>
            <Plus />
            Import from CSV
          </Button>
        )}
      </div>

      <Tabs
        value={selectedTab}
        onValueChange={handelClickTab}
        defaultValue="documents"
      >
        <div className="mb-4 flex items-center justify-between">
          <TabsList className="w-full md:w-fit">
            <TabsTrigger value="documents" className="w-full">
              Documents
            </TabsTrigger>
            <TabsTrigger value="franchisees" className="w-full">
              Franchisees
            </TabsTrigger>
            <TabsTrigger value="leadership-team" className="w-full">
              Leadership Team
            </TabsTrigger>
          </TabsList>
          <MeetingLinkComponent />
        </div>
        <TabsContent
          value="documents"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <DocumentsTab />
        </TabsContent>
        <TabsContent
          value="franchisees"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <FranchiseesTab />
        </TabsContent>
        <TabsContent
          value="leadership-team"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <LeadershipTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardInternalManager;
