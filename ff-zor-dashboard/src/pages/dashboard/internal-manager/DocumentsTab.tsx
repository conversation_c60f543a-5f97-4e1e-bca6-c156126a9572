import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import DeleteDialog from '@/components/dialogs/DeleteDialog';
import DocumentDetailIcon from '@/components/global/DocumentDetailIcon';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useGetFdd } from '@/hooks/fdd/useGetFdd';
import { toast } from '@/hooks/ui/use-toast';
import { useDeleteZorDocument } from '@/hooks/zor-document/useDeleteZorDocument';
import { useGetAllZorDocuments } from '@/hooks/zor-document/useGetAllZorDocuments';
import { useUpdateZorDocument } from '@/hooks/zor-document/useUpdateZorDocument';
import useFddStore from '@/stores/useFddStore';
import useZorDocumentStore from '@/stores/useZorDocumentStore';

const DocumentTab = () => {
  const { zorDocuments, setZorDocuments } = useZorDocumentStore();
  const { fdd, setFdd } = useFddStore();

  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [selectedKey, setSelectedKey] = useState<string | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const {
    result: zorDocumentsResult,
    isError,
    isSuccess,
    isLoading,
    isRefetching,
  } = useGetAllZorDocuments();

  const {
    result: fddResult,
    isError: isFddError,
    isSuccess: isFddSuccess,
    isLoading: isFddLoading,
    isRefetching: isFddRefetching,
  } = useGetFdd();

  const {
    mutate: deleteZorDocument,
    isError: isDeleteError,
    isSuccess: isDeleteSuccess,
    isLoading: isDeleteLoading,
  } = useDeleteZorDocument();

  const {
    mutate: updateZorDocument,
    result: updateZorDocumentResult,
    isError: isUpdateError,
    isSuccess: isUpdateSuccess,
    isLoading: isUpdateLoading,
  } = useUpdateZorDocument();

  const handleDelete = (id: string) => {
    setSelectedId(id);
    setOpenDeleteDialog(true);
  };

  const handleToggle = (id: string, key: string, value: boolean) => {
    setSelectedId(id);
    setSelectedKey(key);
    updateZorDocument({
      id,
      payload: { [key]: value },
    });
  };

  const handleDeleteConfirm = () => {
    if (selectedId && zorDocuments) {
      deleteZorDocument(selectedId);
    }
  };

  useEffect(() => {
    if (isError || isDeleteError || isUpdateError || isFddError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isError, isDeleteError, isUpdateError, isFddError]);

  useEffect(() => {
    if (isSuccess && zorDocumentsResult?.data) {
      setZorDocuments(zorDocumentsResult.data);
    }
  }, [isSuccess, zorDocumentsResult, setZorDocuments]);

  useEffect(() => {
    if (isFddSuccess && fddResult?.data) {
      setFdd(fddResult.data);
    }
  }, [isFddSuccess, fddResult, setFdd]);

  useEffect(() => {
    if (isDeleteSuccess) {
      setZorDocuments(
        (zorDocuments ?? []).filter((item) => item.id !== selectedId)
      );
      setSelectedId(null);
      setOpenDeleteDialog(false);
    }
  }, [isDeleteSuccess, setZorDocuments]);

  useEffect(() => {
    if (isUpdateSuccess && updateZorDocumentResult?.data) {
      setZorDocuments(
        zorDocuments?.map((item) =>
          item.id === selectedId ? updateZorDocumentResult?.data : item
        ) ?? []
      );
      setSelectedId(null);
      setSelectedKey(null);
    }
  }, [isUpdateSuccess, updateZorDocumentResult, setZorDocuments]);

  if (isLoading || isFddLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* FDD Section */}
      <div>
        <div className="mb-4 flex items-center gap-2 border-b border-border pb-2 font-medium text-zinc-700">
          <FileText className="size-4" />
          Franchise Disclosure Document (FDD)
          {isFddRefetching && <Loader2 className="size-4 animate-spin" />}
        </div>

        {fdd ? (
          <div className="border-primary/20 from-primary/5 to-primary/10 overflow-hidden rounded-xl border bg-gradient-to-br">
            {/* Document Header */}
            <div className="bg-white/80 p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <DocumentDetailIcon
                    url={fdd.url}
                    name={fdd.name}
                    size={fdd.size}
                  />
                </div>
                <div className="text-sm text-zinc-500">
                  Document ID: {fdd.file_id}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="rounded-xl border border-dashed border-zinc-300 bg-zinc-50 p-8 text-center">
            <FileText className="mx-auto mb-2 size-8 text-zinc-400" />
            <div className="text-sm text-zinc-600">
              No FDD document available
            </div>
          </div>
        )}
      </div>

      {/* Regular Documents Section */}
      <div>
        <div className="mb-4 flex items-center gap-2 border-b border-border pb-2 font-medium text-zinc-700">
          Uploaded Documents
          {isRefetching && <Loader2 className="size-4 animate-spin" />}
        </div>
        <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="as-title-04 w-[40%] px-4">
                  Document name
                </TableHead>
                <TableHead className="as-title-04 w-[15%]">Stage</TableHead>
                <TableHead className="as-title-04 w-[15%]">
                  Franchisee
                </TableHead>
                <TableHead className="as-title-04 w-[15%]">
                  Consultant
                </TableHead>
                <TableHead className="as-title-04 w-[15%] text-right">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white">
              {zorDocuments?.length ? (
                zorDocuments.map((document) => (
                  <TableRow key={document.id}>
                    <TableCell className="px-4">
                      <DocumentDetailIcon
                        url={document.url}
                        name={document.name}
                        size={document.size}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={document.for_stage}
                          onCheckedChange={(checked) =>
                            handleToggle(document.id, 'for_stage', checked)
                          }
                        />
                        {document.id === selectedId &&
                          selectedKey === 'for_stage' &&
                          isUpdateLoading && (
                            <Loader2 className="size-4 animate-spin" />
                          )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={document.for_franchisee}
                          onCheckedChange={(checked) =>
                            handleToggle(document.id, 'for_franchisee', checked)
                          }
                        />
                        {document.id === selectedId &&
                          selectedKey === 'for_franchisee' &&
                          isUpdateLoading && (
                            <Loader2 className="size-4 animate-spin" />
                          )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={document.for_consultant}
                          onCheckedChange={(checked) =>
                            handleToggle(document.id, 'for_consultant', checked)
                          }
                        />
                        {document.id === selectedId &&
                          selectedKey === 'for_consultant' &&
                          isUpdateLoading && (
                            <Loader2 className="size-4 animate-spin" />
                          )}
                      </div>
                    </TableCell>
                    <TableCell className="px-4">
                      <div
                        className="group float-right w-fit cursor-pointer rounded-md bg-red-50 p-2.5 hover:bg-red-500"
                        onClick={() => handleDelete(document.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500 group-hover:text-white" />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">
                    No data found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          <DeleteDialog
            open={openDeleteDialog}
            label1={zorDocuments?.find((item) => item.id === selectedId)?.name}
            label2="from your discovery stage?"
            isLoading={isDeleteLoading}
            setOpen={setOpenDeleteDialog}
            onDelete={handleDeleteConfirm}
            onCancel={() => setOpenDeleteDialog(false)}
          />
        </div>
      </div>
    </div>
  );
};

export default DocumentTab;
