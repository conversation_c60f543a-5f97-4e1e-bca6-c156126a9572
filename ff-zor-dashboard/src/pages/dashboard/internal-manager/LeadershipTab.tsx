import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import AddLeadershipUser from '@/components/dialogs/AddLeadershipUserDialog';
import DeleteDialog from '@/components/dialogs/DeleteDialog';
import EditLeadershipUserForm from '@/components/forms/profile/EditLeaderShipUserForm';
import LeadershipTeamTable from '@/components/tables/LeadershipTeamTable';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/hooks/ui/use-toast';
import { useDeleteZorMember } from '@/hooks/zor-member/useDeleteZorMember';
import { useGetAllZorMembers } from '@/hooks/zor-member/useGetAllZorMembers';
import { useUpdateAddedZorMember } from '@/hooks/zor-member/useUpdateAddedZorMember';
import logger from '@/services/logger.service';
import useZorMemberStore from '@/stores/useZorMemberStore';
import { ZorMember } from '@/types/zor.member.type';

const LeadershipTab = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<ZorMember | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [memberToDelete, setMemberToDelete] = useState<ZorMember | null>(null);
  const [updatingMemberId, setUpdatingMemberId] = useState<string | null>(null);

  const { zorMembers: zorMembersFromStore, setZorMembers } =
    useZorMemberStore();

  const {
    result: zorMembers,
    isError: isZorMembersError,
    isSuccess: isZorMembersSuccess,
    isLoading: isZorMembersLoading,
    isRefetching: isZorMembersRefetching,
    refetch: refetchZorMembers,
  } = useGetAllZorMembers();

  const {
    mutate: deleteZorMember,
    isLoading: isDeleting,
    isError: isDeleteError,
    isSuccess: isDeleteSuccess,
  } = useDeleteZorMember();

  const {
    mutate: updateZorMember,
    isLoading: isUpdating,
    isError: isUpdateError,
    isSuccess: isUpdateSuccess,
  } = useUpdateAddedZorMember();

  const cancelEdit = () => {
    setIsEdit(false);
    setSelectedUser(null);
  };

  const handleDelete = (member: ZorMember) => {
    setMemberToDelete(member);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (memberToDelete?.id) {
      try {
        await deleteZorMember(memberToDelete.id);
      } catch (error) {
        logger.error('Failed to delete ZOR member', error as Error, {
          memberId: memberToDelete.id,
          memberName: memberToDelete.full_name,
        });
      }
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setMemberToDelete(null);
  };

  const handleTogglePublish = async (member: ZorMember, isPublic: boolean) => {
    setUpdatingMemberId(member.id);
    try {
      await updateZorMember({
        id: member.id,
        full_name: member.full_name,
        photo_url: member.photo_url,
        position: member.position,
        city: member.city,
        phone: member.phone,
        email: member.email,
        meeting_link: member.meeting_link,
        linkedin: member.linkedin,
        preferred_contact: member.preferred_contact,
        is_public: isPublic,
        is_added: member.is_added,
      });
    } catch (error) {
      logger.error(
        'Failed to update ZOR member publish status',
        error as Error,
        { memberId: member.id, memberName: member.full_name, isPublic }
      );
    }
  };

  useEffect(() => {
    if (isZorMembersError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isZorMembersError]);

  useEffect(() => {
    if (isDeleteError) {
      toast({
        title: 'Error deleting member',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isDeleteError]);

  useEffect(() => {
    if (isUpdateError) {
      toast({
        title: 'Error updating member',
        description: 'Please try again later',
        variant: 'destructive',
      });
      setUpdatingMemberId(null);
    }
  }, [isUpdateError]);

  useEffect(() => {
    if (isDeleteSuccess) {
      toast({
        title: 'Member deleted successfully',
        variant: 'default',
      });
      setDeleteDialogOpen(false);
      setMemberToDelete(null);
      refetchZorMembers();
    }
  }, [isDeleteSuccess, refetchZorMembers]);

  useEffect(() => {
    if (isUpdateSuccess) {
      toast({
        title: 'Member updated successfully',
        variant: 'default',
      });
      setUpdatingMemberId(null);
      refetchZorMembers();
    }
  }, [isUpdateSuccess, refetchZorMembers]);

  useEffect(() => {
    if (isZorMembersSuccess && zorMembers?.data) {
      setZorMembers(zorMembers?.data || []);
    }
  }, [zorMembers, isZorMembersSuccess, setZorMembers]);

  return (
    <div>
      <div className="mb-4 flex justify-between border-b border-border pb-2">
        {isEdit && selectedUser ? (
          <div className="flex items-center gap-2 font-medium text-zinc-700">
            <ArrowLeft
              size={16}
              className="cursor-pointer"
              onClick={cancelEdit}
            />
            Leadership team
          </div>
        ) : (
          <div className="flex items-center gap-2 font-medium text-zinc-700">
            Leadership
            {(isZorMembersRefetching || isDeleting) && (
              <Loader2 className="size-4 animate-spin" />
            )}
          </div>
        )}
        {!isEdit && (
          <Button onClick={() => setIsOpen(true)}>
            <Plus />
            Add user
          </Button>
        )}
      </div>

      {isEdit && selectedUser ? (
        <EditLeadershipUserForm data={selectedUser} setIsEdit={setIsEdit} />
      ) : (
        <div className="rounded-xl bg-white p-4">
          <Tabs defaultValue="leadership-all">
            <div className="flex items-center justify-between">
              <TabsList className="mb-2 w-full md:w-fit">
                <TabsTrigger value="leadership-all" className="w-full">
                  All
                </TabsTrigger>
                <TabsTrigger value="leadership-user" className="w-full">
                  User
                </TabsTrigger>
                <TabsTrigger value="leadership-added" className="w-full">
                  Added
                </TabsTrigger>
              </TabsList>
            </div>
            {isZorMembersLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="size-4 animate-spin" />
              </div>
            ) : (
              <>
                <TabsContent
                  value="leadership-all"
                  className="rounded-xl border border-alpha-dark-5 bg-white p-4"
                >
                  <LeadershipTeamTable
                    data={zorMembersFromStore || []}
                    isUpdating={isUpdating}
                    updatingMemberId={updatingMemberId}
                    onEdit={(item) => {
                      setIsEdit(true);
                      setSelectedUser(item);
                    }}
                    onDelete={handleDelete}
                    onTogglePublish={handleTogglePublish}
                  />
                </TabsContent>
                <TabsContent
                  value="leadership-user"
                  className="rounded-xl border border-alpha-dark-5 bg-white p-4"
                >
                  <LeadershipTeamTable
                    data={(zorMembersFromStore || []).filter(
                      (member) => !member.is_added
                    )}
                    isUpdating={isUpdating}
                    updatingMemberId={updatingMemberId}
                    onTogglePublish={handleTogglePublish}
                  />
                </TabsContent>
                <TabsContent
                  value="leadership-added"
                  className="rounded-xl border border-alpha-dark-5 bg-white p-4"
                >
                  <LeadershipTeamTable
                    data={(zorMembersFromStore || []).filter(
                      (member) => member.is_added
                    )}
                    isUpdating={isUpdating}
                    updatingMemberId={updatingMemberId}
                    onEdit={(item) => {
                      setIsEdit(true);
                      setSelectedUser(item);
                    }}
                    onDelete={handleDelete}
                    onTogglePublish={handleTogglePublish}
                  />
                </TabsContent>
              </>
            )}
          </Tabs>
        </div>
      )}

      {/* Add Dialog */}
      {isOpen && <AddLeadershipUser open={isOpen} setOpen={setIsOpen} />}

      {/* Delete Dialog */}
      <DeleteDialog
        open={deleteDialogOpen}
        label1={memberToDelete?.full_name}
        label2="leadership member"
        isLoading={isDeleting}
        setOpen={setDeleteDialogOpen}
        onDelete={confirmDelete}
        onCancel={cancelDelete}
      />
    </div>
  );
};

export default LeadershipTab;
