import { Edit, Loader2, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import AddFranchiseeDialog from '@/components/dialogs/AddFranchiseeDialog';
import DeleteDialog from '@/components/dialogs/DeleteDialog';
import EditFranchiseeDialog from '@/components/dialogs/EditFranchiseeDialog';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDeleteFranchisee } from '@/hooks/franchisees/useDeleteFranchisee';
import { useGetAllFranchisees } from '@/hooks/franchisees/useGetAllFranchisees';
import { toast } from '@/hooks/ui/use-toast';
import logger from '@/services/logger.service';
import useFranchiseeStore from '@/stores/useFranchiseeStore';
import { Franchisee } from '@/types/franchisee.type';

const FranchiseesTab = () => {
  const { franchisees, setFranchisees } = useFranchiseeStore();

  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedFranchisee, setSelectedFranchisee] =
    useState<Franchisee | null>(null);

  const {
    result: franchiseesFromApi,
    isError: isFranchiseesError,
    isSuccess: isFranchiseesSuccess,
    isLoading: isFranchiseesLoading,
    isRefetching: isFranchiseesRefetching,
  } = useGetAllFranchisees();

  const { mutate: deleteFranchisee, isPending: isDeleting } =
    useDeleteFranchisee();

  useEffect(() => {
    if (isFranchiseesError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isFranchiseesError]);

  useEffect(() => {
    if (isFranchiseesSuccess && franchiseesFromApi?.data) {
      setFranchisees(franchiseesFromApi.data);
    }
  }, [franchiseesFromApi, isFranchiseesSuccess, setFranchisees]);

  const handleAddNew = () => {
    setAddDialogOpen(true);
  };

  const handleEdit = (franchisee: Franchisee) => {
    setSelectedFranchisee(franchisee);
    setEditDialogOpen(true);
  };

  const handleEditClick = (franchisee: Franchisee) => () => {
    handleEdit(franchisee);
  };

  const handleDelete = (franchisee: Franchisee) => {
    setSelectedFranchisee(franchisee);
    setDeleteDialogOpen(true);
  };

  const handleDeleteClick = (franchisee: Franchisee) => () => {
    handleDelete(franchisee);
  };

  const confirmDelete = () => {
    if (selectedFranchisee?.id) {
      deleteFranchisee(selectedFranchisee.id, {
        onSuccess: () => {
          toast({
            title: 'Success',
            description: 'Franchisee deleted successfully',
          });
          setDeleteDialogOpen(false);
          setSelectedFranchisee(null);
        },
        onError: (error) => {
          logger.error('Failed to delete franchisee', error as Error, {
            franchiseeId: selectedFranchisee?.id,
          });

          toast({
            title: 'Error',
            description: error?.message || 'Failed to delete franchisee',
            variant: 'destructive',
          });
        },
      });
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setSelectedFranchisee(null);
  };

  const renderTableContent = () => {
    if (isFranchiseesLoading) {
      return (
        <TableRow>
          <TableCell colSpan={5}>
            <div className="flex items-center justify-center">
              <Loader2 className="size-4 animate-spin" />
            </div>
          </TableCell>
        </TableRow>
      );
    }

    if (!franchisees?.length) {
      return (
        <TableRow>
          <TableCell colSpan={5} className="text-center">
            No franchisees found
          </TableCell>
        </TableRow>
      );
    }

    return franchisees.map((franchisee) => (
      <TableRow key={franchisee.id}>
        <TableCell className="px-4">{franchisee.name}</TableCell>
        <TableCell>{franchisee.email}</TableCell>
        <TableCell>{franchisee.phone}</TableCell>
        <TableCell>{franchisee.location}</TableCell>
        <TableCell className="flex justify-end gap-2 px-4">
          <Button
            size="icon"
            variant="outline"
            onClick={handleEditClick(franchisee)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="icon"
            variant="destructive"
            onClick={handleDeleteClick(franchisee)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <div>
      <div className="mb-4 flex items-center gap-2 border-b border-border pb-2 font-medium text-zinc-700">
        Franchisees
        {isFranchiseesRefetching && <Loader2 className="size-4 animate-spin" />}
      </div>
      <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="as-title-04 px-4">Name</TableHead>
              <TableHead className="as-title-04">Email</TableHead>
              <TableHead className="as-title-04">Phone</TableHead>
              <TableHead className="as-title-04">Franchise Location</TableHead>
              <TableHead className="as-title-04 px-4 text-right">
                Action
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="bg-white">{renderTableContent()}</TableBody>
        </Table>
      </div>

      <div className="mt-4 flex justify-end">
        <Button onClick={handleAddNew} className="flex items-center gap-2">
          <Plus className="size-4" />
          Add Franchisee
        </Button>
      </div>

      {/* Add Dialog */}
      <AddFranchiseeDialog open={addDialogOpen} setOpen={setAddDialogOpen} />

      {/* Edit Dialog */}
      <EditFranchiseeDialog
        open={editDialogOpen}
        franchisee={selectedFranchisee}
        setOpen={setEditDialogOpen}
        onSuccess={() => setSelectedFranchisee(null)}
      />

      {/* Delete Dialog */}
      <DeleteDialog
        open={deleteDialogOpen}
        label1={selectedFranchisee?.name}
        label2="franchisee"
        isLoading={isDeleting}
        setOpen={setDeleteDialogOpen}
        onDelete={confirmDelete}
        onCancel={cancelDelete}
      />
    </div>
  );
};

export default FranchiseesTab;
