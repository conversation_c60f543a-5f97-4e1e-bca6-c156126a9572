import { useOrganizationList, useUser } from '@clerk/clerk-react';
import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import AddNotesDialog from '@/components/dialogs/AddNotesDialog';
import NoteCard from '@/components/notes/NoteCard';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCreateNote } from '@/hooks/notes/useCreateNote';
import { useGetNotes } from '@/hooks/notes/useGetNotes';
import { useToast } from '@/hooks/ui/use-toast';
import { Note, NoteCategory } from '@/types/note.type';

interface NotesTabProps {
  zeeId: string;
}

const NotesTab = ({ zeeId }: NotesTabProps) => {
  const [activeTab, setActiveTab] = useState<'all' | NoteCategory>('all');
  const [isAddNotesDialogOpen, setIsAddNotesDialogOpen] = useState(false);

  const { toast } = useToast();
  const { user } = useUser();
  const { userMemberships } = useOrganizationList({
    userMemberships: { infinite: true, keepPreviousData: true },
  });

  // API hooks
  const {
    mutate: createNote,
    isLoading: isCreating,
    isSuccess: createSuccess,
  } = useCreateNote();

  const {
    data: notesData,
    isLoading: isFetching,
    refetch: refetchNotes,
  } = useGetNotes(zeeId);

  const notes = notesData?.data || [];

  // Get user's role from organization membership
  const getUserRole = () => {
    const role = userMemberships.data?.[0]?.role;

    // Map Clerk roles to display names
    const roleMapping: Record<string, string> = {
      'org:partner_consultant': 'Partner Consultant',
      'org:partner_administrator': 'Partner Administrator',
      'org:leadership_owner': 'Leadership Owner',
      'org:leadership_administrator': 'Leadership Administrator',
      'org:franchisee': 'Franchisee',
    };

    // Return mapped role or format the role name
    return (
      roleMapping[role || ''] ||
      role
        ?.replace('org:', '')
        .replace('_', ' ')
        .replace(/\b\w/g, (l) => l.toUpperCase()) ||
      'User'
    );
  };

  // Refetch notes after successful creation
  useEffect(() => {
    if (createSuccess) {
      refetchNotes();
    }
  }, [createSuccess, refetchNotes]);

  const tabs = [
    { id: 'all', label: 'All Notes', count: notes.length },
    {
      id: 'calls',
      label: 'Calls',
      count: notes.filter((n: Note) => n.category === 'calls').length,
    },
    {
      id: 'meeting',
      label: 'Meeting',
      count: notes.filter((n: Note) => n.category === 'meeting').length,
    },
    {
      id: 'manual-email',
      label: 'Manual Email',
      count: notes.filter((n: Note) => n.category === 'manual-email').length,
    },
    {
      id: 'system-emails',
      label: 'System Emails',
      count: notes.filter((n: Note) => n.category === 'system-emails').length,
    },
    {
      id: 'firefiles',
      label: 'Firefiles',
      count: notes.filter((n: Note) => n.category === 'firefiles').length,
    },
    {
      id: 'general',
      label: 'General',
      count: notes.filter((n: Note) => n.category === 'general').length,
    },
  ];

  const getFilteredNotes = (tabId: string): Note[] => {
    return tabId === 'all'
      ? notes
      : notes.filter((note: Note) => note.category === tabId);
  };

  const handleEditNote = (noteId: string, content: string) => {
    // TODO: Implement edit note API call
    console.log('Edit note:', noteId, content);
    toast({
      title: 'Feature coming soon',
      description: 'Note editing will be available soon',
    });
  };

  const handleDeleteNote = (noteId: string) => {
    // TODO: Implement delete note API call
    console.log('Delete note:', noteId);
    toast({
      title: 'Feature coming soon',
      description: 'Note deletion will be available soon',
    });
  };

  const handleAddNote = async (noteData: {
    title: string;
    content: string;
    category: NoteCategory;
    attachment?: {
      file_name: string;
      file_url: string;
      file_size: number;
      file_type: string;
    };
  }) => {
    try {
      // Prepare the API payload
      const createNotePayload = {
        zee_id: zeeId,
        title: noteData.title,
        content: noteData.content,
        category: noteData.category,
        author_id: user?.id,
        author_name: user?.fullName || user?.firstName || 'Current User',
        author_avatar: user?.imageUrl,
        author_role: getUserRole(),
        is_automated: false,
        ...(noteData.attachment && {
          attachments: [
            {
              file_name: noteData.attachment.file_name,
              file_path: noteData.attachment.file_url,
              file_url: noteData.attachment.file_url,
              file_size: noteData.attachment.file_size,
              file_type: noteData.attachment.file_type,
            },
          ],
        }),
      };

      // Call the API to create the note
      await createNote(createNotePayload);

      // Show success message
      toast({
        title: 'Note created successfully',
        description: noteData.attachment
          ? `Note created with attachment: ${noteData.attachment.file_name}`
          : 'Note created successfully',
      });

      // Close the dialog
      setIsAddNotesDialogOpen(false);
    } catch (error) {
      console.error('Error creating note:', error);
      toast({
        title: 'Error creating note',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  };

  const renderNotesContent = (tabId: string) => {
    const filteredNotes = getFilteredNotes(tabId);

    if (isFetching) {
      return (
        <div className="rounded-xl border border-border bg-white p-8 text-center">
          <p className="text-typo-gray-quaternary">Loading notes...</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Notes List */}
        <div className="space-y-3">
          {filteredNotes.map((note) => (
            <NoteCard
              key={note.id}
              note={note}
              onEdit={handleEditNote}
              onDelete={handleDeleteNote}
            />
          ))}
        </div>

        {filteredNotes.length === 0 && (
          <div className="rounded-xl border border-border bg-white p-8 text-center">
            <p className="text-typo-gray-quaternary">
              {tabId === 'all'
                ? 'No notes yet. Click "Add Notes" to create your first note.'
                : `No ${tabId.replace('-', ' ')} notes yet. Switch to "All Notes" or add a new note in this category.`}
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="as-title-01 as-strong font-medium text-zinc-700">
          Contact Notes
        </h3>
        <Button
          onClick={() => setIsAddNotesDialogOpen(true)}
          className="flex items-center gap-2"
          disabled={isCreating}
        >
          <Plus size={16} />
          {isCreating ? 'Creating...' : 'Add Notes'}
        </Button>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'all' | NoteCategory)}
      >
        <TabsList className="mb-4 bg-gray-200">
          {tabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id}>
              {tab.label} {tab.count > 0 && `(${tab.count})`}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id}>
            {renderNotesContent(tab.id)}
          </TabsContent>
        ))}
      </Tabs>

      {/* Add Notes Dialog */}
      <AddNotesDialog
        open={isAddNotesDialogOpen}
        setOpen={setIsAddNotesDialogOpen}
        onSave={handleAddNote}
      />
    </div>
  );
};

export default NotesTab;
