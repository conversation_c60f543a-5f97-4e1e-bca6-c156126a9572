import 'react-circular-progressbar/dist/styles.css';

import { useOrganization } from '@clerk/clerk-react';
import { ChevronLeft, Loader2, SquarePen } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import DefaultCompanyLogo from '@/assets/icons/default-company-logo.svg?react';
import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import ConsultantCard from '@/components/cards/ConsultantCard';
import EditBrandScoreDialog from '@/components/dialogs/EditBrandScoreDialog';
import SemiCircleProgressBar from '@/components/SemicircleProgressBar';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetBrandMatchScore } from '@/hooks/brand-match-scores/useGetBrandMatchScore';
import { useGetZeeProfile } from '@/hooks/zee/useGetZeeProfile';
import useZeeStore from '@/stores/useZeeStore';

import { BackgroundTab } from './BackgroundTab';
import DiscoveryTab from './DiscoveryTab';
import { DocumentsTab } from './DocumentsTab';
import FinancialTab from './FinancialTab';
import NotesTab from './NotesTab';
import ProfileTab from './ProfileTab';
import TerritoriesTab from './TerritoriesTab';
import ZeeFeatureLocksTab from './ZeeFeatureLocksTab';

const ZeeProfile = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [percentage, setPercentage] = useState<number>(0);
  const [selectedTab, setSelectedTab] = useState<string>('profile');

  const navigate = useNavigate();
  const { organization } = useOrganization();
  const { id: zeeId } = useParams<{ id: string }>();

  const { zee, setZee } = useZeeStore();
  const { result, isLoading, isError } = useGetZeeProfile(zeeId || '');

  // Get brand match score for this zee
  const {
    result: brandMatchScoreResult,
    isLoading: isBrandMatchScoreLoading,
    isError: isBrandMatchScoreError,
  } = useGetBrandMatchScore(zeeId || '');

  const hasBrandMatchScore =
    !isBrandMatchScoreError && brandMatchScoreResult?.data?.score !== undefined;
  const currentScore = hasBrandMatchScore
    ? brandMatchScoreResult?.data?.score || 0
    : 0;

  const goBack = () => {
    navigate(`/dashboard/data-center?tab=discovery`);
  };

  const handleGoBackClick = (): void => {
    goBack();
  };

  const handelClickTab = (value: string) => {
    setSelectedTab(value);
  };

  const handleEditClick = (): void => {
    setIsOpen(true);
  };

  const handleConsultantNameClick = (): void => {
    const consultantId = zee?.consultant_inviter?.id;
    if (!consultantId) return;
    navigate(`/dashboard/consultant-profile/${consultantId}`);
  };

  const renderZeeAvatar = () => {
    if (zee?.photo_url) {
      return (
        <img
          src={zee.photo_url}
          alt={zee.full_name || 'Zee avatar'}
          className="h-[90px] w-[90px] rounded-full border-2 border-white shadow-lg"
        />
      );
    }
    return (
      <EmptyAvatar className="h-[90px] w-[90px] rounded-full border-2 border-white shadow-lg" />
    );
  };

  useEffect(() => {
    if (result?.data) {
      setZee(result.data);
    }
  }, [result, setZee]);

  // Update percentage when brand match score is loaded
  useEffect(() => {
    setPercentage(currentScore);
  }, [currentScore]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError || !zee) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500">Failed to load zee profile</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <div className="flex w-full items-center justify-between gap-5">
        <Button
          size="icon"
          variant="ghost"
          className="flex-shrink-0"
          onClick={handleGoBackClick}
        >
          <ChevronLeft className="cursor-pointer" />
        </Button>
        <div className="flex w-full items-center justify-start gap-5">
          {renderZeeAvatar()}
          <div>
            <p className="text-3xl text-[#101828]">{zee.full_name}</p>
            <p className="text-base">
              {zee.address}, {zee.city}, {zee.state} {zee.zip_code}
            </p>
          </div>
        </div>
        <div className="flex flex-col gap-4 xl:flex-row">
          {/* Consultant Card */}
          <ConsultantCard
            isLoading={isLoading}
            isError={false}
            consultant={zee?.consultant_inviter}
            onConsultantClick={handleConsultantNameClick}
          />

          {/* Brand Match Score Card */}
          <div className="flex rounded-xl bg-zinc-50 p-2.5">
            <div className="w-[120px] space-y-2.5 text-sm text-typo-gray-tertiary">
              {organization?.imageUrl ? (
                <img
                  src={organization?.imageUrl}
                  alt={organization?.name || 'Company logo'}
                  className="h-6 w-6 rounded-full"
                />
              ) : (
                <DefaultCompanyLogo className="h-6 w-6" />
              )}
              <div>
                <p>Brand</p>
                <p className="">match score</p>
              </div>
            </div>
            <div className="flex items-start">
              {isBrandMatchScoreLoading ? (
                <div className="flex h-[80px] w-[150px] items-center justify-center">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                </div>
              ) : hasBrandMatchScore ? (
                <div style={{ width: 150, height: 80, overflow: 'hidden' }}>
                  <div
                    style={{
                      width: 150,
                      height: 150,
                      position: 'relative',
                      top: '0px',
                    }}
                  >
                    <SemiCircleProgressBar percentage={percentage} />
                  </div>
                </div>
              ) : (
                <div className="flex h-[80px] w-[150px] items-center justify-center">
                  <div className="text-center">
                    <p className="text-xs text-gray-500">No brand match</p>
                    <p className="text-xs text-gray-500">score set</p>
                  </div>
                </div>
              )}
              <div
                className="cursor-pointer rounded-md border border-secondary bg-white p-2.5"
                onClick={handleEditClick}
              >
                <SquarePen className="h-4 w-4" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <Tabs
        value={selectedTab}
        onValueChange={handelClickTab}
        className="w-full"
      >
        <TabsList className="mb-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="financials">Financials</TabsTrigger>
          <TabsTrigger value="background">Background</TabsTrigger>
          <TabsTrigger value="territories">Territories</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="discovery">Discovery</TabsTrigger>
          <TabsTrigger value="feature-lock">Feature Lock</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>
        <TabsContent
          value="profile"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <ProfileTab />
        </TabsContent>
        <TabsContent
          value="financials"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <FinancialTab />
        </TabsContent>
        <TabsContent
          value="background"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <BackgroundTab />
        </TabsContent>
        <TabsContent
          value="territories"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <TerritoriesTab zeeId={zeeId || ''} />
        </TabsContent>
        <TabsContent
          value="documents"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <DocumentsTab zeeId={zeeId || ''} />
        </TabsContent>
        <TabsContent
          value="discovery"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <DiscoveryTab />
        </TabsContent>
        <TabsContent
          value="feature-lock"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          {zeeId ? (
            <ZeeFeatureLocksTab zeeId={zeeId} />
          ) : (
            <div>Invalid zee profile</div>
          )}
        </TabsContent>
        <TabsContent
          value="notes"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          {zeeId ? <NotesTab zeeId={zeeId} /> : <div>Invalid zee profile</div>}
        </TabsContent>
      </Tabs>
      {isOpen && (
        <EditBrandScoreDialog
          open={isOpen}
          setOpen={setIsOpen}
          setPercentage={setPercentage}
          percentage={percentage}
          zeeId={zeeId || ''}
          hasExistingScore={hasBrandMatchScore}
        />
      )}
    </div>
  );
};

export default ZeeProfile;
