import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { z } from 'zod';

import { ZeeFinancialFormSchema } from '@/components/forms/schemas/ZeeFinancialFormSchema';
import { ZeeOtherFinancialFormSchema } from '@/components/forms/schemas/ZeeOtherFinancialFormSchema';
import { ZeeFinancialForm } from '@/components/forms/zee/ZeeFinancialForm';
import { ZeeOtherFinancialForm } from '@/components/forms/zee/ZeeOtherFinancialForm';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useCreateZeeFinancial } from '@/hooks/zee/useCreateZeeFinancial';
import { useGetZeeFinancial } from '@/hooks/zee/useGetZeeFinancial';
import { useUpdateZeeFinancial } from '@/hooks/zee/useUpdateZeeFinancial';
import useZeeStore from '@/stores/useZeeStore';

type FinancialFormValues = z.infer<typeof ZeeFinancialFormSchema>;
type OtherFinancialFormValues = z.infer<typeof ZeeOtherFinancialFormSchema>;

const FinancialTab = () => {
  const { id: zeeId } = useParams<{ id: string }>();
  const financialForm = useForm<FinancialFormValues>({
    resolver: zodResolver(ZeeFinancialFormSchema),
    defaultValues: {},
  });
  const otherFinancialForm = useForm<OtherFinancialFormValues>({
    resolver: zodResolver(ZeeOtherFinancialFormSchema),
    defaultValues: {},
  });

  const {
    zeeFinancial,
    zeeOtherFinancial,
    setZeeFinancial,
    setZeeOtherFinancial,
  } = useZeeStore();
  const {
    result: zeeFinancialResult,
    isLoading: isZeeFinancialLoading,
    isError: isZeeFinancialError,
    isSuccess: isZeeFinancialSuccess,
  } = useGetZeeFinancial(zeeId || '');
  const {
    mutate: updateZeeFinancial,
    result: updateZeeFinancialResult,
    isError: isUpdateZeeFinancialError,
    isSuccess: isUpdateZeeFinancialSuccess,
    isLoading: isUpdateZeeFinancialLoading,
  } = useUpdateZeeFinancial();
  const {
    mutate: createZeeFinancial,
    result: createZeeFinancialResult,
    isError: isCreateZeeFinancialError,
    isSuccess: isCreateZeeFinancialSuccess,
    isLoading: isCreateZeeFinancialLoading,
  } = useCreateZeeFinancial();

  const resetForms = useCallback(() => {
    financialForm.reset({
      ...zeeFinancial,
    });

    otherFinancialForm.reset({
      ...zeeOtherFinancial,
    });
  }, [zeeFinancial, zeeOtherFinancial]);

  const handleSubmit = async (): Promise<void> => {
    console.log('1. handleSubmit');
    const isFinancialFormValid = await financialForm.trigger();
    console.log('2. isFinancialFormValid', isFinancialFormValid);
    console.log('financialForm.formState.errors', financialForm);
    const isOtherFinancialFormValid = await otherFinancialForm.trigger();
    console.log('3. isOtherFinancialFormValid', isOtherFinancialFormValid);
    if (isFinancialFormValid && isOtherFinancialFormValid) {
      const submitPayload = {
        ...financialForm.getValues(),
        other_financial: otherFinancialForm.getValues(),
      };
      console.log('4. submitPayload', submitPayload);
      if (!zeeFinancial && !zeeOtherFinancial) {
        console.log('5. createZeeFinancial');
        createZeeFinancial(submitPayload);
      } else {
        console.log('6. updateZeeFinancial');
        updateZeeFinancial(submitPayload);
      }
    }
  };

  const renderEditButtons = () => {
    return (
      <div className="flex w-full justify-end">
        <div className="flex gap-1">
          <Button size="sm" className="w-full md:w-fit" onClick={handleSubmit}>
            Save Changes
          </Button>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (isZeeFinancialError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isZeeFinancialError]);

  useEffect(() => {
    if (isZeeFinancialSuccess && zeeFinancialResult?.data) {
      if (zeeFinancialResult?.data?.zee_financial) {
        setZeeFinancial(zeeFinancialResult?.data?.zee_financial);
      }

      if (zeeFinancialResult?.data?.zee_other_financial) {
        setZeeOtherFinancial(zeeFinancialResult?.data?.zee_other_financial);
      }

      resetForms();
    }
  }, [
    zeeFinancialResult,
    isZeeFinancialSuccess,
    resetForms,
    setZeeFinancial,
    setZeeOtherFinancial,
  ]);

  useEffect(() => {
    if (isUpdateZeeFinancialError || isCreateZeeFinancialError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isUpdateZeeFinancialError, isCreateZeeFinancialError]);

  useEffect(() => {
    if (isCreateZeeFinancialSuccess || isUpdateZeeFinancialSuccess) {
      if (createZeeFinancialResult?.data) {
        setZeeFinancial(createZeeFinancialResult?.data?.zee_financial);
        setZeeOtherFinancial(
          createZeeFinancialResult?.data?.zee_other_financial
        );
        resetForms();
      }
      if (updateZeeFinancialResult?.data) {
        setZeeFinancial(updateZeeFinancialResult?.data?.zee_financial);
        setZeeOtherFinancial(
          updateZeeFinancialResult?.data?.zee_other_financial
        );
        resetForms();
      }
    }
  }, [
    createZeeFinancialResult,
    updateZeeFinancialResult,
    isCreateZeeFinancialSuccess,
    isUpdateZeeFinancialSuccess,
    resetForms,
    setZeeFinancial,
    setZeeOtherFinancial,
  ]);

  if (isZeeFinancialLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {(isCreateZeeFinancialLoading || isUpdateZeeFinancialLoading) && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-100/50 backdrop-blur-sm">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      <ZeeFinancialForm form={financialForm} />
      <ZeeOtherFinancialForm form={otherFinancialForm} />
      {renderEditButtons()}
    </div>
  );
};

export default FinancialTab;
