import {
  DollarSign,
  Edit,
  Folders,
  Loader2,
  MapPin,
  Sparkles,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { useCreateZeeFeatureLocks } from '@/hooks/feature-locks/useCreateZeeFeatureLocks';
import { useGetFeatureLocks } from '@/hooks/feature-locks/useGetFeatureLocks';
import { useGetZeeFeatureLocks } from '@/hooks/feature-locks/useGetZeeFeatureLocks';
import { useUpdateZeeFeatureLocks } from '@/hooks/feature-locks/useUpdateZeeFeatureLocks';
import { useGetAllStage } from '@/hooks/stages/useGetAllStages';
import { toast } from '@/hooks/ui/use-toast';
import useStageStore from '@/stores/stages/useStageStore';

interface ZeeFeatureLocksTabProps {
  zeeId: string;
}

type FeatureKey = 'aimei' | 'library' | 'financials' | 'territories';

interface StageAccess {
  name: FeatureKey;
  icon: React.ReactNode;
}

const INITIAL_VALUES: Record<FeatureKey, number> = {
  aimei: 0,
  library: 0,
  financials: 0,
  territories: 0,
};

const STAGES_OF_ACCESS: StageAccess[] = [
  { name: 'aimei', icon: <Sparkles className="h-5 w-5" /> },
  { name: 'library', icon: <Folders className="h-5 w-5" /> },
  { name: 'financials', icon: <DollarSign className="h-5 w-5" /> },
  { name: 'territories', icon: <MapPin className="h-5 w-5" /> },
];

const ZeeFeatureLocksTab = ({ zeeId }: ZeeFeatureLocksTabProps) => {
  const lastStageRef = useRef<HTMLParagraphElement>(null);

  const { stageList, setStageList } = useStageStore();
  const [isEdit, setIsEdit] = useState(false);
  const [values, setValues] =
    useState<Record<FeatureKey, number>>(INITIAL_VALUES);
  const [editedValues, setEditedValues] =
    useState<Record<FeatureKey, number>>(INITIAL_VALUES);
  const [lastStageWidth, setLastStageWidth] = useState(0);

  const {
    result: getAllStagesResult,
    isError: isGetAllStagesError,
    isSuccess: isGetAllStagesSuccess,
  } = useGetAllStage();

  const {
    result: orgFeatureLocksResult,
    isError: isOrgFeatureLocksError,
    isSuccess: isOrgFeatureLocksSuccess,
    isLoading: isOrgFeatureLocksLoading,
  } = useGetFeatureLocks();

  const {
    result: zeeFeatureLocksResult,
    isError: isZeeFeatureLocksError,
    isSuccess: isZeeFeatureLocksSuccess,
    isLoading: isZeeFeatureLocksLoading,
    isRefetching: isZeeFeatureLocksRefetching,
  } = useGetZeeFeatureLocks(zeeId);

  const {
    mutate: createZeeFeatureLocks,
    result: createZeeFeatureLocksResult,
    isError: isCreateZeeFeatureLocksError,
    isSuccess: isCreateZeeFeatureLocksSuccess,
    isLoading: isCreateZeeFeatureLocksLoading,
  } = useCreateZeeFeatureLocks(zeeId);

  const {
    mutate: updateZeeFeatureLocks,
    result: updateZeeFeatureLocksResult,
    isError: isUpdateZeeFeatureLocksError,
    isSuccess: isUpdateZeeFeatureLocksSuccess,
    isLoading: isUpdateZeeFeatureLocksLoading,
  } = useUpdateZeeFeatureLocks(zeeId);

  const getStageId = (value: number) =>
    value > 0 ? stageList[value - 1].id : null;

  const handleSave = () => {
    const payload = {
      aimei: getStageId(editedValues.aimei),
      library: getStageId(editedValues.library),
      financials: getStageId(editedValues.financials),
      territories: getStageId(editedValues.territories),
    };

    if (zeeFeatureLocksResult?.data) {
      updateZeeFeatureLocks(payload);
    } else {
      createZeeFeatureLocks(payload);
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
    setEditedValues(values);
  };

  const renderStageHeader = () => {
    return (
      <div className="as-title-04 as-strong flex border-b text-typo-gray-tertiary">
        <div className="w-40 flex-shrink-0 px-4 py-3">Name</div>
        <div className="flex flex-1">
          <p className="w-full py-3 text-center">No set</p>
          {stageList.map((item, index) => (
            <p
              key={item.id}
              ref={index === stageList.length - 1 ? lastStageRef : null}
              className="w-full py-3 text-center"
            >
              {item.name}
            </p>
          ))}
        </div>
      </div>
    );
  };

  const renderStageRow = (stage: StageAccess) => (
    <div key={stage.name} className="flex">
      <div className="as-body-02 as-strong flex w-40 flex-shrink-0 gap-2 px-4 py-6">
        {stage.icon}
        <p>{stage.name.charAt(0).toUpperCase() + stage.name.slice(1)}</p>
      </div>
      <div className="flex flex-1 items-center justify-center">
        <div style={{ width: lastStageWidth / 2 }} className="h-1.5">
          <div className="float-left h-full w-full rounded-r-lg bg-primary" />
        </div>
        <div className="ml-[-10px] mr-[-10px] flex-1">
          <Slider
            max={stageList.length}
            value={[isEdit ? editedValues[stage.name] : values[stage.name]]}
            disabled={!isEdit}
            className="rounded-full bg-secondary"
            onValueChange={([value]) => {
              setEditedValues((prev) => ({ ...prev, [stage.name]: value }));
            }}
          />
        </div>
        <div style={{ width: lastStageWidth / 2 }} className="h-1.5">
          <div className="float-left h-full w-full rounded-r-lg bg-secondary" />
        </div>
      </div>
    </div>
  );

  const updateValuesFromResult = (
    result:
      | typeof zeeFeatureLocksResult
      | typeof updateZeeFeatureLocksResult
      | typeof createZeeFeatureLocksResult
      | typeof orgFeatureLocksResult
  ) => {
    if (!result?.data || stageList.length === 0) return;

    const newValues = Object.fromEntries(
      Object.entries(result.data)
        .filter(([key]) =>
          ['aimei', 'library', 'financials', 'territories'].includes(key)
        )
        .map(([key, value]) => [
          key,
          stageList.findIndex((stage) => stage.id === value) + 1,
        ])
    ) as Record<FeatureKey, number>;

    setValues(newValues);
    setEditedValues(newValues);
  };

  useEffect(() => {
    if (lastStageRef.current && stageList.length > 0) {
      const elementClientWidth = lastStageRef.current.clientWidth;
      setLastStageWidth(elementClientWidth);
    }
  }, [stageList, lastStageRef.current]);

  useEffect(() => {
    if (
      isGetAllStagesError ||
      isOrgFeatureLocksError ||
      isZeeFeatureLocksError ||
      isUpdateZeeFeatureLocksError ||
      isCreateZeeFeatureLocksError
    ) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [
    isGetAllStagesError,
    isOrgFeatureLocksError,
    isZeeFeatureLocksError,
    isUpdateZeeFeatureLocksError,
    isCreateZeeFeatureLocksError,
  ]);

  useEffect(() => {
    if (isGetAllStagesSuccess) {
      setStageList(
        getAllStagesResult?.data.sort(
          (a, b) => (a.index ?? 0) - (b.index ?? 0)
        ) ?? []
      );
    }
  }, [getAllStagesResult, isGetAllStagesSuccess, setStageList]);

  useEffect(() => {
    if (isZeeFeatureLocksSuccess) {
      updateValuesFromResult(zeeFeatureLocksResult);
    }
  }, [zeeFeatureLocksResult, isZeeFeatureLocksSuccess, stageList]);

  useEffect(() => {
    if (isUpdateZeeFeatureLocksSuccess) {
      updateValuesFromResult(updateZeeFeatureLocksResult);
      setIsEdit(false);
    }
  }, [updateZeeFeatureLocksResult, isUpdateZeeFeatureLocksSuccess, stageList]);

  useEffect(() => {
    if (isCreateZeeFeatureLocksSuccess) {
      updateValuesFromResult(createZeeFeatureLocksResult);
      setIsEdit(false);
    }
  }, [createZeeFeatureLocksResult, isCreateZeeFeatureLocksSuccess, stageList]);

  // Fallback to organization defaults when zee doesn't have individual locks
  useEffect(() => {
    if (
      !isZeeFeatureLocksLoading &&
      !zeeFeatureLocksResult?.data &&
      isOrgFeatureLocksSuccess &&
      orgFeatureLocksResult?.data
    ) {
      updateValuesFromResult(orgFeatureLocksResult);
    }
  }, [
    orgFeatureLocksResult,
    isOrgFeatureLocksSuccess,
    zeeFeatureLocksResult,
    isZeeFeatureLocksLoading,
    stageList,
  ]);

  return (
    <div className="w-full rounded-md border-border bg-muted">
      <div className="flex items-center justify-between border-b px-6 py-3">
        <div className="flex items-center gap-2">
          <p className="as-title-01 as-strong">Stage of access</p>
          {(isZeeFeatureLocksLoading ||
            isZeeFeatureLocksRefetching ||
            isOrgFeatureLocksLoading) && (
            <Loader2 className="size-4 animate-spin" />
          )}
        </div>
        {!isEdit ? (
          <Button
            size="sm"
            variant="outline"
            className="bg-secondary hover:bg-white"
            onClick={() => setIsEdit(true)}
          >
            <Edit className="h-4 w-4" /> Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              disabled={
                isUpdateZeeFeatureLocksLoading || isCreateZeeFeatureLocksLoading
              }
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              type="submit"
              disabled={
                isUpdateZeeFeatureLocksLoading || isCreateZeeFeatureLocksLoading
              }
              onClick={handleSave}
            >
              {(isUpdateZeeFeatureLocksLoading ||
                isCreateZeeFeatureLocksLoading) && (
                <Loader2 className="size-4 animate-spin" />
              )}
              Save Changes
            </Button>
          </div>
        )}
      </div>
      <div className="bg-muted p-4">
        <div className="rounded-md bg-white px-4">
          {renderStageHeader()}
          {STAGES_OF_ACCESS.map(renderStageRow)}
        </div>
      </div>
    </div>
  );
};

export default ZeeFeatureLocksTab;
