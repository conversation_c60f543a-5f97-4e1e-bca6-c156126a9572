import { <PERSON>ader2, <PERSON><PERSON><PERSON>, <PERSON>otate<PERSON>cw, UserIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import SimpleTerritoryItem from '@/components/territories/SimpleTerritoryItem';
import StateStatusBadge from '@/components/territories/StateStatusBadge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { TERRITORIES_API_URL } from '@/constants/apis';
import { configuration } from '@/constants/config';
import {
  SAMPLE_PREFERRED_PERSONS,
  SAMPLE_ZEE_USERS,
} from '@/constants/territories/dummy';
import { US_STATES } from '@/constants/territories/states';
import { apiManager } from '@/helpers/api.manager';
import { toast } from '@/hooks/ui/use-toast';
import { cn } from '@/lib/ui/utils';
import useTerritoryStore from '@/stores/useTerritoryStore';
import { Territory } from '@/types/territories/territory.type';

interface TerritoriesTabProps {
  zeeId?: string;
}

const TerritoriesTab = ({ zeeId }: TerritoriesTabProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [expandedStates, setExpandedStates] = useState<Record<string, boolean>>(
    {}
  );

  const { likedTerritories, setLikedTerritories } = useTerritoryStore();

  const fetchLikedTerritories = useCallback(async () => {
    if (!zeeId) return;

    setIsLoading(true);
    try {
      const response = await apiManager.get(TERRITORIES_API_URL, {
        baseURL: configuration.TERRITORIES_API_URL,
        headers: { 'Content-Type': 'application/json' },
        params: {
          liked_only: true,
          zee_id: zeeId,
        },
      });

      if (response.status !== 200) {
        toast({
          title: 'Error loading territories',
          variant: 'destructive',
        });
        return;
      }

      setLikedTerritories(response.data.data);
    } catch (error) {
      console.error('Error in fetching territories:', error);
    } finally {
      setIsLoading(false);
    }
  }, [zeeId, setLikedTerritories]);

  // Group territories by state
  const territoriesByState = useMemo(() => {
    if (!likedTerritories) return {};

    return likedTerritories.reduce(
      (acc, territory) => {
        const state = territory.state;
        if (!acc[state]) {
          acc[state] = [];
        }
        acc[state].push(territory);
        return acc;
      },
      {} as Record<string, Territory[]>
    );
  }, [likedTerritories]);

  // Calculate counts for each state
  const stateStats = useMemo(() => {
    return Object.entries(territoriesByState).map(
      ([state, stateTerritories]) => {
        const total = stateTerritories.length;
        const sold = stateTerritories.filter((t) => t.status === 'SOLD').length;
        const reserved = stateTerritories.filter(
          (t) => t.status === 'RESERVED'
        ).length;
        const available = stateTerritories.filter(
          (t) => t.status === 'AVAILABLE'
        ).length;
        const state_preference_count =
          stateTerritories[0]?.state_preference_count || 0;
        // Extract photo arrays from SAMPLE_ZEE_USERS
        const userPhotos = SAMPLE_ZEE_USERS.map((user) => user.photo_url);

        // Take first 3
        const state_preference_photos = userPhotos.slice(0, 3);

        return {
          state,
          total,
          sold,
          reserved,
          available,
          state_preference_count,
          state_preference_photos,
        };
      }
    );
  }, [territoriesByState]);

  const toggleState = (state: string) => {
    setExpandedStates((prev) => ({
      ...prev,
      [state]: !prev[state],
    }));
  };

  const handleToggleState = (state: string) => () => {
    toggleState(state);
  };

  useEffect(() => {
    fetchLikedTerritories();
  }, [fetchLikedTerritories]);

  if (isLoading) {
    return (
      <div className="flex w-full justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (!zeeId) {
    return (
      <div className="flex items-center justify-center py-8">
        <p className="text-muted-foreground">No zee selected</p>
      </div>
    );
  }

  if (!likedTerritories || likedTerritories.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="outline"
            onClick={fetchLikedTerritories}
            className="h-8 w-8"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No liked territories found</p>
        </div>
      </div>
    );
  }

  if (stateStats.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="outline"
            onClick={fetchLikedTerritories}
            className="h-8 w-8"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No liked territories found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button
          size="icon"
          variant="outline"
          onClick={fetchLikedTerritories}
          className="h-8 w-8"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>
      <div className="space-y-4">
        {stateStats.map(
          ({
            state,
            total,
            sold,
            reserved,
            state_preference_count,
            state_preference_photos,
          }) => (
            <div
              key={state}
              className="overflow-hidden rounded-xl border border-border"
            >
              <div
                className={cn(
                  'flex items-center justify-between gap-6 bg-zinc-50 px-6 py-3.5 max-md:flex-col max-md:items-start max-md:gap-3 max-md:p-3',
                  expandedStates[state] && 'bg-brand-50'
                )}
              >
                <div className="flex items-center gap-2">
                  <MapPin
                    className={cn(
                      'h-5 w-5 text-zinc-400',
                      expandedStates[state] && 'text-brand-500'
                    )}
                  />
                  <p className="as-title-01 as-strong text-gray-700">
                    {US_STATES[state as keyof typeof US_STATES] || state}
                  </p>
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center gap-2 rounded-full border border-border px-4 py-2 max-md:hidden">
                    {state_preference_photos?.map((item, index) => (
                      <Avatar
                        key={index}
                        className={cn('h-6 w-6', index !== 0 && 'ml-[-20px]')}
                      >
                        <AvatarImage src={item} />
                        <AvatarFallback>
                          <UserIcon className="h-3 w-3" />
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    <p className="as-strong as-title-03">
                      {SAMPLE_PREFERRED_PERSONS[
                        state as keyof typeof US_STATES
                      ] + state_preference_count}{' '}
                      other users are looking in{' '}
                      {US_STATES[state as keyof typeof US_STATES]}
                    </p>
                  </div>
                  <StateStatusBadge
                    count={total - sold - reserved}
                    label="territories available"
                  />
                  <Button type="button" onClick={handleToggleState(state)}>
                    {expandedStates[state] ? 'Hide' : 'View'}
                  </Button>
                </div>
              </div>

              {expandedStates[state] && (
                <>
                  {territoriesByState[state].map((territory) => (
                    <SimpleTerritoryItem
                      key={territory.id}
                      territory={territory}
                    />
                  ))}
                </>
              )}
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default TerritoriesTab;
