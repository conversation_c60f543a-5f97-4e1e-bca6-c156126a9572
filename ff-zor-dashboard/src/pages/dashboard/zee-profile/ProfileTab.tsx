import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { z } from 'zod';

import ZeeProfileFormSchema from '@/components/forms/schemas/ZeeProfileFormSchema';
import { ZeeProfileForm } from '@/components/forms/zee/ZeeProfileForm';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useGetZeeProfile } from '@/hooks/zee/useGetZeeProfile';
import { useUpdateZeeProfile } from '@/hooks/zee/useUpdateZeeProfile';
import useZeeStore from '@/stores/useZeeStore';

const ProfileTab = () => {
  const { id: zeeId } = useParams<{ id: string }>();
  const { zee, setZee } = useZeeStore();
  const { result, isLoading, isError } = useGetZeeProfile(zeeId || '');
  const { mutate: updateZee, isLoading: isUpdating } = useUpdateZeeProfile();

  const form = useForm<z.infer<typeof ZeeProfileFormSchema>>({
    resolver: zodResolver(ZeeProfileFormSchema),
    defaultValues: {},
  });

  useEffect(() => {
    if (result?.data) {
      setZee(result.data);
      form.reset(result.data);
    }
  }, [result, setZee, form]);

  useEffect(() => {
    if (isError) {
      toast({
        title: 'Something went wrong',
        description: 'Failed to load profile data',
        variant: 'destructive',
      });
    }
  }, [isError]);

  const handleSubmit = async () => {
    const isValid = await form.trigger();
    if (!isValid) return;

    const formData = form.getValues();
    updateZee(formData);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (!zee) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-typo-gray-tertiary">No profile data available</p>
      </div>
    );
  }

  return (
    <div className="relative space-y-2">
      {isUpdating && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-100/50 backdrop-blur-sm">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      <ZeeProfileForm form={form} />

      <div className="flex w-full justify-end">
        <Button size="sm" className="w-full md:w-fit" onClick={handleSubmit}>
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default ProfileTab;
