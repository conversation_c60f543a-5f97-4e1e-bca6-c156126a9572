import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import { NotesAttachmentsTable } from '@/components/tables/NotesAttachmentsTable';
import { SignedDocumentsTable } from '@/components/tables/SignedDocumentsTable';
import { ZeeDocumentsTable } from '@/components/tables/ZeeDocumentsTable';
import { ZorDocumentsTable } from '@/components/tables/ZorDocumentsTable';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SIGNED_DUMMY_DOCUMENTS } from '@/constants/documents/dummy';
import { useGetNotesAttachments } from '@/hooks/notes/useGetNotesAttachments';
import { toast } from '@/hooks/ui/use-toast';
import { useGetAllZeeDocuments } from '@/hooks/zee-document/useGetAllZeeDocuments';
import { useGetAllZorDocuments } from '@/hooks/zor-document/useGetAllZorDocuments';
import { useZeeDocumentStore } from '@/stores/useZeeDocumentStore';
import useZorDocumentStore from '@/stores/useZorDocumentStore';

const DocumentsTab = ({ zeeId }: { zeeId: string }) => {
  const { zorDocuments, setZorDocuments } = useZorDocumentStore();
  const { zeeDocuments, setZeeDocuments } = useZeeDocumentStore();

  const {
    result: zorDocumentsResult,
    isError: zorDocumentsError,
    isSuccess: zorDocumentsSuccess,
    isLoading: zorDocumentsLoading,
  } = useGetAllZorDocuments();

  const {
    result: zeeDocumentsResult,
    isError: zeeDocumentsError,
    isSuccess: zeeDocumentsSuccess,
    isLoading: zeeDocumentsLoading,
  } = useGetAllZeeDocuments(zeeId);

  const {
    data: notesAttachmentsResult,
    isError: notesAttachmentsError,
    isLoading: notesAttachmentsLoading,
  } = useGetNotesAttachments(zeeId);

  useEffect(() => {
    if (zorDocumentsError || zeeDocumentsError || notesAttachmentsError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [zorDocumentsError, zeeDocumentsError, notesAttachmentsError]);

  useEffect(() => {
    if (zorDocumentsSuccess && zorDocumentsResult?.data) {
      setZorDocuments(zorDocumentsResult.data);
    }
  }, [zorDocumentsSuccess, zorDocumentsResult, setZorDocuments]);

  useEffect(() => {
    if (zeeDocumentsSuccess && zeeDocumentsResult?.data) {
      setZeeDocuments(zeeDocumentsResult.data);
    }
  }, [zeeDocumentsSuccess, zeeDocumentsResult, setZeeDocuments]);

  if (zorDocumentsLoading || zeeDocumentsLoading || notesAttachmentsLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4 border-b border-border pb-2">
        <p className="as-title-01 as-strong text-zinc-700">Documents</p>
      </div>
      <div className="rounded-xl md:bg-white md:p-4">
        <Tabs defaultValue="shared-docs">
          <div className="flex items-center justify-between">
            <TabsList className="w-full max-md:bg-slate-200 md:w-fit">
              <TabsTrigger value="shared-docs" className="w-full">
                Shared Docs
              </TabsTrigger>
              <TabsTrigger value="signed-docs" className="w-full">
                Signed Docs
              </TabsTrigger>
              <TabsTrigger value="uploaded-docs" className="w-full">
                Uploaded Docs
              </TabsTrigger>
              <TabsTrigger value="notes-attachments" className="w-full">
                Notes Attachments
              </TabsTrigger>
            </TabsList>
          </div>
          {zorDocumentsLoading ||
          zeeDocumentsLoading ||
          notesAttachmentsLoading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="size-4 animate-spin" />
            </div>
          ) : (
            <>
              <TabsContent value="shared-docs">
                <ZorDocumentsTable data={zorDocuments || []} />
              </TabsContent>
              <TabsContent value="signed-docs">
                <SignedDocumentsTable data={SIGNED_DUMMY_DOCUMENTS} />
              </TabsContent>
              <TabsContent value="uploaded-docs">
                <ZeeDocumentsTable data={zeeDocuments || []} />
              </TabsContent>
              <TabsContent value="notes-attachments">
                <NotesAttachmentsTable
                  data={notesAttachmentsResult?.data || []}
                />
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export { DocumentsTab };
