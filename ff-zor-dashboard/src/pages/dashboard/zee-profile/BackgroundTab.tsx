import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { z } from 'zod';

import { PersonalBackgroundFormSchema } from '@/components/forms/schemas/zee/PersonalBackgroundFormSchema';
import { IndustryBackgroundForm } from '@/components/forms/zee/background/ZeeIndustryBackgroundForm';
import { IndustryInterestForm } from '@/components/forms/zee/background/ZeeIndustryInterestForm';
import { PersonalBackgroundForm } from '@/components/forms/zee/background/ZeePersonalBackgroundForm';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useCreateZeeBackground } from '@/hooks/zee-background/useCreateZeeBackground';
import { useGetZeeBackground } from '@/hooks/zee-background/useGetZeeBackground';
import { useUpdateZeeBackground } from '@/hooks/zee-background/useUpdateZeeBackground';
import useZeeStore from '@/stores/useZeeStore';

type PersonalBackgroundFormValues = z.infer<
  typeof PersonalBackgroundFormSchema
>;

const BackgroundTab = () => {
  const { id: zeeId } = useParams();
  const [industryInterests, setIndustryInterests] = useState<
    Record<string, number>
  >({});
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);

  const personalBackgroundForm = useForm<PersonalBackgroundFormValues>({
    resolver: zodResolver(PersonalBackgroundFormSchema),
    defaultValues: {},
  });

  const { zeeBackground, setZeeBackground } = useZeeStore();

  const {
    result: zeeBackgroundResult,
    isError: isZeeBackgroundError,
    isSuccess: isZeeBackgroundSuccess,
    isLoading: isZeeBackgroundLoading,
  } = useGetZeeBackground(zeeId || '');
  const {
    mutate: createZeeBackground,
    result: createZeeBackgroundResult,
    isError: isCreateZeeBackgroundError,
    isSuccess: isCreateZeeBackgroundSuccess,
    isLoading: isCreateZeeBackgroundLoading,
  } = useCreateZeeBackground();
  const {
    mutate: updateZeeBackground,
    result: updateZeeBackgroundResult,
    isError: isUpdateZeeBackgroundError,
    isSuccess: isUpdateZeeBackgroundSuccess,
    isLoading: isUpdateZeeBackgroundLoading,
  } = useUpdateZeeBackground(zeeId || '');

  const resetForms = useCallback(() => {
    personalBackgroundForm.reset({
      ...zeeBackground,
      affirmative_answers_explanation:
        zeeBackground?.affirmative_answers_explanation || '',
    });

    setSelectedIndustries([...(zeeBackground?.industry_background || [])]);
    setIndustryInterests({
      ...(zeeBackground?.industry_interest || {}),
    });
  }, [zeeBackground]);

  const handleSubmit = async (): Promise<void> => {
    const isPersonalBackgroundFormValid =
      await personalBackgroundForm.trigger();

    if (isPersonalBackgroundFormValid) {
      const submitPayload = {
        ...personalBackgroundForm.getValues(),
        industry_background: selectedIndustries,
        industry_interest: industryInterests,
      };

      if (!zeeBackground) {
        createZeeBackground(submitPayload);
      } else {
        updateZeeBackground(submitPayload);
      }
    }
  };

  const renderEditButtons = () => {
    return (
      <div className="flex w-full justify-end">
        <div className="flex gap-1">
          <Button size="sm" className="w-full md:w-fit" onClick={handleSubmit}>
            Save Changes
          </Button>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (isZeeBackgroundError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isZeeBackgroundError]);

  useEffect(() => {
    if (isZeeBackgroundSuccess && zeeBackgroundResult?.data) {
      if (zeeBackgroundResult?.data) {
        setZeeBackground(zeeBackgroundResult?.data);
      }

      resetForms();
    }
  }, [
    zeeBackgroundResult,
    isZeeBackgroundSuccess,
    resetForms,
    setZeeBackground,
  ]);

  useEffect(() => {
    if (isCreateZeeBackgroundError || isUpdateZeeBackgroundError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isCreateZeeBackgroundError, isUpdateZeeBackgroundError]);

  useEffect(() => {
    if (isCreateZeeBackgroundSuccess || isUpdateZeeBackgroundSuccess) {
      if (createZeeBackgroundResult?.data) {
        setZeeBackground(createZeeBackgroundResult?.data);
      }

      if (updateZeeBackgroundResult?.data) {
        setZeeBackground(updateZeeBackgroundResult?.data);
      }

      resetForms();
    }
  }, [
    createZeeBackgroundResult,
    updateZeeBackgroundResult,
    isCreateZeeBackgroundSuccess,
    isUpdateZeeBackgroundSuccess,
    resetForms,
    setZeeBackground,
  ]);

  if (isZeeBackgroundLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  return (
    <div className="relative space-y-1.5">
      {(isCreateZeeBackgroundLoading || isUpdateZeeBackgroundLoading) && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-100/50 backdrop-blur-sm">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      <PersonalBackgroundForm form={personalBackgroundForm} />
      <IndustryBackgroundForm
        selectedIndustries={selectedIndustries}
        setSelectedIndustries={setSelectedIndustries}
      />
      <IndustryInterestForm
        industryInterests={industryInterests}
        setIndustryInterests={setIndustryInterests}
      />
      {renderEditButtons()}
    </div>
  );
};

export { BackgroundTab };
