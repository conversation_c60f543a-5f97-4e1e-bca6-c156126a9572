import { Loader2, LockKeyhole } from 'lucide-react';
import { useMemo } from 'react';
import { useParams } from 'react-router-dom';

import CompletedSVG from '@/assets/icons/completed.svg';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { useGetZeeOnboardingStatus } from '@/hooks/zee-onboarding-status/useGetZeeOnboardingStatus';
import { useUpdateZeeOnboardingStatus } from '@/hooks/zee-onboarding-status/useUpdateZeeOnboardingStatus';
import { truncateText } from '@/lib/string';
import { cn } from '@/lib/ui/utils';
import {
  DiscoveryStageResponse,
  StageItemResponse,
  ZeeDiscoveryStage,
  ZeeStageItem,
} from '@/types/zee.onboarding.status.type';

export const ProgressResult = (item: ZeeStageItem, index?: number) => {
  switch (item.status) {
    case 'completed':
      return (
        <div className="flex items-center gap-2">
          <img src={CompletedSVG} alt="" width={24} height={24} />
          <div>{item.title && truncateText(item.title, 50)}</div>
        </div>
      );
    case 'locked':
      return (
        <div className="flex w-full items-center justify-between pr-6">
          <div className="flex items-center gap-2">
            <div className="flex h-6 w-6 items-center justify-center rounded-full border bg-white text-typo-gray-quaternary">
              {index !== undefined ? index + 1 : ''}
            </div>
            <div className="text-typo-gray-quaternary">
              {item.title && truncateText(item.title, 50)}
            </div>
          </div>
          <LockKeyhole color="#A1A1AA" size={16} />
        </div>
      );

    default:
      break;
  }
};

const DiscoveryTab = () => {
  const { id: zeeId } = useParams<{ id: string }>();

  const { result, isLoading, isError } = useGetZeeOnboardingStatus(zeeId || '');
  const { mutate: updateStatus, isLoading: isUpdating } =
    useUpdateZeeOnboardingStatus();

  // Transform API data to UI format
  const discoveryStages: ZeeDiscoveryStage[] = useMemo(() => {
    if (!result?.data?.discovery_stages) return [];

    const completedStageIds =
      result.data.zee_onboarding_status?.completed_stage_ids || [];
    const completedStageItemIds =
      result.data.zee_onboarding_status?.completed_stage_item_ids || [];

    return result.data.discovery_stages.map((stage: DiscoveryStageResponse) => {
      const stageItems: ZeeStageItem[] = stage.stage_items.map(
        (item: StageItemResponse) => {
          const isCompleted = completedStageItemIds.includes(item.id);
          return {
            id: item.id,
            stage_id: item.stage_id,
            title: item.title,
            description: item.description,
            type: item.type,
            status: isCompleted ? 'completed' : 'locked',
            locked: !isCompleted,
          };
        }
      );

      const isStageCompleted = completedStageIds.includes(stage.id);
      const allItemsCompleted = stageItems.every(
        (item) => item.status === 'completed'
      );

      return {
        id: stage.id,
        stage_name: stage.name,
        status: isStageCompleted || allItemsCompleted ? 'completed' : 'locked',
        locked: !isStageCompleted && !allItemsCompleted,
        stage_items: stageItems,
      };
    });
  }, [result?.data]);

  const handleLockToggle = async (stageItemId: string, shouldLock: boolean) => {
    if (!zeeId || !result?.data?.zee_onboarding_status) return;

    const currentCompletedStageIds =
      result.data.zee_onboarding_status.completed_stage_ids;
    const currentCompletedStageItemIds =
      result.data.zee_onboarding_status.completed_stage_item_ids;

    let updatedCompletedStageItemIds = [...currentCompletedStageItemIds];

    if (shouldLock) {
      // Remove from completed stage items to lock it
      updatedCompletedStageItemIds = updatedCompletedStageItemIds.filter(
        (id) => id !== stageItemId
      );
    } else {
      // Add to completed stage items to unlock it
      if (!updatedCompletedStageItemIds.includes(stageItemId)) {
        updatedCompletedStageItemIds.push(stageItemId);
      }
    }

    try {
      await updateStatus({
        zee_id: zeeId,
        payload: {
          completed_stage_ids: currentCompletedStageIds,
          completed_stage_item_ids: updatedCompletedStageItemIds,
        },
      });
    } catch (error) {
      console.error('Failed to update zee onboarding status:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="">
        <div className="as-title-01 as-strong flex justify-between border-b px-6 py-3">
          Discovery stages
        </div>
        <div className="flex items-center justify-center p-8">
          <div className="text-red-500">Failed to load discovery stages</div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-4 border-b border-border pb-2">
        <p className="as-title-01 as-strong text-zinc-700">Documents</p>
      </div>
      <div>
        {discoveryStages.map((stage: ZeeDiscoveryStage, stageIndex: number) => {
          // Calculate completion percentage without useMemo inside map
          const stageCompletionPercentage =
            stage.stage_items.length === 0
              ? 0
              : Math.round(
                  (stage.stage_items.filter(
                    (item) => item.status === 'completed'
                  ).length /
                    stage.stage_items.length) *
                    100
                );

          return (
            <div
              key={stage.id}
              className={cn(
                'rounded-2xl border border-border bg-white',
                stageIndex > 0 && 'mt-4'
              )}
            >
              <div className="flex w-full items-center justify-between border-b border-border px-6 py-3">
                <div className="w-1/2 text-sm font-medium text-typo-gray-secondary">
                  {stageIndex + 1}. {stage.stage_name}
                </div>
                <div className="flex w-1/2 items-center gap-4">
                  <Progress
                    value={stageCompletionPercentage}
                    className="bg-secondary"
                  />
                  <p className="w-[150px] text-sm text-typo-gray-secondary">
                    {stageCompletionPercentage}% Completed
                  </p>
                </div>
              </div>
              {stage.stage_items.map(
                (item: ZeeStageItem, itemIndex: number) => (
                  <div
                    key={item.id}
                    className={cn(
                      'flex w-full items-center border-b border-border px-6 py-4 text-sm text-typo-gray-secondary',
                      itemIndex === stage.stage_items.length - 1 && 'border-b-0'
                    )}
                  >
                    <div className="flex w-1/2 items-center gap-2 font-medium">
                      {ProgressResult(item, itemIndex)}
                    </div>
                    <div className="flex w-1/2 justify-end">
                      <Switch
                        checked={item.locked}
                        disabled={isUpdating}
                        className="data-[state=checked]:bg-primary"
                        onCheckedChange={(checked) =>
                          handleLockToggle(item.id, checked)
                        }
                      />
                    </div>
                  </div>
                )
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DiscoveryTab;
