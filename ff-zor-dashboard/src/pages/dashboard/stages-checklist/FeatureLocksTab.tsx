import {
  DollarSign,
  Edit,
  Folders,
  Loader2,
  MapPin,
  Sparkles,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { useGetFeatureLocks } from '@/hooks/feature-locks/useGetFeatureLocks';
import { useUpdateFeatureLocks } from '@/hooks/feature-locks/useUpdateFeatureLocks';
import { toast } from '@/hooks/ui/use-toast';
import useStageStore from '@/stores/stages/useStageStore';

type FeatureKey = 'aimei' | 'library' | 'financials' | 'territories';

interface StageAccess {
  name: FeatureKey;
  icon: React.ReactNode;
}

const INITIAL_VALUES: Record<FeatureKey, number> = {
  aimei: 0,
  library: 0,
  financials: 0,
  territories: 0,
};

const STAGES_OF_ACCESS: StageAccess[] = [
  { name: 'aimei', icon: <Sparkles className="h-5 w-5" /> },
  { name: 'library', icon: <Folders className="h-5 w-5" /> },
  { name: 'financials', icon: <DollarSign className="h-5 w-5" /> },
  { name: 'territories', icon: <MapPin className="h-5 w-5" /> },
];

const FeatureLocksTab = () => {
  const lastStageRef = useRef<HTMLParagraphElement>(null);

  const { stageList } = useStageStore();
  const [isEdit, setIsEdit] = useState(false);
  const [values, setValues] =
    useState<Record<FeatureKey, number>>(INITIAL_VALUES);
  const [editedValues, setEditedValues] =
    useState<Record<FeatureKey, number>>(INITIAL_VALUES);
  const [lastStageWidth, setLastStageWidth] = useState(0);

  const {
    result: featureLocksResult,
    isError: isFeatureLocksError,
    isSuccess: isFeatureLocksSuccess,
    isLoading: isFeatureLocksLoading,
    isRefetching: isFeatureLocksRefetching,
  } = useGetFeatureLocks();

  const {
    mutate: updateFeatureLocks,
    result: updateFeatureLocksResult,
    isError: isUpdateFeatureLocksError,
    isSuccess: isUpdateFeatureLocksSuccess,
    isLoading: isUpdateFeatureLocksLoading,
  } = useUpdateFeatureLocks();

  const getStageId = (value: number) =>
    value > 0 ? stageList[value - 1].id : null;

  const handleSave = () => {
    updateFeatureLocks({
      aimei: getStageId(editedValues.aimei),
      library: getStageId(editedValues.library),
      financials: getStageId(editedValues.financials),
      territories: getStageId(editedValues.territories),
    });
  };

  const handleCancel = () => {
    setIsEdit(false);
    setEditedValues(values);
  };

  const renderStageHeader = () => {
    return (
      <div className="as-title-04 as-strong flex border-b text-typo-gray-tertiary">
        <div className="w-40 flex-shrink-0 px-4 py-3">Name</div>
        <div className="flex flex-1">
          <p className="w-full py-3 text-center">No set</p>
          {stageList.map((item, index) => (
            <p
              key={item.id}
              ref={index === stageList.length - 1 ? lastStageRef : null}
              className="w-full py-3 text-center"
            >
              {item.name}
            </p>
          ))}
        </div>
      </div>
    );
  };

  const renderStageRow = (stage: StageAccess) => (
    <div key={stage.name} className="flex">
      <div className="as-body-02 as-strong flex w-40 flex-shrink-0 gap-2 px-4 py-6">
        {stage.icon}
        <p>{stage.name.charAt(0).toUpperCase() + stage.name.slice(1)}</p>
      </div>
      <div className="flex flex-1 items-center justify-center">
        <div style={{ width: lastStageWidth / 2 }} className="h-1.5">
          <div className="float-left h-full w-full rounded-r-lg bg-primary" />
        </div>
        <div className="ml-[-10px] mr-[-10px] flex-1">
          <Slider
            max={stageList.length}
            value={[isEdit ? editedValues[stage.name] : values[stage.name]]}
            disabled={!isEdit}
            className="rounded-full bg-secondary"
            onValueChange={([value]) => {
              setEditedValues((prev) => ({ ...prev, [stage.name]: value }));
            }}
          />
        </div>
        <div style={{ width: lastStageWidth / 2 }} className="h-1.5">
          <div className="float-left h-full w-full rounded-r-lg bg-secondary" />
        </div>
      </div>
    </div>
  );

  const updateValuesFromResult = (
    result: typeof featureLocksResult | typeof updateFeatureLocksResult
  ) => {
    if (!result?.data) return;

    const newValues = Object.fromEntries(
      Object.entries(result.data).map(([key, value]) => [
        key,
        stageList.findIndex((stage) => stage.id === value) + 1,
      ])
    ) as Record<FeatureKey, number>;

    setValues(newValues);
    setEditedValues(newValues);
  };

  useEffect(() => {
    if (lastStageRef.current && stageList.length > 0) {
      const elementClientWidth = lastStageRef.current.clientWidth;
      setLastStageWidth(elementClientWidth);
    }
  }, [stageList, lastStageRef.current]);

  useEffect(() => {
    if (isFeatureLocksError || isUpdateFeatureLocksError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isFeatureLocksError, isUpdateFeatureLocksError]);

  useEffect(() => {
    if (isFeatureLocksSuccess) {
      updateValuesFromResult(featureLocksResult);
    }
  }, [featureLocksResult, isFeatureLocksSuccess]);

  useEffect(() => {
    if (isUpdateFeatureLocksSuccess) {
      updateValuesFromResult(updateFeatureLocksResult);
      setIsEdit(false);
    }
  }, [updateFeatureLocksResult, isUpdateFeatureLocksSuccess]);

  return (
    <div className="w-full rounded-md border-border bg-muted">
      <div className="flex items-center justify-between border-b px-6 py-3">
        <div className="flex items-center gap-2">
          <p className="as-title-01 as-strong">Stage of access</p>
          {(isFeatureLocksLoading || isFeatureLocksRefetching) && (
            <Loader2 className="size-4 animate-spin" />
          )}
        </div>
        {!isEdit ? (
          <Button
            size="sm"
            variant="outline"
            className="bg-secondary hover:bg-white"
            onClick={() => setIsEdit(true)}
          >
            <Edit className="h-4 w-4" /> Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              disabled={isUpdateFeatureLocksLoading}
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              type="submit"
              disabled={isUpdateFeatureLocksLoading}
              onClick={handleSave}
            >
              {isUpdateFeatureLocksLoading && (
                <Loader2 className="size-4 animate-spin" />
              )}
              Save Changes
            </Button>
          </div>
        )}
      </div>
      <div className="bg-muted p-4">
        <div className="rounded-md bg-white px-4">
          {renderStageHeader()}
          {STAGES_OF_ACCESS.map(renderStageRow)}
        </div>
      </div>
    </div>
  );
};

export default FeatureLocksTab;
