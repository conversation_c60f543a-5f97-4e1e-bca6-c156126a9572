import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

import { StageItemList } from '@/components/stages/StageItemList';
import StageList from '@/components/stages/StageList';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetStagePrompts } from '@/hooks/stage-prompts/useGetStagePrompts';
import { useGetAllStage } from '@/hooks/stages/useGetAllStages';
import { toast } from '@/hooks/ui/use-toast';
import { useGetAllZorDocuments } from '@/hooks/zor-document/useGetAllZorDocuments';
import useStageStore from '@/stores/stages/useStageStore';
import useStagePromptsStore from '@/stores/useStagePromptsStore';
import useZorDocumentStore from '@/stores/useZorDocumentStore';
import { Stage } from '@/types/stage.type';

import FeatureLocksTab from './FeatureLocksTab';

const DashboardStagesChecklists = () => {
  const [selectedTab, setSelectedTab] = useState<string>('discovery-stages');
  const [selectedStage, setSelectedStage] = useState<Stage | null>(null);

  const {
    result: getAllStagesResult,
    isError: isGetAllStagesError,
    isSuccess: isGetAllStagesSuccess,
    isLoading: isGetAllStagesLoading,
  } = useGetAllStage();
  const { stageList, setStageList } = useStageStore();

  const {
    result: zorDocumentsResult,
    isError: isGetAllZorDocumentsError,
    isSuccess: isGetAllZorDocumentsSuccess,
    isLoading: isGetAllZorDocumentsLoading,
  } = useGetAllZorDocuments();
  const { setZorDocuments } = useZorDocumentStore();

  const {
    result: getStagePromptsResult,
    isError: isGetStagePromptsError,
    isSuccess: isGetStagePromptsSuccess,
    isLoading: isGetStagePromptsLoading,
  } = useGetStagePrompts();
  const { setStagePrompts } = useStagePromptsStore();

  const handelClickTab = (value: string) => {
    setSelectedTab(value);
  };

  useEffect(() => {
    if (isGetAllStagesError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isGetAllStagesError]);

  useEffect(() => {
    if (isGetAllStagesSuccess) {
      setStageList(
        getAllStagesResult?.data.sort(
          (a, b) => (a.index ?? 0) - (b.index ?? 0)
        ) ?? []
      );
    }
  }, [getAllStagesResult, isGetAllStagesSuccess, setStageList]);

  useEffect(() => {
    if (isGetAllZorDocumentsError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isGetAllZorDocumentsError]);

  useEffect(() => {
    if (isGetAllZorDocumentsSuccess && zorDocumentsResult?.data) {
      setZorDocuments(zorDocumentsResult.data);
    }
  }, [zorDocumentsResult, isGetAllZorDocumentsSuccess, setZorDocuments]);

  useEffect(() => {
    if (isGetStagePromptsError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isGetStagePromptsError]);

  useEffect(() => {
    if (isGetStagePromptsSuccess && getStagePromptsResult?.data) {
      setStagePrompts(getStagePromptsResult.data);
    }
  }, [getStagePromptsResult, isGetStagePromptsSuccess, setStagePrompts]);

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <div className="flex w-full flex-col justify-between gap-4 md:flex-row">
        <div className="max-w-md">
          <h2 className="as-strong">Stages & Checklist</h2>
          <p className="as-title-02 mt-2 font-light text-typo-gray-tertiary">
            You can drag and drop the stages and checklists to change their
            place!
          </p>
        </div>
      </div>
      <Tabs
        value={selectedTab}
        className="w-full"
        defaultValue="discovery-stages"
        onValueChange={(value) => handelClickTab(value)}
      >
        <TabsList className="mb-4 w-full md:w-fit">
          <TabsTrigger value="discovery-stages" className="w-full">
            Discovery Stages
          </TabsTrigger>
          <TabsTrigger value="feature-locks" className="w-full">
            Feature Locks
          </TabsTrigger>
        </TabsList>

        <TabsContent value="discovery-stages" className="w-full space-y-2">
          <div className="flex w-full items-center justify-between">
            {isGetAllStagesLoading ||
            isGetStagePromptsLoading ||
            isGetAllZorDocumentsLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="size-4 animate-spin" />
              </div>
            ) : (
              <StageList
                selectedStage={selectedStage}
                setSelectedStage={setSelectedStage}
              />
            )}
          </div>
          {stageList.length > 0 && selectedStage && (
            <StageItemList selectedStage={selectedStage} />
          )}
        </TabsContent>
        <TabsContent value="feature-locks">
          {selectedTab == 'feature-locks' && <FeatureLocksTab />}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardStagesChecklists;
