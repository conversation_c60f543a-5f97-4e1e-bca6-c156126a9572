import { Loader2 } from 'lucide-react';

import { US_STATES } from '@/constants/territories/states';
import { useGetConsultant } from '@/hooks/consultant/useGetConsultant';

import { CONSULTANT_FIRMS } from '../../../constants/consultant-firms';

interface ProfileTabProps {
  consultantId: string;
}

export const ProfileTab = ({ consultantId }: ProfileTabProps) => {
  const { result, isLoading, isError } = useGetConsultant(consultantId);

  if (isLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError || !result?.data) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <div className="text-center">
          <p className="text-red-500">Failed to load consultant profile</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  const consultant = result.data;

  return (
    <div className="space-y-6">
      {/* Contact Details Section */}
      <div className="rounded-xl border border-alpha-dark-5 bg-white p-6">
        <h3 className="as-title-02 as-strong mb-6 font-medium text-zinc-700">
          Contact Details
        </h3>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Full Name
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.full_name || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Consultant Firm Affiliated
            </label>
            {consultant.consultant_firm_affiliated &&
            consultant.consultant_firm_affiliated.length > 0 ? (
              <div className="mt-2 flex flex-wrap gap-2">
                {consultant.consultant_firm_affiliated.map(
                  (firmKey: string, index: number) => (
                    <span
                      key={index}
                      className="rounded-md bg-blue-100 px-3 py-1 text-sm font-medium text-blue-600"
                    >
                      {CONSULTANT_FIRMS[
                        firmKey as keyof typeof CONSULTANT_FIRMS
                      ] || firmKey}
                    </span>
                  )
                )}
              </div>
            ) : (
              <p className="text-typo-gray-secondary">N/A</p>
            )}
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Email
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.email || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Phone
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.phone || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              City
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.city || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              State
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.state || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Zip Code
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.zip_code || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Address
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.address || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Meeting Link
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.meeting_link ? (
                <a
                  href={consultant.meeting_link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-brand-600 underline hover:text-brand-700"
                >
                  {consultant.meeting_link}
                </a>
              ) : (
                'N/A'
              )}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              LinkedIn
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.linkedin ? (
                <a
                  href={consultant.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-brand-600 underline hover:text-brand-700"
                >
                  {consultant.linkedin}
                </a>
              ) : (
                'N/A'
              )}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Website
            </label>
            <p className="text-typo-gray-secondary">
              {consultant.website ? (
                <a
                  href={consultant.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-brand-600 underline hover:text-brand-700"
                >
                  {consultant.website}
                </a>
              ) : (
                'N/A'
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Preferred Markets Section */}
      <div className="rounded-xl border border-alpha-dark-5 bg-white p-6">
        <h3 className="as-title-02 as-strong mb-4 text-zinc-700">
          Preferred Markets
        </h3>
        <div className="mb-4">
          <label className="as-title-03 text-typo-gray-quaternary">
            Markets
          </label>
          {consultant.preferred_markets &&
          consultant.preferred_markets.length > 0 ? (
            <div className="mt-2 flex flex-wrap gap-2">
              {consultant.preferred_markets.map((market, index) => (
                <span
                  key={index}
                  className="rounded-md bg-brand-100 px-3 py-1 text-sm font-medium text-brand-600"
                >
                  {US_STATES[market as keyof typeof US_STATES]}
                </span>
              ))}
            </div>
          ) : (
            <p className="as-body-03 text-typo-gray-secondary">
              No preferred markets specified
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
