import { ChevronLeft } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetConsultant } from '@/hooks/consultant/useGetConsultant';

import { IndustriesTab } from './IndustriesTab';
import { ProfileTab } from './ProfileTab';

type TabValue = 'profile' | 'industries';

const TAB_OPTIONS: { value: TabValue; label: string }[] = [
  { value: 'profile', label: 'Profile' },
  { value: 'industries', label: 'Industries' },
];

const DEFAULT_TAB: TabValue = 'profile';

export const ConsultantProfile = () => {
  const { consultantId } = useParams<{ consultantId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedTab, setSelectedTab] = useState<TabValue>(DEFAULT_TAB);

  const { result } = useGetConsultant(consultantId || '');
  const consultant = result?.data;

  const goBack = () => {
    navigate(`/dashboard/data-center?tab=consultant`);
  };

  const handleGoBackClick = (): void => {
    goBack();
  };

  const handleClickTab = (value: TabValue): void => {
    setSelectedTab(value);
    navigate(`/dashboard/consultant-profile/${consultantId}?tab=${value}`);
  };

  const renderUserAvatar = () => {
    if (consultant?.photo_url) {
      return (
        <img
          src={consultant.photo_url}
          alt={consultant.full_name || 'Consultant avatar'}
          className="h-[90px] w-[90px] rounded-full border-2 border-white shadow-lg"
        />
      );
    }
    return (
      <EmptyAvatar className="h-[90px] w-[90px] rounded-full border-2 border-white shadow-lg" />
    );
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const newDefaultTab = (queryParams.get('tab') as TabValue) || DEFAULT_TAB;
    setSelectedTab(newDefaultTab);
  }, [location]);

  if (!consultantId) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500">Invalid consultant ID</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex w-full items-center justify-between gap-5">
        <Button
          size="icon"
          variant="ghost"
          className="flex-shrink-0"
          onClick={handleGoBackClick}
        >
          <ChevronLeft className="cursor-pointer" />
        </Button>
        <div className="flex w-full flex-col justify-center md:flex-row md:justify-between">
          <div className="flex flex-col items-center gap-5 md:flex-row">
            {renderUserAvatar()}
            <div className="space-y-1 max-md:text-center">
              <h2 className="as-strong">{consultant?.full_name || '####'}</h2>
              <p className="as-body-02 as-strong text-muted-foreground">
                {consultant?.email || '####'}
              </p>
            </div>
          </div>
        </div>
      </div>
      <Tabs
        value={selectedTab}
        onValueChange={(value) => handleClickTab(value as TabValue)}
      >
        <TabsList className="mb-4 w-full md:w-fit">
          {TAB_OPTIONS.map(({ value, label }) => (
            <TabsTrigger key={value} value={value} className="w-full">
              {label}
            </TabsTrigger>
          ))}
        </TabsList>
        <TabsContent
          value="profile"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <ProfileTab consultantId={consultantId} />
        </TabsContent>
        <TabsContent
          value="industries"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <IndustriesTab consultantId={consultantId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
