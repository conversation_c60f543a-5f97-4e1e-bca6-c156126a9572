import { Loader2 } from 'lucide-react';

import {
  currentWorkingBrandsOptions,
  IndustryOptions,
} from '@/constants/options';
import { useGetConsultantIndustries } from '@/hooks/consultant/useGetConsultantIndustries';

interface IndustriesTabProps {
  consultantId: string;
}

export const IndustriesTab = ({ consultantId }: IndustriesTabProps) => {
  const { result, isLoading, isError } =
    useGetConsultantIndustries(consultantId);

  if (isLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError || !result?.data) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <div className="text-center">
          <p className="text-red-500">Failed to load consultant industries</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  const consultantIndustries = result.data;

  return (
    <div className="space-y-6">
      {/* Industry Background Section */}
      <div className="rounded-xl border border-alpha-dark-5 bg-white p-6">
        <h3 className="as-title-02 as-strong mb-4 text-zinc-700">
          Industry Background
        </h3>
        <div className="mb-4">
          <label className="as-title-03 text-typo-gray-quaternary">
            Industries
          </label>
          {consultantIndustries.industry_background &&
          consultantIndustries.industry_background.length > 0 ? (
            <div className="mt-2 flex flex-wrap gap-2">
              {consultantIndustries.industry_background.map(
                (industry, index) => (
                  <span
                    key={index}
                    className="rounded-md bg-brand-100 px-3 py-1 text-sm font-medium text-brand-600"
                  >
                    {IndustryOptions.find((option) => option.value === industry)
                      ?.label || 'N/A'}
                  </span>
                )
              )}
            </div>
          ) : (
            <p className="text-typo-gray-secondary">No industries specified</p>
          )}
        </div>
      </div>

      {/* Franchise Sales Organizations (FSOs) Section */}
      <div className="rounded-xl border border-alpha-dark-5 bg-white p-6">
        <h3 className="as-title-02 as-strong mb-4 text-zinc-700">
          Franchise Sales Organizations (FSOs)
        </h3>
        <div className="grid grid-cols-1 gap-6">
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Brands Currently Working With
            </label>
            <p className="text-typo-gray-secondary">
              {currentWorkingBrandsOptions.find(
                (option) =>
                  option.value === consultantIndustries.current_working_brands
              )?.label || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Do you currently work with any FSOs?
            </label>
            <p className="text-typo-gray-secondary">
              {consultantIndustries.have_any_fsos || 'N/A'}
            </p>
          </div>
          <div>
            <label className="as-title-03 text-typo-gray-quaternary">
              Detailed Answer
            </label>
            <div className="mt-2">
              {consultantIndustries.detailed_answer ? (
                <div className="rounded-md bg-gray-50 p-3">
                  <p className="whitespace-pre-wrap text-typo-gray-secondary">
                    {consultantIndustries.detailed_answer}
                  </p>
                </div>
              ) : (
                <p className="text-typo-gray-secondary">
                  No detailed answer provided
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
