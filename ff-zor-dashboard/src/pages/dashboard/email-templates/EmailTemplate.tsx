import { ArrowLeftIcon, ChevronRight, Loader2 } from 'lucide-react';
import { Link, useNavigate, useParams } from 'react-router-dom';

import EmailTemplateForm from '@/components/forms/email-templates/EmailTemplateForm';
import { Button } from '@/components/ui/button';
import { useGetEmailTemplate } from '@/hooks/email-templates/useGetEmailTemplate';

const DashboardEmailTemplate = () => {
  const navigate = useNavigate();

  const params = useParams();
  const id = params.id as string;

  const {
    result: getEmailTemplateResult,
    isError: getEmailTemplateIsError,
    isLoading: getEmailTemplateIsLoading,
  } = useGetEmailTemplate(id);

  if (getEmailTemplateIsLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (getEmailTemplateIsError || !getEmailTemplateResult?.data) {
    return (
      <div className="flex h-full flex-col items-center justify-center space-y-4">
        <p className="as-body-03 as-strong text-zinc-500">
          Something went wrong
        </p>
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard/settings?tab=email-settings')}
        >
          Go back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-center space-y-8">
        <div className="flex flex-col items-center gap-5 md:flex-row">
          <div className="space-y-4">
            <Link
              to="/dashboard/settings?tab=email-settings"
              className="inline-flex flex-row items-center space-x-4 text-sm text-zinc-500 hover:underline"
            >
              <ArrowLeftIcon className="mr-2 size-4" /> Emails
            </Link>
            <h2 className="as-strong flex flex-row items-center space-x-4 text-2xl">
              Email Template
              <ChevronRight className="ml-4 size-6" />
              <p className="font-normal text-zinc-600">
                {getEmailTemplateResult?.data?.name}
              </p>
            </h2>
          </div>
        </div>
        <div className="rounded-xl border border-alpha-dark-5 bg-muted p-4">
          <EmailTemplateForm emailTemplate={getEmailTemplateResult?.data} />
        </div>
      </div>
    </div>
  );
};

export default DashboardEmailTemplate;
