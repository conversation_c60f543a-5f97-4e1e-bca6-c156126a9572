import { useOrganizationList } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { CompanyDetailsForm } from '@/components/forms/profile/CompanyDetailForm';
import { FinancialForm } from '@/components/forms/profile/FinancialForm';
import CompanyFormSchema from '@/components/forms/schemas/CompanyFormSchema';
import FinancialFormSchema from '@/components/forms/schemas/FinancialFormSchema';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useCreateZor } from '@/hooks/zor/useCreateZor';
import { useGetZor } from '@/hooks/zor/useGetZor';
import { useUpdateZor } from '@/hooks/zor/useUpdateZor';
import { getLocalDateFromUTC } from '@/lib/date';
import useZorStore from '@/stores/useZorStore';

interface InformationTabProps {
  onUnsavedChangesUpdate?: (
    hasChanges: boolean,
    saveFunction?: () => void
  ) => void;
}

const InformationTab = ({ onUnsavedChangesUpdate }: InformationTabProps) => {
  const { isLoaded, setActive } = useOrganizationList();
  const { zor, zorFinancial, setZor, setZorFinancial } = useZorStore();

  const companyForm = useForm<z.infer<typeof CompanyFormSchema>>({
    resolver: zodResolver(CompanyFormSchema),
    defaultValues: {},
  });

  const financialForm = useForm<z.infer<typeof FinancialFormSchema>>({
    resolver: zodResolver(FinancialFormSchema),
    defaultValues: {
      is_sba_eligible: false,
      is_veteran_discount: false,
    },
  });

  const {
    result: zorResult,
    isError: isZorError,
    isSuccess: isZorSuccess,
    isLoading: isZorLoading,
    isRefetching: isZorRefetching,
  } = useGetZor();

  const {
    mutate: createZor,
    result: createZorResult,
    isError: isCreateZorError,
    isSuccess: isCreateZorSuccess,
    isLoading: isCreateZorLoading,
  } = useCreateZor();

  const {
    mutate: updateZor,
    result: updateZorResult,
    isError: isUpdateZorError,
    isSuccess: isUpdateZorSuccess,
    isLoading: isUpdateZorLoading,
  } = useUpdateZor();

  const resetForms = () => {
    companyForm.reset({
      ...zor,
      franchise_since: zor?.franchise_since?.toString() ?? '',
      fdd_renewal_date: zor?.fdd_renewal_date
        ? getLocalDateFromUTC(zor.fdd_renewal_date)
        : undefined,
    });
    financialForm.reset({ ...zorFinancial });
  };

  const handleSubmit = async () => {
    const [isCompanyFormValid, isFinancialFormValid] = await Promise.all([
      companyForm.trigger(),
      financialForm.trigger(),
    ]);

    if (!isCompanyFormValid || !isFinancialFormValid) return;

    const companyData = companyForm.getValues();
    const submitData = {
      ...companyData,
      ...financialForm.getValues(),
      slug: companyData.name.toLowerCase().replace(/ /g, '-'),
      franchise_since: Number(companyData.franchise_since),
    };

    if (zor) {
      updateZor(submitData);
    } else {
      createZor(submitData);
    }
  };

  useEffect(() => {
    resetForms();
  }, [zor, zorFinancial]);

  useEffect(() => {
    const hasChanges =
      companyForm.formState.isDirty || financialForm.formState.isDirty;
    onUnsavedChangesUpdate?.(hasChanges, hasChanges ? handleSubmit : undefined);
  }, [
    companyForm.formState.isDirty,
    financialForm.formState.isDirty,
    onUnsavedChangesUpdate,
    handleSubmit,
  ]);

  useEffect(() => {
    const hasError = isZorError || isCreateZorError || isUpdateZorError;
    if (hasError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isZorError, isCreateZorError, isUpdateZorError]);

  useEffect(() => {
    if (!isZorSuccess || !zorResult?.data) return;

    const { zor_profile, zor_financial } = zorResult.data;
    if (zor_profile) setZor({ ...zor_profile });
    if (zor_financial) setZorFinancial({ ...zor_financial });
  }, [zorResult, isZorSuccess, setZor, setZorFinancial]);

  useEffect(() => {
    if (!isCreateZorSuccess && !isUpdateZorSuccess) return;

    const result = createZorResult?.data || updateZorResult?.data;
    if (!result) return;

    const { zor_profile, zor_financial } = result;

    if (isCreateZorSuccess && isLoaded && zor_profile) {
      setActive({ organization: zor_profile.id });
    }

    setZor({ ...zor_profile });
    setZorFinancial({ ...zor_financial });

    companyForm.reset({
      ...zor_profile,
      franchise_since: zor_profile?.franchise_since?.toString() ?? '',
      fdd_renewal_date: zor_profile?.fdd_renewal_date
        ? getLocalDateFromUTC(zor_profile.fdd_renewal_date)
        : undefined,
    });
    financialForm.reset({ ...zor_financial });
    onUnsavedChangesUpdate?.(false);
    toast({ description: 'Changes saved successfully' });
  }, [
    isCreateZorSuccess,
    isUpdateZorSuccess,
    createZorResult,
    updateZorResult,
    setZor,
    setZorFinancial,
    companyForm,
    financialForm,
    onUnsavedChangesUpdate,
  ]);

  if (isZorLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  const isLoading = isCreateZorLoading || isUpdateZorLoading;
  const showRefetchingLoader = isZorRefetching;

  return (
    <div className="relative space-y-1.5">
      {isLoading && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-100/50 backdrop-blur-sm">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      {showRefetchingLoader && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      <CompanyDetailsForm form={companyForm} />
      <FinancialForm form={financialForm} />

      <div className="flex w-full justify-end">
        <Button
          size="sm"
          type="button"
          className="w-full md:w-fit"
          onClick={handleSubmit}
        >
          Save Change
        </Button>
      </div>
    </div>
  );
};

export default InformationTab;
