import { useOrganization, useUser } from '@clerk/clerk-react';
import { Plus } from 'lucide-react';
import { useState } from 'react';

import InviteUserDialog from '@/components/dialogs/InviteUserDialog';
import UsersTable, {
  OrganizationMembershipResource,
} from '@/components/tables/UsersTable';
import { Button } from '@/components/ui/button';

const UserProfilesTab = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { user } = useUser();
  const { organization, memberships, invitations } = useOrganization({
    invitations: { infinite: true, keepPreviousData: true },
    memberships: { infinite: true, keepPreviousData: true },
  });

  const canManageUsers = user?.organizationMemberships?.some((membership) =>
    membership.role.includes('leadership')
  );

  const users = [
    ...(invitations?.data
      ?.filter((invitation) => invitation.role !== 'org:zee')
      .map((invitation) => ({
        id: invitation.id,
        name: `N/A`,
        email: invitation.emailAddress,
        status: 'pending',
        userType: invitation.role.includes('leadership')
          ? 'leadership'
          : 'partner',
        permissionLevel: invitation.role.includes('owner')
          ? 'owner'
          : invitation.role.includes('administrator')
            ? 'administrator'
            : 'consultant',
      })) || []),
    ...(memberships?.data
      ?.filter((member) => member.role !== 'org:zee')
      .map((member) => ({
        id: member.id,
        name:
          `${member.publicUserData?.firstName || ''} ${
            member.publicUserData?.lastName || ''
          }`.trim() || 'N/A',
        email: member.publicUserData?.identifier,
        imageUrl: member.publicUserData?.imageUrl,
        userType: member.role.includes('leadership') ? 'leadership' : 'partner',
        permissionLevel: member.role.includes('owner')
          ? 'owner'
          : member.role.includes('administrator')
            ? 'administrator'
            : 'consultant',
      })) || []),
  ];

  const handleRefetchUsers = async () => {
    window.location.reload();
  };

  return (
    <div className="gap-6 space-y-4 rounded-xl">
      {organization && (
        <>
          <div className="flex items-center justify-between border-b p-2">
            <p className="as-title-01">Users</p>
            {canManageUsers && (
              <div className="flex gap-2">
                <Button size="sm" onClick={() => setIsOpen(true)}>
                  <Plus />
                  Invite user
                </Button>
              </div>
            )}
          </div>
          <div className="w-full rounded-xl bg-white p-6">
            <UsersTable
              memberships={users as OrganizationMembershipResource[]}
              canRevokeInvitations={canManageUsers || false}
              onRefetch={handleRefetchUsers}
            />
          </div>
          <InviteUserDialog
            open={isOpen}
            setOpen={setIsOpen}
            onSuccess={handleRefetchUsers}
          />
        </>
      )}
      {!organization && (
        <div className="flex min-h-20 items-center justify-center">
          <p className="as-body-02 text-muted-foreground">
            You are not a member of any organization
          </p>
        </div>
      )}
    </div>
  );
};

export default UserProfilesTab;
