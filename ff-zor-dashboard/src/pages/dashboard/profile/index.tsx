import { useOrganization } from '@clerk/clerk-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import DefaultCompanyLogo from '@/assets/icons/default-company-logo.svg?react';
import UnsavedChangesDialog from '@/components/dialogs/UnsavedChangesDialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import FinancialTab from './FinancialTab';
import InformationTab from './InformationTab';
import MyProfileTab from './MyProfileTab';
import UserProfilesTab from './UserProfilesTab';

const DashboardProfile = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { organization } = useOrganization();

  const [selectedTab, setSelectedTab] = useState<string>();
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingTab, setPendingTab] = useState<string | null>(null);

  const hasUnsavedChangesRef = useRef<() => boolean>(() => false);
  // remove save-on-leave pattern; manual save only
  const saveCurrentTabRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const newDefaultTab = queryParams.get('tab') || 'information';
    setSelectedTab(newDefaultTab);
  }, [location]);

  const checkUnsavedChanges = useCallback(() => {
    return hasUnsavedChangesRef.current();
  }, []);

  const handleTabClick = (value: string) => {
    if (checkUnsavedChanges()) {
      setPendingTab(value);
      setShowUnsavedDialog(true);
    } else {
      setSelectedTab(value);
      navigate(`/dashboard/profile?tab=${value}`);
    }
  };

  const handleDiscardAndLeave = () => {
    if (pendingTab) {
      hasUnsavedChangesRef.current = () => false;
      setSelectedTab(pendingTab);
      navigate(`/dashboard/profile?tab=${pendingTab}`);
    }
    setShowUnsavedDialog(false);
    setPendingTab(null);
  };

  const handleUnsavedChangesUpdate = useCallback(
    (tabName: string) => {
      return (hasChanges: boolean, saveFunction?: () => void) => {
        if (selectedTab === tabName) {
          hasUnsavedChangesRef.current = () => hasChanges;
          saveCurrentTabRef.current = saveFunction || null;
        }
      };
    },
    [selectedTab]
  );

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-center md:flex-row md:justify-between">
        <div className="flex flex-col items-center gap-5 md:flex-row">
          {organization?.imageUrl ? (
            <img
              src={organization?.imageUrl}
              alt={organization?.name || 'Company logo'}
              className="h-[90px] w-[90px] rounded-full border-2 border-white shadow-lg"
            />
          ) : (
            <DefaultCompanyLogo className="h-[90px] w-[90px] rounded-full border-2 border-white shadow-lg" />
          )}
          <div className="space-y-1">
            <h2 className="as-strong">
              {organization?.name || 'Company name'}
            </h2>
            {organization?.publicMetadata?.website ? (
              <Link to={organization?.publicMetadata?.website} target="_blank">
                <p className="as-body-02 as-strong text-typo-gray-tertiary">
                  {(organization?.publicMetadata?.website as string) ||
                    'Company website'}
                </p>
              </Link>
            ) : (
              <p className="as-body-02 as-strong text-typo-gray-tertiary">
                {(organization?.publicMetadata?.website as string) ||
                  'Company website'}
              </p>
            )}
          </div>
        </div>
      </div>
      <Tabs value={selectedTab} onValueChange={handleTabClick}>
        <TabsList className="mb-4 w-full md:w-fit">
          <TabsTrigger value="information" className="w-full">
            Information
          </TabsTrigger>
          <TabsTrigger value="financial" className="w-full">
            Financial
          </TabsTrigger>
          <TabsTrigger value="user-profiles" className="w-full">
            User Profiles
          </TabsTrigger>
          <TabsTrigger value="my-profile" className="w-full">
            My Profile
          </TabsTrigger>
        </TabsList>
        <TabsContent
          value="information"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <InformationTab
            onUnsavedChangesUpdate={handleUnsavedChangesUpdate('information')}
          />
        </TabsContent>
        <TabsContent
          value="financial"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <FinancialTab
            onUnsavedChangesUpdate={handleUnsavedChangesUpdate('financial')}
          />
        </TabsContent>
        <TabsContent
          value="user-profiles"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <UserProfilesTab />
        </TabsContent>
        <TabsContent
          value="my-profile"
          className="rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <MyProfileTab
            onUnsavedChangesUpdate={handleUnsavedChangesUpdate('my-profile')}
          />
        </TabsContent>
      </Tabs>

      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={() => setShowUnsavedDialog(false)}
        onConfirm={handleDiscardAndLeave}
      />
    </div>
  );
};

export default DashboardProfile;
