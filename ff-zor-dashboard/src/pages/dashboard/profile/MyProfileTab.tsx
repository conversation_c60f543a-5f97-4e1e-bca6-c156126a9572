import { useClerk, useUser } from '@clerk/clerk-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, TriangleAlert } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import DeleteAccountDialog from '@/components/dialogs/DeleteAccountDialog';
import { MyProfileForm } from '@/components/forms/profile/MyProfileForm';
import { ResetPasswordForm } from '@/components/forms/profile/ResetPasswordForm';
import MyProfileFormSchema from '@/components/forms/schemas/MyProfileFormSchema';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useDeleteUser } from '@/hooks/user/useDeleteUser';
import { useCreateZorMember } from '@/hooks/zor-member/useCreateZorMember';
import { useGetZorMember } from '@/hooks/zor-member/useGetZorMember';
import { useUpdateZorMember } from '@/hooks/zor-member/useUpdateZorMember';
import useZorMemberStore from '@/stores/useZorMemberStore';

interface MyProfileTabProps {
  onUnsavedChangesUpdate?: (
    hasChanges: boolean,
    saveFunction?: () => void
  ) => void;
}

const MyProfileTab = ({ onUnsavedChangesUpdate }: MyProfileTabProps) => {
  const { zorMember, setZorMember } = useZorMemberStore();

  const myProfileForm = useForm<z.infer<typeof MyProfileFormSchema>>({
    resolver: zodResolver(MyProfileFormSchema),
    defaultValues: {},
  });

  const {
    result: zorMemberResult,
    isError: isZorMemberError,
    isSuccess: isZorMemberSuccess,
    isLoading: isZorMemberLoading,
    isRefetching: isZorMemberRefetching,
  } = useGetZorMember();

  const {
    mutate: createZorMember,
    result: createZorMemberResult,
    isError: isCreateZorMemberError,
    isSuccess: isCreateZorMemberSuccess,
    isLoading: isCreateZorMemberLoading,
  } = useCreateZorMember();

  const {
    mutate: updateZorMember,
    result: updateZorMemberResult,
    isError: isUpdateZorMemberError,
    isSuccess: isUpdateZorMemberSuccess,
    isLoading: isUpdateZorMemberLoading,
  } = useUpdateZorMember();

  const resetForms = () => {
    myProfileForm.reset({ ...zorMember });
  };

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { user } = useUser();
  const { signOut } = useClerk();
  const { mutate: deleteUser } = useDeleteUser();

  const handleDeleteAccount = async () => {
    try {
      setIsDeleting(true);
      await deleteUser(user?.id || '');
      toast({ description: 'Account deleted successfully. Signing out…' });
      setTimeout(() => {
        signOut({ redirectUrl: '/auth/sign-in' });
      }, 800);
    } catch {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSubmit = async () => {
    const isValid = await myProfileForm.trigger();
    if (!isValid) return;

    const formData = myProfileForm.getValues();
    if (zorMember) {
      updateZorMember(formData);
    } else {
      createZorMember(formData);
    }
  };

  useEffect(() => {
    resetForms();
  }, [zorMember]);

  useEffect(() => {
    const hasChanges = myProfileForm.formState.isDirty;
    onUnsavedChangesUpdate?.(hasChanges, hasChanges ? handleSubmit : undefined);
  }, [myProfileForm.formState.isDirty, onUnsavedChangesUpdate, handleSubmit]);

  useEffect(() => {
    const hasError =
      isZorMemberError || isCreateZorMemberError || isUpdateZorMemberError;
    if (hasError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isZorMemberError, isCreateZorMemberError, isUpdateZorMemberError]);

  useEffect(() => {
    if (isZorMemberSuccess && zorMemberResult?.data) {
      setZorMember({ ...zorMemberResult.data });
    }
  }, [zorMemberResult, isZorMemberSuccess, setZorMember]);

  useEffect(() => {
    if (!isCreateZorMemberSuccess && !isUpdateZorMemberSuccess) return;

    const newData = createZorMemberResult?.data || updateZorMemberResult?.data;
    if (newData) {
      setZorMember({ ...newData });
      myProfileForm.reset({ ...newData });
      onUnsavedChangesUpdate?.(false);
      toast({ description: 'Changes saved successfully' });
    }
  }, [
    createZorMemberResult,
    updateZorMemberResult,
    isCreateZorMemberSuccess,
    isUpdateZorMemberSuccess,
    setZorMember,
    myProfileForm,
    onUnsavedChangesUpdate,
  ]);

  if (isZorMemberLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  const isLoading = isCreateZorMemberLoading || isUpdateZorMemberLoading;
  const showRefetchingLoader = isZorMemberRefetching;

  return (
    <div className="relative space-y-1.5">
      {isLoading && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-100/50 backdrop-blur-sm">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      {showRefetchingLoader && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      <MyProfileForm form={myProfileForm} />
      <ResetPasswordForm />

      <div className="flex flex-col items-center justify-between gap-6 rounded-xl border border-border bg-white p-6 md:flex-row">
        <div className="flex gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-md bg-red-100 p-2">
            <TriangleAlert color="red" className="h-4 w-4" />
          </div>
          <div className="as-title-03 as-strong text-zinc-700">
            Delete account
            <p className="as-body-04">
              Deleting this account is permanent and cannot be undone. Please
              contact the support team to recover your account.
            </p>
          </div>
        </div>
        <Button
          className="w-full bg-red-100 text-pink-strong md:w-fit"
          onClick={() => setIsDeleteDialogOpen(true)}
        >
          Delete account
        </Button>
      </div>

      <div className="flex w-full justify-end">
        <Button size="sm" className="w-full md:w-fit" onClick={handleSubmit}>
          Save Change
        </Button>
      </div>

      <DeleteAccountDialog
        open={isDeleteDialogOpen}
        loading={isDeleting}
        onCancel={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteAccount}
      />
    </div>
  );
};

export default MyProfileTab;
