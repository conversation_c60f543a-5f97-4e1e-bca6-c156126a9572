import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { BillingForm } from '@/components/forms/profile/BillingForm';
import { PaymentForm } from '@/components/forms/profile/PaymentForm';
import BillingFormSchema from '@/components/forms/schemas/BillingFormSchema';
import { CardListFormSchema } from '@/components/forms/schemas/PaymentFormSchema';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useCreateZorFinancial } from '@/hooks/zor-financial/useCreateZorFinancial';
import { useGetZorFinancial } from '@/hooks/zor-financial/useGetZorFinancial';
import { useUpdateZorFinancial } from '@/hooks/zor-financial/useUpdateZorFinancial';
import useZorStore from '@/stores/useZorStore';

interface FinancialTabProps {
  onUnsavedChangesUpdate?: (
    hasChanges: boolean,
    saveFunction?: () => void
  ) => void;
}

const FinancialTab = ({ onUnsavedChangesUpdate }: FinancialTabProps) => {
  const {
    zorBillingDetails,
    zorPaymentDetails,
    setZorBillingDetails,
    setZorPaymentDetails,
  } = useZorStore();

  const billingForm = useForm<z.infer<typeof BillingFormSchema>>({
    resolver: zodResolver(BillingFormSchema),
    defaultValues: {},
  });

  const paymentForm = useForm<z.infer<typeof CardListFormSchema>>({
    resolver: zodResolver(CardListFormSchema),
    defaultValues: {},
  });

  const {
    result: zorFinancialResult,
    isError: isZorFinancialError,
    isSuccess: isZorFinancialSuccess,
    isLoading: isZorFinancialLoading,
    isRefetching: isZorFinancialRefetching,
  } = useGetZorFinancial();

  const {
    mutate: createZorFinancial,
    result: createZorFinancialResult,
    isError: isCreateZorFinancialError,
    isSuccess: isCreateZorFinancialSuccess,
    isLoading: isCreateZorFinancialLoading,
  } = useCreateZorFinancial();

  const {
    mutate: updateZorFinancial,
    result: updateZorFinancialResult,
    isError: isUpdateZorFinancialError,
    isSuccess: isUpdateZorFinancialSuccess,
    isLoading: isUpdateZorFinancialLoading,
  } = useUpdateZorFinancial();

  const resetForms = () => {
    billingForm.reset({ ...zorBillingDetails });
    paymentForm.reset({ cardData: [...(zorPaymentDetails ?? [])] });
  };

  const handleSubmit = async () => {
    const [isBillingFormValid, isPaymentFormValid] = await Promise.all([
      billingForm.trigger(),
      paymentForm.trigger(),
    ]);

    if (!isBillingFormValid || !isPaymentFormValid) return;

    const submitData = {
      ...billingForm.getValues(),
      payment_details: paymentForm.getValues('cardData'),
    };

    const action =
      !zorBillingDetails && !zorPaymentDetails
        ? createZorFinancial
        : updateZorFinancial;

    action(submitData);
  };

  useEffect(() => {
    resetForms();
  }, [zorBillingDetails, zorPaymentDetails]);

  useEffect(() => {
    const hasChanges =
      billingForm.formState.isDirty || paymentForm.formState.isDirty;
    onUnsavedChangesUpdate?.(hasChanges, hasChanges ? handleSubmit : undefined);
  }, [
    billingForm.formState.isDirty,
    paymentForm.formState.isDirty,
    onUnsavedChangesUpdate,
    handleSubmit,
  ]);

  useEffect(() => {
    if (!isZorFinancialError) return;

    toast({
      title: 'Something went wrong',
      description: 'Please try again',
      variant: 'destructive',
    });
  }, [isZorFinancialError]);

  useEffect(() => {
    if (!isCreateZorFinancialError && !isUpdateZorFinancialError) return;

    toast({
      title: 'Something went wrong',
      description: 'Please try again',
      variant: 'destructive',
    });
  }, [isCreateZorFinancialError, isUpdateZorFinancialError]);

  useEffect(() => {
    if (!isZorFinancialSuccess || !zorFinancialResult?.data) return;

    const { zor_billing_detail, zor_payment_details } = zorFinancialResult.data;

    if (zor_billing_detail) {
      setZorBillingDetails({ ...zor_billing_detail });
    }

    setZorPaymentDetails([...(zor_payment_details ?? [])]);
  }, [
    zorFinancialResult,
    isZorFinancialSuccess,
    setZorBillingDetails,
    setZorPaymentDetails,
  ]);

  useEffect(() => {
    if (!isCreateZorFinancialSuccess && !isUpdateZorFinancialSuccess) return;

    const result =
      createZorFinancialResult?.data || updateZorFinancialResult?.data;
    if (!result) return;

    const { zor_billing_detail, zor_payment_details } = result;
    setZorBillingDetails({ ...zor_billing_detail });
    setZorPaymentDetails([...(zor_payment_details ?? [])]);

    billingForm.reset({ ...zor_billing_detail });
    paymentForm.reset({ cardData: [...(zor_payment_details ?? [])] });
    onUnsavedChangesUpdate?.(false);
    toast({ description: 'Changes saved successfully' });
  }, [
    createZorFinancialResult,
    updateZorFinancialResult,
    isCreateZorFinancialSuccess,
    isUpdateZorFinancialSuccess,
    setZorBillingDetails,
    setZorPaymentDetails,
    billingForm,
    paymentForm,
    onUnsavedChangesUpdate,
  ]);

  if (isZorFinancialLoading) {
    return (
      <div className="flex h-full w-full flex-1 items-center justify-center">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  const isLoading = isCreateZorFinancialLoading || isUpdateZorFinancialLoading;
  const showRefetchingLoader = isZorFinancialRefetching;

  return (
    <div className="relative space-y-1.5">
      {isLoading && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-gray-100/50 backdrop-blur-sm">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      {showRefetchingLoader && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="size-4 animate-spin" />
        </div>
      )}

      <BillingForm form={billingForm} />
      <PaymentForm form={paymentForm} />

      <div className="flex w-full justify-end">
        <Button size="sm" className="w-full md:w-fit" onClick={handleSubmit}>
          Save Change
        </Button>
      </div>
    </div>
  );
};

export default FinancialTab;
