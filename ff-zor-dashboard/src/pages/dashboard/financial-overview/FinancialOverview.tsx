import { Download, Loader2, Unplug } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Document, Page } from 'react-pdf';
import { useLocation, useNavigate } from 'react-router-dom';

import UploadFDDDialog from '@/components/dialogs/UploadFDDDialog';
import UploadWorkbookDialog from '@/components/dialogs/UploadWorkbookDialog';
import DocumentDetailIcon from '@/components/global/DocumentDetailIcon';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetFdd } from '@/hooks/fdd/useGetFdd';
import { useUpdateFdd } from '@/hooks/fdd/useUpdateFdd';
import { toast } from '@/hooks/ui/use-toast';
import { useGetWorkbook } from '@/hooks/workbook/useGetWorkbook';
import useFddStore from '@/stores/useFddStore';
import useWorkbookStore from '@/stores/useWorkbookStore';

import FinancialProjectionTab from './FinancialProjectionTab';

const DashboardFinancialOverview = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const containerRef = useRef<HTMLDivElement>(null);

  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [selectedTab, setSelectedTab] = useState<string>();
  const [uploadFDDOpen, setUploadFDDOpen] = useState(false);
  const [uploadWorkbookOpen, setUploadWorkbookOpen] = useState(false);

  const { fdd, setFdd } = useFddStore();
  const { workbook, setWorkbook } = useWorkbookStore();

  const {
    result: fddResult,
    isError: isFddError,
    isSuccess: isFddSuccess,
    isLoading: isFddLoading,
  } = useGetFdd();

  const {
    result: workbookResult,
    isError: isWorkbookError,
    isSuccess: isWorkbookSuccess,
  } = useGetWorkbook();

  const {
    mutate: updateFdd,
    result: updateFddResult,
    isError: isUpdateFddError,
    isSuccess: isUpdateFddSuccess,
    isLoading: isUpdateFddLoading,
  } = useUpdateFdd();

  const handelClickTab = (value: string) => {
    setSelectedTab(value);
    navigate(`/dashboard/financial-overview?tab=${value}`);
  };

  const goToPinPage = () => {
    if (selectedTab && fdd && containerRef.current) {
      const pageIndex = fdd[selectedTab as keyof typeof fdd];
      if (typeof pageIndex === 'number' && pageIndex > 0) {
        const container = containerRef.current;
        const pageHeight = container.scrollHeight / numPages;
        const scrollPosition = (pageIndex - 1) * pageHeight;
        container.scrollTo({
          top: scrollPosition,
          behavior: 'smooth',
        });
        setCurrentPage(pageIndex);
      }
    }
  };

  const handleUploadClick = () => {
    if (selectedTab === 'financial-projection') {
      setUploadWorkbookOpen(true);
    } else {
      setUploadFDDOpen(true);
    }
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const newDefaultTab = queryParams.get('tab') || 'franchisee_fee';
    setSelectedTab(newDefaultTab);
  }, [location]);

  useEffect(() => {
    goToPinPage();
  }, [fdd, numPages, selectedTab]);

  useEffect(() => {
    if (isFddError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isFddError]);

  useEffect(() => {
    if (isFddSuccess && fddResult?.data) {
      setFdd(fddResult.data);
    }
  }, [fddResult, isFddSuccess, setFdd]);

  useEffect(() => {
    if (isWorkbookError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isWorkbookError]);

  useEffect(() => {
    if (isWorkbookSuccess && workbookResult?.data) {
      setWorkbook(workbookResult.data);
    }
  }, [workbookResult, isWorkbookSuccess, setWorkbook]);

  useEffect(() => {
    if (isUpdateFddError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isUpdateFddError]);

  useEffect(() => {
    if (isUpdateFddSuccess && updateFddResult?.data) {
      setFdd(updateFddResult.data);

      toast({
        title: 'PINed successfully',
      });
    }
  }, [updateFddResult, isUpdateFddSuccess, setFdd]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-center md:flex-row md:justify-between">
        <div className="flex flex-col items-center gap-5 md:flex-row">
          <div className="space-y-1">
            <h2 className="as-strong">Financial Overview</h2>
            <p className="as-body-02 as-strong text-typo-gray-tertiary">
              All the financial information will be here
            </p>
          </div>
        </div>
        <Button
          size="sm"
          variant={
            selectedTab === 'financial-projection'
              ? workbook
                ? 'destructive'
                : 'default'
              : fdd
                ? 'destructive'
                : 'default'
          }
          onClick={handleUploadClick}
        >
          <Unplug />
          {selectedTab === 'financial-projection'
            ? workbook
              ? 'Renew Workbook'
              : 'Upload Workbook'
            : fdd
              ? 'Renew FDD'
              : 'Upload FDD'}
        </Button>
      </div>
      <Tabs
        value={selectedTab}
        onValueChange={(value) => handelClickTab(value)}
        defaultValue="franchisee_fee"
      >
        <TabsList className="mb-4 w-full md:w-fit">
          <TabsTrigger value="franchisee_fee" className="w-full">
            Franchisee Fee
          </TabsTrigger>
          <TabsTrigger value="initial_investment" className="w-full">
            Initial Investment
          </TabsTrigger>
          <TabsTrigger
            value="royalities_brandfund_tech_feeee"
            className="w-full"
          >
            Royalities, Brand Fund, Tech Fee
          </TabsTrigger>
          <TabsTrigger
            value="financial_performance_representation"
            className="w-full"
          >
            Financial Performance Representation
          </TabsTrigger>
          <TabsTrigger value="financial-projection" className="w-full">
            Financial Projection Workbook
          </TabsTrigger>
        </TabsList>
        {[
          'franchisee_fee',
          'initial_investment',
          'royalities_brandfund_tech_feeee',
          'financial_performance_representation',
        ].includes(selectedTab ?? '') && (
          <div className="rounded-xl border border-alpha-dark-5 bg-muted p-4">
            <div className="mb-4 flex items-center justify-between border-b border-border pb-2">
              <p className="as-title-01 as-strong">FDD</p>
              {fdd && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    updateFdd({
                      [selectedTab ?? '']: currentPage,
                    })
                  }
                >
                  {isUpdateFddLoading ? (
                    <Loader2 className="size-4 animate-spin" />
                  ) : (
                    <Download />
                  )}
                  PIN
                </Button>
              )}
            </div>

            {isFddLoading ? (
              <div className="flex items-center justify-center">
                <Loader2 className="size-4 animate-spin" />
              </div>
            ) : fdd ? (
              <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-white p-2 py-4">
                <div className="mb-2 flex items-center justify-between">
                  <div className="ml-4">
                    <DocumentDetailIcon
                      url={fdd.url}
                      name={fdd.name}
                      size={fdd.size}
                    />
                  </div>
                  {numPages > 0 && (
                    <div className="mr-6 text-sm text-gray-500">
                      Page {currentPage} of {numPages}
                    </div>
                  )}
                </div>
                <div className="px-6 pb-2">
                  <div
                    ref={containerRef}
                    className="h-[800px] overflow-y-auto rounded-xl border"
                    onScroll={(e) => {
                      const container = e.currentTarget;
                      const scrollTop = container.scrollTop;
                      const pageHeight = container.scrollHeight / numPages;
                      const newPage = Math.floor(scrollTop / pageHeight) + 1;
                      setCurrentPage(newPage);
                    }}
                  >
                    <Document
                      file={fdd.url}
                      loading={<Loader2 className="size-4 animate-spin" />}
                      className="w-full space-y-4 bg-muted"
                      onLoadSuccess={({ numPages }) => {
                        goToPinPage();
                        setNumPages(numPages);
                      }}
                    >
                      {Array.from(new Array(numPages), (_, index) => (
                        <div
                          key={`page-container-${index}`}
                          className="flex min-h-[800px] items-center justify-center"
                        >
                          <Page
                            key={`page_${index + 1}`}
                            pageNumber={index + 1}
                            width={undefined}
                            className="w-full"
                          />
                        </div>
                      ))}
                    </Document>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <p className="as-body-02 as-strong text-typo-gray-tertiary">
                  No FDD found
                </p>
              </div>
            )}
          </div>
        )}
        <TabsContent
          value="financial-projection"
          className="mt-0 rounded-xl border border-alpha-dark-5 bg-muted p-4"
        >
          <FinancialProjectionTab />
        </TabsContent>
      </Tabs>
      <UploadFDDDialog open={uploadFDDOpen} setOpen={setUploadFDDOpen} />
      <UploadWorkbookDialog
        open={uploadWorkbookOpen}
        setOpen={setUploadWorkbookOpen}
      />
    </div>
  );
};

export default DashboardFinancialOverview;
