import { Download, Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import DocumentDetailIcon from '@/components/global/DocumentDetailIcon';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useGetWorkbook } from '@/hooks/workbook/useGetWorkbook';
import useWorkbookStore from '@/stores/useWorkbookStore';

const FinancialProjectionTab = () => {
  const { workbook, setWorkbook } = useWorkbookStore();

  const {
    result: workbookResult,
    isError: isWorkbookError,
    isSuccess: isWorkbookSuccess,
    isLoading: isWorkbookLoading,
  } = useGetWorkbook();

  const handleDownload = () => {
    if (workbook?.url) {
      const link = document.createElement('a');
      link.href = workbook.url;
      link.download = workbook.name || 'Workbook.xlsx';
      link.click();
    }
  };

  useEffect(() => {
    if (isWorkbookError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  }, [isWorkbookError]);

  useEffect(() => {
    if (isWorkbookSuccess && workbookResult?.data) {
      setWorkbook(workbookResult.data);
    }
  }, [workbookResult, isWorkbookSuccess, setWorkbook]);

  return (
    <>
      <div className="mb-4 flex items-center justify-between border-b border-border pb-2">
        <p className="as-title-01 as-strong">Franchisee Project Workbook</p>
        {workbook && (
          <Button size="sm" variant="outline" onClick={handleDownload}>
            <Download />
            Export
          </Button>
        )}
      </div>

      {isWorkbookLoading ? (
        <div className="flex items-center justify-center">
          <Loader2 className="size-4 animate-spin" />
        </div>
      ) : workbook ? (
        <div className="overflow-hidden rounded-xl border border-alpha-dark-5 bg-white p-2 py-4">
          <div className="mb-2 flex items-center justify-between">
            <div className="ml-4">
              <DocumentDetailIcon
                url={workbook.url}
                name={workbook.name}
                size={workbook.size}
              />
            </div>
          </div>
          <div className="px-6 pb-2">
            <iframe
              src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(workbook.url)}`}
              title="Workbook Viewer"
              width="100%"
              height="600px"
            ></iframe>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center">
          <p className="as-body-02 as-strong text-typo-gray-tertiary">
            No workbook found
          </p>
        </div>
      )}
    </>
  );
};

export default FinancialProjectionTab;
