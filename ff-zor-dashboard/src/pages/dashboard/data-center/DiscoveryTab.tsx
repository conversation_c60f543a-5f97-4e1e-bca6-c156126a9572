import 'react-circular-progressbar/dist/styles.css';

import { ChevronRight, Loader2, Search, User } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { buildStyles, CircularProgressbar } from 'react-circular-progressbar';
import { useNavigate } from 'react-router-dom';

import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { transformRole } from '@/helpers/role.helper';
import { toast } from '@/hooks/ui/use-toast';
import { useGetZorDiscoveryData } from '@/hooks/zor/useGetZorDiscoveryData';
import { ZeeUser } from '@/types/data-center/discovery.type';

// Reasonable sort options for Discovery tab
const DISCOVERY_SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'lead-source', label: 'Lead Source' },
  { value: 'stage', label: 'Current Stage' },
  { value: 'discovery-completion', label: 'Discovery Completion' },
  { value: 'compatibility-rating', label: 'Compatibility Rating' },
];

const DiscoveryTab = () => {
  const navigate = useNavigate();
  const { result, isLoading, isError, isRefetching } = useGetZorDiscoveryData();

  const [sort, setSort] = useState('stage');
  const [searchVal, setSearchVal] = useState('');

  // Get compatibility rating from brand_match_score
  const getCompatibilityRating = (user: ZeeUser): number | null => {
    return user.brand_match_score;
  };

  const zeeUsers = result?.data?.zee_users || [];

  const filteredAndSortedUsers = useMemo(() => {
    const searchLower = searchVal.toLowerCase();
    let filtered = zeeUsers.filter(
      (user) =>
        user.full_name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        user.current_stage?.stage_name?.toLowerCase().includes(searchLower) ||
        user.lead_source?.inviter_name?.toLowerCase().includes(searchLower) ||
        (user.lead_source === null && 'direct'.includes(searchLower))
    );

    // Sort based on selected option
    filtered = [...filtered].sort((a, b) => {
      switch (sort) {
        case 'stage':
          return a.current_stage.stage_name.localeCompare(
            b.current_stage.stage_name
          );
        case 'lead-source': {
          // Sort by lead source: invited users first (by inviter name), then direct users
          const aSource = a.lead_source?.inviter_name || 'Direct';
          const bSource = b.lead_source?.inviter_name || 'Direct';

          // Direct users go to the end
          if (aSource === 'Direct' && bSource !== 'Direct') return 1;
          if (aSource !== 'Direct' && bSource === 'Direct') return -1;

          return aSource.localeCompare(bSource);
        }
        case 'discovery-completion':
          return b.completion_rate - a.completion_rate;
        case 'compatibility-rating': {
          const aRating = getCompatibilityRating(a) || 0;
          const bRating = getCompatibilityRating(b) || 0;
          return bRating - aRating;
        }
        default:
          return a.full_name.localeCompare(b.full_name);
      }
    });

    return filtered;
  }, [searchVal, sort, zeeUsers]);

  const handleGoDetail = (zeeId: string) => {
    navigate(`/dashboard/zee-profile/${zeeId}`);
  };

  const handleSortChange = (value: string) => {
    setSort(value);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchVal(e.target.value);
  };

  // Error handling
  useEffect(() => {
    if (isError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isError]);

  const renderTableRow = (user: ZeeUser) => {
    const compatibilityRating = getCompatibilityRating(user);

    return (
      <TableRow key={user.zee_id}>
        {/* Customer */}
        <TableCell className="flex items-center gap-2 px-4">
          <div className="rounded-full bg-muted">
            {user.photo_url ? (
              <img
                src={user.photo_url}
                alt={user.full_name}
                className="h-10 w-10 rounded-full"
              />
            ) : (
              <User className="m-2" />
            )}
          </div>
          <div>
            <p className="text-typo-gray-secondary">{user.full_name}</p>
            <p className="text-typo-gray-tertiary">{user.email}</p>
          </div>
        </TableCell>

        {/* Lead Source */}
        <TableCell className="py-4">
          {user.lead_source ? (
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 flex-shrink-0 overflow-hidden rounded-full border">
                {user.lead_source.inviter_photo_url ? (
                  <img
                    src={user.lead_source.inviter_photo_url}
                    alt={user.lead_source.inviter_name || 'Unknown'}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <EmptyAvatar className="!h-full !w-full" />
                )}
              </div>
              <div>
                <p className="text-sm font-medium">
                  {user.lead_source.inviter_name || 'Unknown'}
                </p>
                {user.lead_source.inviter_role ? (
                  <Badge variant="outline" className="text-xs">
                    {transformRole(user.lead_source.inviter_role)}
                  </Badge>
                ) : null}
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 flex-shrink-0 overflow-hidden rounded-full border">
                <EmptyAvatar className="!h-full !w-full" />
              </div>
              <div>
                <p className="text-sm font-medium text-typo-gray-tertiary">
                  Unknown
                </p>
                <Badge
                  variant="outline"
                  className="text-xs text-typo-gray-tertiary"
                >
                  N/A
                </Badge>
              </div>
            </div>
          )}
        </TableCell>

        {/* Current Stage */}
        <TableCell className="space-y-1 px-4">
          <p className="font-medium">{user.current_stage.stage_display}</p>
          <p className="text-sm text-typo-gray-tertiary">
            {user.current_stage_item.item_display}
          </p>
        </TableCell>

        {/* Stage Completion */}
        <TableCell className="px-4">
          <div className="flex items-center gap-2">
            <div className="size-8 flex-shrink-0">
              <CircularProgressbar
                value={user.completion_rate}
                strokeWidth={12}
              />
            </div>
            <p className="font-medium">{user.completion_rate.toFixed(1)}%</p>
          </div>
        </TableCell>

        {/* Compatibility Rating */}
        <TableCell className="px-4">
          {compatibilityRating !== null ? (
            <div className="flex items-center gap-2">
              <div className="size-8 flex-shrink-0">
                <CircularProgressbar
                  value={compatibilityRating}
                  strokeWidth={12}
                  styles={buildStyles({
                    pathColor: '#9631F5', // Purple color for compatibility rating
                    trailColor: '#E5E7EB',
                  })}
                />
              </div>
              <p className="font-medium">{compatibilityRating}%</p>
            </div>
          ) : (
            <p className="text-sm text-typo-gray-tertiary">Not rated</p>
          )}
        </TableCell>

        {/* View */}
        <TableCell className="px-4 text-right">
          <Button
            size="icon"
            type="button"
            variant="outline"
            onClick={() => handleGoDetail(user.zee_id)}
          >
            <ChevronRight />
          </Button>
        </TableCell>
      </TableRow>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500">Failed to load discovery data</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="as-strong as-title-01 flex items-center gap-2 border-b border-border p-4 text-zinc-700">
        Discovery
        {isRefetching && <Loader2 className="size-4 animate-spin" />}
      </div>
      <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-white">
        <div className="m-4 flex justify-between">
          <div className="relative flex items-center">
            <span className="absolute left-3 text-gray-500">
              <Search size={16} />
            </span>
            <Input
              type="text"
              value={searchVal}
              className="pl-8"
              placeholder="Search"
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-1">
            <Select onValueChange={handleSortChange} value={sort}>
              <SelectTrigger className="min-w-[200px]">
                <span className="text-typo-gray-tertiary">Sort By :</span>
                <SelectValue defaultValue={sort} />
              </SelectTrigger>
              <SelectContent>
                {DISCOVERY_SORT_OPTIONS.map((item) => (
                  <SelectItem value={item.value} key={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="as-title-04 w-[25%] px-4">
                  Customer
                </TableHead>
                <TableHead className="as-title-04 w-[20%] px-4">
                  Lead Source
                </TableHead>
                <TableHead className="as-title-04 w-[20%] px-4">
                  Current Stage
                </TableHead>
                <TableHead className="as-title-04 w-[15%] px-4">
                  Stage Completion
                </TableHead>
                <TableHead className="as-title-04 w-[15%] px-4">
                  Compatibility Rating
                </TableHead>
                <TableHead className="as-title-04 w-[5%] px-4 text-right">
                  View
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white text-typo-gray-secondary">
              {filteredAndSortedUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="py-8 text-center">
                    <p className="text-typo-gray-tertiary">
                      No discovery data found
                    </p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedUsers.map(renderTableRow)
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default DiscoveryTab;
