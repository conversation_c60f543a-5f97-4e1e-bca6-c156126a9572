import { Facebook, <PERSON><PERSON>heck, User, UserPlus, Youtube } from 'lucide-react';
import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Rectangle,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import GoogleSvg from '@/assets/icons/google.svg';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Reasonable sort options for Analytics tab
const ANALYTICS_SORT_OPTIONS = [
  { value: 'date', label: 'Date' },
  { value: 'source', label: 'Source' },
  { value: 'leads-count', label: 'Leads Count' },
  { value: 'conversion-rate', label: 'Conversion Rate' },
];

interface ChartData {
  source: string;
  leads: number;
  icon?: string | React.ReactNode;
}

interface CityData {
  city: string;
  leads: number;
}

const LEAD_SOURCE_DATA: ChartData[] = [
  { source: 'Youtube', leads: 186, icon: <Youtube /> },
  { source: 'Email', leads: 305, icon: <MailCheck /> },
  { source: 'Google Ads', leads: 237, icon: GoogleSvg },
  { source: 'Facebook', leads: 73, icon: <Facebook /> },
  { source: 'Consultant', leads: 209, icon: <User /> },
  { source: 'Influencers', leads: 214, icon: <UserPlus /> },
];

const LEAD_CITY_DATA: CityData[] = [
  { city: 'New York', leads: 186 },
  { city: 'Naperville', leads: 305 },
  { city: 'San Francisco', leads: 237 },
  { city: 'Toledo', leads: 73 },
  { city: 'Pembroke Pines', leads: 209 },
  { city: 'Orange', leads: 214 },
];

const CHART_CONFIG = {
  width: 800,
  height: 450,
  barSize: 48,
};

const AnalyticsTab = () => {
  const [sort, setSort] = useState('date');

  const renderSourceIcon = (icon: ChartData['icon']) => {
    const iconClasses = 'flex items-center rounded-md border p-1';

    if (typeof icon === 'string') {
      return (
        <div className={iconClasses}>
          <img src={icon} className="w-5" alt="" />
        </div>
      );
    }

    return <div className={iconClasses}>{icon}</div>;
  };

  const renderBarChart = (
    data: ChartData[] | CityData[],
    dataKey: 'source' | 'city',
    fill: string
  ) => (
    <BarChart {...CHART_CONFIG} data={data}>
      <CartesianGrid vertical={false} strokeDasharray="6 6" />
      <XAxis
        dataKey={dataKey}
        tickLine={false}
        tickMargin={10}
        axisLine={false}
        tickFormatter={(value) => value}
        strikethroughThickness={1}
      />
      <YAxis tickLine={false} axisLine={false} tickMargin={10} />
      <Tooltip />
      <Bar
        dataKey="leads"
        fill={fill}
        radius={8}
        activeBar={<Rectangle fill="red" stroke="blue" />}
      />
    </BarChart>
  );

  return (
    <div>
      <div className="as-strong as-title-01 flex items-center justify-between gap-2 border-b border-border p-4 text-zinc-700">
        Analytics
        <Select value={sort} onValueChange={setSort}>
          <SelectTrigger className="w-[200px] bg-white">
            <span className="text-typo-gray-tertiary">Sort By:</span>
            <SelectValue defaultValue={sort} />
          </SelectTrigger>
          <SelectContent>
            {ANALYTICS_SORT_OPTIONS.map((item) => (
              <SelectItem value={item.value} key={item.value}>
                {item.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2 p-3">
        <div className="flex w-full flex-col justify-between gap-6 rounded-xl border border-alpha-dark-5 bg-white p-4 md:flex-row">
          <div>
            <p className="mb-10 font-medium text-zinc-700">Lead source</p>
            {renderBarChart(LEAD_SOURCE_DATA, 'source', 'var(--brand-200)')}
          </div>
          <div className="flex-1 overflow-hidden rounded-xl border">
            <Table>
              <TableHeader className="bg-muted">
                <TableRow>
                  <TableHead className="as-title-04 px-4">Source</TableHead>
                  <TableHead className="as-title-04 px-4">Leads</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="bg-white text-typo-gray-secondary">
                {LEAD_SOURCE_DATA.map((field) => (
                  <TableRow key={field.source}>
                    <TableCell className="flex items-center gap-2 px-4 py-6">
                      {field.icon && renderSourceIcon(field.icon)}
                      <p className="text-typo-gray-secondary">{field.source}</p>
                    </TableCell>
                    <TableCell className="px-4 py-6">
                      <p className="text-typo-gray-secondary">{field.leads}</p>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        <div className="flex w-full flex-col justify-between gap-6 rounded-xl border border-alpha-dark-5 bg-white p-4 md:flex-row">
          <div>
            <p className="mb-10 font-medium text-zinc-700">Lead City</p>
            {renderBarChart(LEAD_CITY_DATA, 'city', '#CFA1FB')}
          </div>
          <div className="flex-1 overflow-hidden rounded-xl border">
            <Table>
              <TableHeader className="bg-muted">
                <TableRow>
                  <TableHead className="as-title-04 px-4">City</TableHead>
                  <TableHead className="as-title-04 px-4">Leads</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="bg-white text-typo-gray-secondary">
                {LEAD_CITY_DATA.map((field) => (
                  <TableRow key={field.city}>
                    <TableCell className="flex items-center gap-2 px-4 py-6">
                      <p className="text-typo-gray-secondary">{field.city}</p>
                    </TableCell>
                    <TableCell className="px-4 py-6">
                      <p className="text-typo-gray-secondary">{field.leads}</p>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsTab;
