import 'react-circular-progressbar/dist/styles.css';

import { ChevronRight, Loader2, Search, User } from 'lucide-react';
import { useMemo, useState } from 'react';
import { CircularProgressbar } from 'react-circular-progressbar';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useGetConsultants } from '@/hooks/zor/useGetConsultants';
import { Consultant } from '@/types/data-center/consultant.type';

// Reasonable sort options for Consultant tab
const CONSULTANT_SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'brand-prospects', label: 'Brand Prospects' },
  { value: 'prospects-closed', label: 'Prospects Closed' },
  { value: 'conversions', label: 'Conversions' },
];

const getRandomValue = (
  consultantId: string,
  min: number,
  max: number
): number => {
  // Use consultant_id to generate consistent random values
  const hash = consultantId.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0);
    return a & a;
  }, 0);
  return Math.abs(hash % (max - min + 1)) + min;
};

const ConsultantTab = () => {
  const navigate = useNavigate();
  const { result, isLoading, isError, isRefetching } = useGetConsultants();

  const [sort, setSort] = useState('name');
  const [searchVal, setSearchVal] = useState('');

  const consultants = result?.data?.consultants || [];

  const filteredFields = useMemo(() => {
    const searchLower = searchVal.toLowerCase();
    let filtered = consultants.filter(
      (field) =>
        field.full_name?.toLowerCase().includes(searchLower) ||
        field.email?.toLowerCase().includes(searchLower)
    );

    // Sort based on selected option
    filtered = [...filtered].sort((a, b) => {
      const aId = a.consultant_id || a.id || '';
      const bId = b.consultant_id || b.id || '';

      switch (sort) {
        case 'name':
          return (a.full_name || '').localeCompare(b.full_name || '');
        case 'brand-prospects':
          return getRandomValue(bId, 10, 50) - getRandomValue(aId, 10, 50);
        case 'prospects-closed':
          return (
            getRandomValue(bId + 'closed', 5, 30) -
            getRandomValue(aId + 'closed', 5, 30)
          );
        case 'conversions':
          return (
            getRandomValue(bId + 'conv', 20, 90) -
            getRandomValue(aId + 'conv', 20, 90)
          );
        default:
          return (a.full_name || '').localeCompare(b.full_name || '');
      }
    });

    return filtered;
  }, [searchVal, consultants, sort]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchVal(e.target.value);
  };

  const handleGoDetail = (consultantId: string) => {
    navigate(`/dashboard/consultant-profile/${consultantId}`);
  };

  const renderTableRow = (field: Consultant) => {
    const consultantId = field.consultant_id || field.id || '';
    const brandProspects = getRandomValue(consultantId, 10, 50);
    const prospectsClosed = getRandomValue(consultantId + 'closed', 5, 30);
    const conversions = getRandomValue(consultantId + 'conv', 20, 90);

    return (
      <TableRow key={consultantId}>
        <TableCell className="flex items-center gap-2 px-4">
          <div className="rounded-full bg-muted">
            {field.photo_url ? (
              <img
                src={field.photo_url}
                alt={field.full_name}
                className="h-10 w-10 rounded-full"
              />
            ) : (
              <User className="m-2" />
            )}
          </div>
          <div>
            <p className="text-typo-gray-secondary">{field.full_name}</p>
            <p className="text-typo-gray-tertiary">{field.email}</p>
          </div>
        </TableCell>
        <TableCell className="px-4">{brandProspects}</TableCell>
        <TableCell className="px-4">{prospectsClosed}</TableCell>
        <TableCell className="flex items-center gap-2 px-4">
          <div className="size-8 flex-shrink-0">
            <CircularProgressbar value={conversions} strokeWidth={12} />
          </div>
          <p className="as-body-03 as-strong">{conversions}%</p>
        </TableCell>
        <TableCell className="px-4 text-right">
          <Button
            size="icon"
            type="button"
            variant="outline"
            onClick={() => handleGoDetail(consultantId)}
          >
            <ChevronRight />
          </Button>
        </TableCell>
      </TableRow>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500">Failed to load consultant data</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="as-strong as-title-01 flex items-center justify-between gap-2 border-b border-border p-4 text-zinc-700">
        Consultant
        {isRefetching && <Loader2 className="size-4 animate-spin" />}
      </div>
      <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-white">
        <div className="m-4 flex justify-between">
          <div className="relative flex items-center">
            <span className="absolute left-3 text-gray-500">
              <Search size={16} />
            </span>
            <Input
              type="text"
              value={searchVal}
              className="pl-8"
              placeholder="Search"
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-1">
            <Select value={sort} onValueChange={setSort}>
              <SelectTrigger className="w-[200px] bg-white">
                <span className="text-typo-gray-tertiary">Sort By:</span>
                <SelectValue defaultValue={sort} />
              </SelectTrigger>
              <SelectContent>
                {CONSULTANT_SORT_OPTIONS.map((item) => (
                  <SelectItem value={item.value} key={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="as-title-04 w-[40%] px-4">Name</TableHead>
                <TableHead className="as-title-04 w-[15%] px-4">
                  Brand Prospects
                </TableHead>
                <TableHead className="as-title-04 w-[15%] px-4">
                  Prospects Closed
                </TableHead>
                <TableHead className="as-title-04 w-[15%] px-4">
                  Conversions
                </TableHead>
                <TableHead className="as-title-04 w-[15%] px-4 text-right">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white text-typo-gray-secondary">
              {filteredFields.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="py-8 text-center">
                    <p className="text-typo-gray-tertiary">
                      No consultant data found
                    </p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredFields.map(renderTableRow)
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default ConsultantTab;
