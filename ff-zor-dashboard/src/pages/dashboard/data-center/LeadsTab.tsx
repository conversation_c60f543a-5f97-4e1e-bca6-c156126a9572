import { useAuth } from '@clerk/clerk-react';
import { Loader2, Search, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import EmptyAvatar from '@/assets/icons/empty-avatar.svg?react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { transformRole } from '@/helpers/role.helper';
import { useRevokeOrgInvitation } from '@/hooks/auth/useSendOrgInvitation';
import { toast } from '@/hooks/ui/use-toast';
import { useGetZorLeadsData } from '@/hooks/zor/useGetZorLeadsData';
import useLeadsStore from '@/stores/useLeadsStore';
import { Lead } from '@/types/data-center/leads.type';

// Reasonable sort options for Leads tab
const LEADS_SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'email', label: 'Email' },
  { value: 'generated', label: 'Generated' },
  { value: 'status', label: 'Status' },
  { value: 'inviter', label: 'Invited By' },
];

interface LeadData {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  status: 'SENT' | 'ACCEPTED';
  invitationId: string;
  invitedBy?: {
    name: string;
    imageUrl?: string;
    role: string;
  };
}

const LeadsTab = () => {
  const {
    leadsData,
    isLoading: storeLoading,
    shouldRefresh,
    setLeadsData,
    setError,
    clearRefresh,
  } = useLeadsStore();

  const {
    result,
    isLoading: queryLoading,
    isError,
    error,
    isRefetching,
    refetch,
  } = useGetZorLeadsData();
  const { mutate: revokeInvitation, isLoading: isRevoking } =
    useRevokeOrgInvitation();
  const { userId } = useAuth();

  const [sort, setSort] = useState('generated');
  const [searchVal, setSearchVal] = useState('');
  const [revokingInvitationId, setRevokingInvitationId] = useState<string>('');

  const canRevokeInvitations = true; // TODO: Implement proper permission check

  // Use store data if available, fallback to query result
  const leads = leadsData?.leads || result?.data?.leads || [];
  const isLoading = storeLoading || queryLoading;

  // Transform API data to component format
  const transformedLeadsData: LeadData[] = useMemo(() => {
    return leads.map((lead: Lead) => ({
      id: lead.id.toString(),
      name: lead.invitee_name || 'Unknown',
      email: lead.invitee_email,
      createdAt: new Date(lead.created_at).toLocaleDateString(),
      status: lead.status,
      invitationId: lead.invitation_id,
      invitedBy: lead.inviter_info
        ? {
            name: lead.inviter_info.name,
            imageUrl: lead.inviter_info.photo_url,
            role: transformRole(lead.inviter_info.role),
          }
        : undefined,
    }));
  }, [leads]);

  const filteredFields = useMemo(() => {
    const searchLower = searchVal.toLowerCase();
    let filtered = transformedLeadsData.filter(({ name, email, invitedBy }) => {
      return [name, email, invitedBy?.name].some((field) =>
        field?.toLowerCase().includes(searchLower)
      );
    });

    // Enhanced sort logic
    filtered = [...filtered].sort((a, b) => {
      switch (sort) {
        case 'name':
          // Sort pending entries to the end, then alphabetically
          if (a.name === 'Pending' && b.name !== 'Pending') return 1;
          if (a.name !== 'Pending' && b.name === 'Pending') return -1;
          return a.name.localeCompare(b.name);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'status':
          // Sort by status: SENT first, then ACCEPTED
          if (a.status === 'SENT' && b.status === 'ACCEPTED') return -1;
          if (a.status === 'ACCEPTED' && b.status === 'SENT') return 1;
          return 0;
        case 'inviter':
          return (a.invitedBy?.name || '').localeCompare(
            b.invitedBy?.name || ''
          );
        case 'generated':
        default: {
          // Sort by date: newest first
          const dateA = new Date(a.createdAt).getTime();
          const dateB = new Date(b.createdAt).getTime();
          return dateB - dateA;
        }
      }
    });

    return filtered;
  }, [searchVal, transformedLeadsData, sort]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchVal(e.target.value);
  };

  // Update store when data is fetched
  useEffect(() => {
    if (result?.data) {
      setLeadsData(result.data);
    }
  }, [result, setLeadsData]);

  // Handle refresh triggers from store
  useEffect(() => {
    if (shouldRefresh) {
      refetch();
      clearRefresh();
    }
  }, [shouldRefresh, refetch, clearRefresh]);

  // Handle API errors
  useEffect(() => {
    if (isError && error) {
      const errorMessage = 'Failed to load leads data. Please try again.';
      setError(errorMessage);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: errorMessage,
      });
    }
  }, [isError, error, setError]);

  const handleRevokeInvitation = async (leadData: LeadData) => {
    if (leadData.status !== 'SENT') {
      toast({
        variant: 'destructive',
        title: 'Cannot revoke',
        description: 'Only pending invitations can be revoked.',
      });
      return;
    }

    setRevokingInvitationId(leadData.id);

    try {
      // Get required parameters for revocation
      const orgId = leadsData?.zor_id || result?.data?.zor_id;

      if (!orgId || !leadData.invitationId || !userId) {
        throw new Error('Missing required parameters');
      }

      await revokeInvitation({
        orgId,
        invitationId: leadData.invitationId,
        requestingUserId: userId,
      });

      toast({
        title: 'Invitation revoked',
        description: 'The invitation has been successfully revoked.',
      });

      // Trigger store refresh to update the UI
      useLeadsStore.getState().triggerRefresh();
    } catch (error) {
      console.error('Failed to revoke organization invitation', error);
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Failed to revoke invitation. Please try again later.',
      });
    } finally {
      setRevokingInvitationId('');
    }
  };

  const renderTableRow = (field: LeadData) => (
    <TableRow key={field.id}>
      <TableCell className="p-4">
        <div>
          <p className="text-typo-gray-secondary">{field.name}</p>
          <p className="text-typo-gray-tertiary">{field.email}</p>
        </div>
      </TableCell>
      <TableCell className="py-4">{field.createdAt}</TableCell>
      <TableCell className="py-4">
        {field.invitedBy ? (
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 flex-shrink-0 overflow-hidden rounded-full border">
              {field.invitedBy.imageUrl ? (
                <img
                  src={field.invitedBy.imageUrl}
                  alt={field.invitedBy.name}
                  className="h-full w-full object-cover"
                />
              ) : (
                <EmptyAvatar className="!h-full !w-full" />
              )}
            </div>
            <div>
              <p className="text-sm font-medium">
                {field.invitedBy.name || 'Unknown'}
              </p>
              {field.invitedBy.role && (
                <Badge variant="outline" className="text-xs">
                  {field.invitedBy.role}
                </Badge>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 flex-shrink-0 overflow-hidden rounded-full border">
              <EmptyAvatar className="!h-full !w-full" />
            </div>
            <div>
              <p className="text-sm font-medium text-typo-gray-tertiary">
                Unknown
              </p>
              <Badge
                variant="outline"
                className="text-xs text-typo-gray-tertiary"
              >
                N/A
              </Badge>
            </div>
          </div>
        )}
      </TableCell>
      <TableCell className="py-4">
        <div className="flex items-center gap-2">
          <p className="text-muted-foreground">
            {field.status === 'SENT' ? 'Invitation sent' : 'Accepted'}
          </p>
          {field.status === 'SENT' && canRevokeInvitations && (
            <Button
              size="icon"
              variant="outline"
              onClick={() => handleRevokeInvitation(field)}
              disabled={revokingInvitationId === field.id || isRevoking}
              className="h-8 w-8"
            >
              {revokingInvitationId === field.id || isRevoking ? (
                <Loader2 className="size-4 animate-spin" />
              ) : (
                <X className="size-4" />
              )}
            </Button>
          )}
        </div>
      </TableCell>
    </TableRow>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500">Failed to load leads data</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="as-strong as-title-01 flex items-center gap-2 border-b border-border p-4 text-zinc-700">
        Leads
        {isRefetching && <Loader2 className="size-4 animate-spin" />}
      </div>
      <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-white">
        <div className="m-4 flex justify-between">
          <div className="relative flex items-center">
            <span className="absolute left-3 text-gray-500">
              <Search size={16} />
            </span>
            <Input
              type="text"
              value={searchVal}
              className="pl-8"
              placeholder="Search"
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-1">
            <Select value={sort} onValueChange={setSort}>
              <SelectTrigger className="w-[200px] bg-white">
                <span className="text-typo-gray-tertiary">Sort By:</span>
                <SelectValue defaultValue={sort} />
              </SelectTrigger>
              <SelectContent>
                {LEADS_SORT_OPTIONS.map((item) => (
                  <SelectItem value={item.value} key={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="as-title-04 w-[30%] px-4">
                  Prospect Name
                </TableHead>
                <TableHead className="as-title-04 w-[15%]">Generated</TableHead>
                <TableHead className="as-title-04 w-[30%]">
                  Invited By
                </TableHead>
                <TableHead className="as-title-04 w-[25%]">Invite</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white text-typo-gray-secondary">
              {filteredFields.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="py-8 text-center">
                    <p className="text-typo-gray-tertiary">No leads found</p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredFields.map((field) => renderTableRow(field))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default LeadsTab;
