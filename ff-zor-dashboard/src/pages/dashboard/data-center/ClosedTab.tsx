import { Loader2, Search } from 'lucide-react';
import { useMemo, useState } from 'react';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useGetZorClosedData } from '@/hooks/zor/useGetZorClosedData';
import { ClosedZeeUser } from '@/types/data-center/closed.type';

// Sort options for Closed tab
const CLOSED_SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'email', label: 'Email' },
  { value: 'territory', label: 'Territory Number' },
  { value: 'closed-date', label: 'Closed Date' },
];

const ClosedTab = () => {
  const { result, isLoading, isError, isRefetching } = useGetZorClosedData();
  const [searchVal, setSearchVal] = useState('');
  const [sort, setSort] = useState('closed-date');

  // Generate random data for missing fields
  const generateRandomTerritoryNumber = (zeeId: string): string => {
    const hash = zeeId.split('').reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    return `T${(Math.abs(hash) % 9000) + 1000}`;
  };

  const generateRandomLocation = (zeeId: string): string => {
    const cities = [
      'New York, NY',
      'Los Angeles, CA',
      'Chicago, IL',
      'Houston, TX',
      'Phoenix, AZ',
      'Philadelphia, PA',
      'San Antonio, TX',
      'San Diego, CA',
      'Dallas, TX',
      'San Jose, CA',
      'Austin, TX',
      'Jacksonville, FL',
      'Fort Worth, TX',
      'Columbus, OH',
      'Charlotte, NC',
      'San Francisco, CA',
      'Indianapolis, IN',
      'Seattle, WA',
      'Denver, CO',
      'Washington, DC',
    ];

    const hash = zeeId.split('').reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    return cities[Math.abs(hash) % cities.length];
  };

  const generateRandomClosedDate = (zeeId: string): string => {
    const hash = zeeId.split('').reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);

    // Generate dates within the last 2 years
    const daysBack = Math.abs(hash) % (365 * 2);
    const date = new Date();
    date.setDate(date.getDate() - daysBack);

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const closedUsers = result?.data?.completed_zee_users || [];

  const filteredAndSortedUsers = useMemo(() => {
    const searchLower = searchVal.toLowerCase();
    let filtered = closedUsers.filter(
      (user) =>
        user.full_name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        generateRandomTerritoryNumber(user.zee_id)
          .toLowerCase()
          .includes(searchLower)
    );

    // Sort based on selected option
    filtered = [...filtered].sort((a, b) => {
      switch (sort) {
        case 'name':
          return a.full_name.localeCompare(b.full_name);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'territory':
          return generateRandomTerritoryNumber(a.zee_id).localeCompare(
            generateRandomTerritoryNumber(b.zee_id)
          );
        case 'closed-date':
        default:
          return (
            new Date(generateRandomClosedDate(b.zee_id)).getTime() -
            new Date(generateRandomClosedDate(a.zee_id)).getTime()
          );
      }
    });

    return filtered;
  }, [searchVal, sort, closedUsers]);

  const handleSortChange = (value: string) => {
    setSort(value);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchVal(e.target.value);
  };

  const renderTableRow = (user: ClosedZeeUser) => {
    const territoryNumber = generateRandomTerritoryNumber(user.zee_id);
    const franchiseLocation = generateRandomLocation(user.zee_id);
    const closedDate = generateRandomClosedDate(user.zee_id);

    return (
      <TableRow key={user.zee_id}>
        <TableCell className="px-4">{user.full_name}</TableCell>
        <TableCell>{user.email}</TableCell>
        <TableCell>{territoryNumber}</TableCell>
        <TableCell>{franchiseLocation}</TableCell>
        <TableCell>{closedDate}</TableCell>
      </TableRow>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500">Failed to load closed data</p>
          <p className="text-sm text-typo-gray-tertiary">
            Please try again later
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="as-strong as-title-01 flex items-center gap-2 border-b border-border p-4 text-zinc-700">
        Closed
        {isRefetching && <Loader2 className="size-4 animate-spin" />}
      </div>
      <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-white">
        <div className="m-4 flex justify-between">
          <div className="relative flex items-center">
            <span className="absolute left-3 text-gray-500">
              <Search size={16} />
            </span>
            <Input
              type="text"
              value={searchVal}
              className="pl-8"
              placeholder="Search"
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-1">
            <Select onValueChange={handleSortChange} value={sort}>
              <SelectTrigger className="min-w-[200px]">
                <span className="text-typo-gray-tertiary">Sort By :</span>
                <SelectValue defaultValue={sort} />
              </SelectTrigger>
              <SelectContent>
                {CLOSED_SORT_OPTIONS.map((item) => (
                  <SelectItem value={item.value} key={item.value}>
                    {item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="m-4 overflow-hidden rounded-xl border border-alpha-dark-5 bg-muted">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="as-title-04 px-4">Name</TableHead>
                <TableHead className="as-title-04">Email</TableHead>
                <TableHead className="as-title-04">Territory Number</TableHead>
                <TableHead className="as-title-04">
                  Franchise Location
                </TableHead>
                <TableHead className="as-title-04">Closed Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white text-typo-gray-secondary">
              {filteredAndSortedUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="py-8 text-center">
                    <p className="text-typo-gray-tertiary">
                      No closed data found
                    </p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedUsers.map(renderTableRow)
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default ClosedTab;
