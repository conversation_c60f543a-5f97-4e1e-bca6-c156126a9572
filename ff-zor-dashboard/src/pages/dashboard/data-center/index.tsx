import { Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import InviteCandidateDialog from '@/components/dialogs/InviteCandidateDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import ClosedTab from './ClosedTab';
import ConsultantTab from './ConsultantTab';
import DiscoveryTab from './DiscoveryTab';
import LeadsTab from './LeadsTab';
import PipelineTab from './PipelineTab';

const SEARCHABLE_TABS = ['pipeline'] as const;
type TabType =
  | (typeof SEARCHABLE_TABS)[number]
  | 'leads'
  | 'discovery'
  | 'consultant'
  | 'closed';

const DashboardDataCenter = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tab = searchParams.get('tab');

  const [selectedTab, setSelectedTab] = useState<TabType>('pipeline');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleTabChange = (value: TabType) => {
    setSelectedTab(value);
    setSearchQuery('');
    navigate(`/dashboard/data-center?tab=${value}`);
  };

  const handleInviteClick = () => {
    setIsInviteDialogOpen(true);
  };

  const handleTabValueChange = (value: string) => {
    handleTabChange(value as TabType);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const showSearchBar = SEARCHABLE_TABS.includes(
    selectedTab as (typeof SEARCHABLE_TABS)[number]
  );

  useEffect(() => {
    if (tab) {
      setSelectedTab(tab as TabType);
    }
  }, [tab]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-center md:flex-row md:justify-between">
        <div className="flex w-full flex-col items-center justify-between gap-5 md:flex-row">
          <div className="space-y-1">
            <h2 className="as-strong">Data Center</h2>
            <p className="as-body-02 as-strong text-typo-gray-tertiary">
              All the important information will be here
            </p>
          </div>
          <Button onClick={handleInviteClick}>Invite Candidate</Button>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={handleTabValueChange}>
        <div className="mb-4 flex items-center justify-between">
          <TabsList className="w-full md:w-fit">
            <TabsTrigger value="pipeline" className="w-full">
              Pipeline
            </TabsTrigger>
            <TabsTrigger value="leads" className="w-full">
              Leads
            </TabsTrigger>
            <TabsTrigger value="discovery" className="w-full">
              Discovery
            </TabsTrigger>
            <TabsTrigger value="consultant" className="w-full">
              Consultant
            </TabsTrigger>
            <TabsTrigger value="closed" className="w-full">
              Closed
            </TabsTrigger>
          </TabsList>

          {showSearchBar && (
            <div className="relative flex w-[300px] items-center">
              <Search size={16} className="absolute left-3 text-gray-500" />
              <Input
                type="text"
                value={searchQuery}
                className="pl-8"
                placeholder="Search name or description"
                onChange={handleSearchChange}
              />
            </div>
          )}
        </div>

        <TabsContent value="pipeline">
          <PipelineTab searchVal={searchQuery} />
        </TabsContent>
        <TabsContent
          value="leads"
          className="rounded-xl border border-alpha-dark-5 bg-muted"
        >
          <LeadsTab />
        </TabsContent>
        <TabsContent
          value="discovery"
          className="rounded-xl border border-alpha-dark-5 bg-muted"
        >
          <DiscoveryTab />
        </TabsContent>
        <TabsContent
          value="consultant"
          className="rounded-xl border border-alpha-dark-5 bg-muted"
        >
          <ConsultantTab />
        </TabsContent>
        <TabsContent
          value="closed"
          className="rounded-xl border border-alpha-dark-5 bg-muted"
        >
          <ClosedTab />
        </TabsContent>
      </Tabs>

      <InviteCandidateDialog
        open={isInviteDialogOpen}
        setOpen={setIsInviteDialogOpen}
      />
    </div>
  );
};

export default DashboardDataCenter;
