import { DragDropContext } from '@hello-pangea/dnd';
import { ChevronLeft, Loader2 } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import { PipelineList } from '@/components/global/PipelineList';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import { useGetZorPipelineData } from '@/hooks/zor/useGetZorPipelineData';
import { formatLastOnline } from '@/lib/date';
import { cn } from '@/lib/ui/utils';
import { Consultant } from '@/types/data-center/consultant.type';
import { ZeeMember } from '@/types/data-center/pipeline.type';

export interface PipeCard {
  id: string;
  name: string | null;
  state: string | null;
  avatar: string | null;
  consultant?: Consultant | null;
  last_online: string | null; // Formatted human-readable string
}

interface Props {
  searchVal: string;
}

const reorder = (
  list: PipeCard[],
  startIndex: number,
  endIndex: number
): PipeCard[] => {
  const result = [...list];
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);
  return result;
};

const move = (
  source: PipeCard[],
  destination: PipeCard[],
  droppableSource: { index: number; droppableId: string },
  droppableDestination: { index: number; droppableId: string }
): Record<string, PipeCard[]> => {
  const sourceClone = [...source];
  const destClone = [...destination];
  const [removed] = sourceClone.splice(droppableSource.index, 1);

  destClone.splice(droppableDestination.index, 0, removed);

  return {
    [droppableSource.droppableId]: sourceClone,
    [droppableDestination.droppableId]: destClone,
  };
};

// Helper function to convert ZeeMember to PipeCard
const convertZeeMemberToPipeCard = (member: ZeeMember): PipeCard => ({
  id: member.zee_id,
  name: member.full_name,
  state: member.state,
  avatar: member.photo_url || null,
  consultant: {
    full_name: 'Leon Li',
  },
  last_online: formatLastOnline(member.last_online_at),
});

const PipelineTab = ({ searchVal }: Props) => {
  const { result, isLoading, isError, isRefetching } = useGetZorPipelineData();

  // Transform API data to component structure
  const transformedData = useMemo(() => {
    if (!result?.data?.pipeline_stage_items) {
      return [];
    }

    // Each stage item becomes its own column
    return result.data.pipeline_stage_items.map((item) =>
      item.zee_members.map(convertZeeMemberToPipeCard)
    );
  }, [result]);

  // Get stage item titles and stage names from API data
  const stageLabels = useMemo(() => {
    if (!result?.data?.pipeline_stage_items) {
      return [];
    }

    return result.data.pipeline_stage_items.map((item) => ({
      stageName: item.stage_name,
      displayLabel: `${item.stage_item_title} - ${item.stage_name}`,
      stageItemTitle: item.stage_item_title,
    }));
  }, [result]);

  const [pipelineData, setPipelineData] = useState<PipeCard[][]>([]);
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  const toggleSection = (key: number) => {
    setExpandedSections((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  // Error handling
  useEffect(() => {
    if (isError) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [isError]);

  // Update pipeline data when API data changes
  useEffect(() => {
    if (transformedData.length > 0) {
      setPipelineData(transformedData);
    }
  }, [transformedData]);

  useEffect(() => {
    if (!searchVal || transformedData.length === 0) {
      setPipelineData(transformedData);
      return;
    }

    const searchTerm = searchVal.toLowerCase();
    const filteredState = pipelineData.map((section) =>
      section.filter((item) =>
        [item.name, item.state, item.consultant?.full_name].some((field) =>
          field?.toLowerCase().includes(searchTerm)
        )
      )
    );

    setPipelineData(filteredState);
  }, [searchVal, transformedData]);

  const onDragEnd = (result: {
    source: { index: number; droppableId: string };
    destination: { index: number; droppableId: string } | null;
  }) => {
    const { source, destination } = result;

    if (!destination) return;

    const sourceIndex = +source.droppableId;
    const destIndex = +destination.droppableId;

    const newState = [...pipelineData];

    if (sourceIndex === destIndex) {
      newState[sourceIndex] = reorder(
        pipelineData[sourceIndex],
        source.index,
        destination.index
      );
    } else {
      const result = move(
        pipelineData[sourceIndex],
        pipelineData[destIndex],
        source,
        destination
      );
      newState[sourceIndex] = result[sourceIndex];
      newState[destIndex] = result[destIndex];
    }

    setPipelineData(newState);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="size-4 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex min-h-full items-center justify-center rounded-xl border">
        <div className="text-center">
          <div className="text-lg font-medium text-red-600">
            Error loading pipeline data
          </div>
          <div className="mt-2 text-sm text-typo-gray-secondary">
            Please try refreshing the page
          </div>
        </div>
      </div>
    );
  }

  // Show message when there are no pipeline stage items
  if (
    !isLoading &&
    (!result?.data?.pipeline_stage_items ||
      result.data.pipeline_stage_items.length === 0)
  ) {
    return (
      <div className="flex min-h-full items-center justify-center rounded-xl border border-alpha-dark-5 bg-muted py-8">
        <div className="text-center">
          <div className="text-typo-gray-primary text-lg font-medium">
            No pipeline stage items found
          </div>
          <div className="mt-2 text-sm text-typo-gray-secondary">
            Pipeline stage items will appear here once they are created
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {isRefetching && (
        <div className="flex items-center gap-2 text-sm text-zinc-500">
          <Loader2 className="size-4 animate-spin" />
          Refreshing pipeline data...
        </div>
      )}
      <div className="flex min-h-full overflow-auto rounded-xl border">
        <DragDropContext onDragEnd={onDragEnd}>
          {pipelineData.map((items, index) => (
            <div
              key={`pipeline_${index}`}
              className={cn(
                !expandedSections[index] && 'flex flex-1 flex-col',
                index !== pipelineData.length - 1 && 'border-r'
              )}
            >
              <div
                className={cn(
                  'flex h-20 items-center justify-between gap-2 border-b bg-zinc-50 p-3',
                  expandedSections[index] && 'hidden'
                )}
              >
                <div className="as-body-02 as-strong text-typo-gray-secondary">
                  {stageLabels[index]?.displayLabel || `Stage ${index + 1}`}
                </div>
                <div className="flex items-center">
                  <div className="mr-2 flex h-9 w-9 items-center justify-center rounded-full border-2 border-border bg-white text-typo-gray-secondary">
                    {items.length}
                  </div>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => toggleSection(index)}
                  >
                    <ChevronLeft
                      size={15}
                      className={cn(expandedSections[index] && 'rotate-180')}
                    />
                  </Button>
                </div>
              </div>
              <div
                className={cn(
                  'flex h-full items-center justify-center bg-zinc-50 p-3',
                  !expandedSections[index] && 'hidden'
                )}
              >
                <Button variant="outline" onClick={() => toggleSection(index)}>
                  <ChevronLeft
                    size={20}
                    className={cn(expandedSections[index] && 'rotate-180')}
                  />
                </Button>
              </div>
              <PipelineList
                index={index}
                items={items}
                expanded={expandedSections[index]}
              />
            </div>
          ))}
        </DragDropContext>
      </div>
    </div>
  );
};

export default PipelineTab;
