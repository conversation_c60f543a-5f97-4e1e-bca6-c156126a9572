import { useSignIn } from '@clerk/clerk-react';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';

import GoogleIcon from '@/assets/icons/google.svg?react';
import SignUpForm from '@/components/forms/auth/SignUpForm';
import Logo from '@/components/global/Logo';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import logger from '@/services/logger.service';

const SignUp = () => {
  const { isLoaded: isLoadedSignIn, signIn } = useSignIn();

  const [loading, setLoading] = useState(false);

  const handleGoogleSignUp = async () => {
    if (!isLoadedSignIn) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Sign up service is not available',
      });
      return;
    }

    setLoading(true);
    try {
      await signIn.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: '/auth/sso-callback',
        redirectUrlComplete: '/',
      });
    } catch (err) {
      logger.error('Google sign-up authentication failed', err as Error, {
        strategy: 'oauth_google',
      });
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mx-auto flex min-h-full w-full max-w-[400px] flex-col justify-center px-4 max-sm:items-center lg:w-1/2">
      <div className="flex">
        <Logo />
      </div>
      <h2 className="as-strong mb-6 mt-10 text-black">Create your account</h2>
      <SignUpForm />
      <div className="my-4 flex items-center">
        <div className="h-[1px] w-full bg-neutral-200" />
        <p className="as-title-03 as-strong px-2 text-neutral-500">OR</p>
        <div className="h-[1px] w-full bg-neutral-200" />
      </div>
      <Button
        variant="outline"
        disabled={loading}
        className="w-full"
        onClick={handleGoogleSignUp}
      >
        {loading ? (
          <Loader2 className="size-4 animate-spin" />
        ) : (
          <>
            <GoogleIcon className="h-4 w-4" />
            Continue with Google
          </>
        )}
      </Button>

      <p className="as-title-03 mt-4 text-center text-muted-foreground">
        Already have an account?
        <Link to="/auth/sign-in" className="as-strong ml-2 text-brand-700">
          Sign in
        </Link>
      </p>
    </div>
  );
};

export default SignUp;
