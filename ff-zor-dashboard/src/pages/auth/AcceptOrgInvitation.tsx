import { useSignIn, useSignUp } from '@clerk/clerk-react';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { z } from 'zod';

import GoogleIcon from '@/assets/icons/google.svg?react';
import SignUpForm from '@/components/forms/auth/SignUpForm';
import SignUpFormSchema from '@/components/forms/schemas/SignUpFormSchema';
import Logo from '@/components/global/Logo';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/ui/use-toast';
import logger from '@/services/logger.service';

type SignUpFormValues = z.infer<typeof SignUpFormSchema>;

const AcceptOrgInvitation = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('__clerk_ticket');
  const accountStatus = searchParams.get('__clerk_status');
  const email = searchParams.get('email');

  const {
    isLoaded: isLoadedSignIn,
    signIn,
    setActive: setActiveSignIn,
  } = useSignIn();
  const {
    isLoaded: isLoadedSignUp,
    signUp,
    setActive: setActiveSignUp,
  } = useSignUp();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (
      !token ||
      accountStatus !== 'sign_in' ||
      !isLoadedSignIn ||
      !setActiveSignIn
    ) {
      return;
    }

    const createSignIn = async () => {
      try {
        const signInAttempt = await signIn.create({
          strategy: 'ticket',
          ticket: token,
        });

        if (signInAttempt.status === 'complete') {
          await setActiveSignIn({
            session: signInAttempt.createdSessionId,
          });
        } else {
          logger.error('Organization sign-in attempt failed', undefined, {
            signInAttempt,
            token,
          });
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'Failed to join the organization',
          });
        }
      } catch (err) {
        logger.error('Organization sign-in failed', err as Error, { token });
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to join the organization',
        });
      }
    };

    createSignIn();
  }, [token, signIn, accountStatus, setActiveSignIn]);

  const handleSignUp = async (values: SignUpFormValues) => {
    if (!token || !isLoadedSignUp || !setActiveSignUp) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Sign up service is not available',
      });
      return;
    }

    try {
      const signUpAttempt = await signUp.create({
        strategy: 'ticket',
        ticket: token,
        emailAddress: values.email,
        password: values.password,
      });

      if (signUpAttempt.status === 'complete') {
        await setActiveSignUp({ session: signUpAttempt.createdSessionId });
      } else {
        logger.error('Organization sign-up attempt failed', undefined, {
          signUpAttempt,
          token,
          email: values.email,
        });
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to create account',
        });
      }
    } catch (err) {
      logger.error('Organization sign-up failed', err as Error, {
        token,
        email: values.email,
      });
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to create account',
      });
    }
  };

  const handleGoogleSignUp = async () => {
    if (!isLoadedSignIn) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Sign up service is not available',
      });
      return;
    }

    setLoading(true);
    try {
      await signIn.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: '/auth/sso-callback?is_invitation=true',
        redirectUrlComplete: '/',
      });
    } catch (err) {
      logger.error('Google sign-up authentication failed', err as Error, {
        strategy: 'oauth_google',
      });
      toast({
        variant: 'destructive',
        title: 'Something went wrong',
        description: 'Please try again',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="mx-auto flex min-h-full w-full max-w-xl flex-col items-center justify-center gap-3 px-4 max-sm:items-center lg:w-1/2">
        <h2 className="as-strong text-black">No invitation token found.</h2>
      </div>
    );
  }

  if (accountStatus === 'sign_in') {
    return (
      <div className="mx-auto flex min-h-full w-full max-w-xl flex-col items-center justify-center gap-3 px-4 max-sm:items-center lg:w-1/2">
        <Loader2 className="size-4 animate-spin" />
        <h2 className="as-strong text-black">Joining to the organization</h2>
      </div>
    );
  }

  if (accountStatus === 'sign_up') {
    return (
      <div className="mx-auto flex min-h-screen w-full max-w-[400px] flex-col justify-center px-4 max-sm:items-center lg:w-1/2">
        <div className="flex">
          <Logo />
        </div>
        <h2 className="as-strong mb-6 mt-10 text-black">Create your account</h2>
        <SignUpForm
          customSubmit={handleSignUp}
          defaultEmail={email || undefined}
        />
        <div className="my-4 flex items-center">
          <div className="h-[1px] w-full bg-neutral-200" />
          <p className="as-title-03 as-strong px-2 text-neutral-500">OR</p>
          <div className="h-[1px] w-full bg-neutral-200" />
        </div>
        <Button
          variant="outline"
          className="w-full"
          onClick={handleGoogleSignUp}
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="size-4 animate-spin" />
          ) : (
            <>
              <GoogleIcon className="h-4 w-4" />
              Continue with Google
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto flex min-h-full w-full max-w-xl flex-col items-center justify-center gap-3 px-4 max-sm:items-center lg:w-1/2">
      <h2 className="as-strong text-black">
        Organization invitation accepted!
      </h2>
    </div>
  );
};

export default AcceptOrgInvitation;
