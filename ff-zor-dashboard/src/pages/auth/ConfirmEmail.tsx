import { useClerk } from '@clerk/clerk-react';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { toast } from '@/hooks/ui/use-toast';
import logger from '@/services/logger.service';

const ConfirmEmail = () => {
  const navigate = useNavigate();
  const { setActive, handleEmailLinkVerification } = useClerk();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        await handleEmailLinkVerification({});
        await setActive?.({
          session: null,
          redirectUrl: '/auth/sign-in',
        });
      } catch (err) {
        logger.error('Email verification failed', err as Error);
        if (err instanceof Error && err.message.includes('expired')) {
          toast({
            variant: 'destructive',
            title: 'Email link has expired',
            description: 'Please try again',
          });
        } else {
          toast({
            variant: 'destructive',
            title: 'Email link verification failed',
            description: 'Please try again',
          });
        }
        navigate('/auth/sign-up');
      }
    };

    verifyEmail();
  }, [navigate, handleEmailLinkVerification]);

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="text-center">
        <div className="mb-4 flex items-center justify-center">
          <Loader2 className="size-4 animate-spin" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">
          Confirming Email
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          Please wait while we verify your email address...
        </p>
      </div>
    </div>
  );
};

export default ConfirmEmail;
