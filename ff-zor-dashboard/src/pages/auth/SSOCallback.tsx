import { AuthenticateWithRedirectCallback } from '@clerk/clerk-react';
import { Loader2 } from 'lucide-react';

const SSOCallback = () => {
  // Handle the redirect flow by calling the Clerk.handleRedirectCallback() method
  // or rendering the prebuilt <AuthenticateWithRedirectCallback/> component.
  // This is the final step in the custom OAuth flow.
  return (
    <div className="flex h-screen w-full items-center justify-center">
      <Loader2 className="size-4 animate-spin" />
      <AuthenticateWithRedirectCallback />
    </div>
  );
};

export default SSOCallback;
