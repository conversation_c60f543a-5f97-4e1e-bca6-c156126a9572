{"name": "ff-zor-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "format": "prettier --write .", "lint:fix": "npm run format && npm run lint -- --fix", "preview": "vite preview", "prepare": "husky && husky install", "env:local": "node scripts/switch-env.js local", "env:dashboard": "node scripts/switch-env.js dashboard", "env:help": "node scripts/switch-env.js --help"}, "dependencies": {"@clerk/backend": "^1.23.11", "@clerk/clerk-react": "^5.21.1", "@clerk/react-router": "^0.1.6", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@react-google-maps/api": "^2.20.6", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.60.6", "@tanstack/react-table": "^8.20.5", "@turf/turf": "^7.2.0", "axios": "^1.7.7", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "google-maps": "^4.3.3", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.445.0", "payment": "^2.4.7", "pdfjs-dist": "^5.3.31", "rc-slider": "^11.1.8", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-color-palette": "^7.3.0", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-minimal-pie-chart": "^9.1.0", "react-payment-logos": "^1.1.0", "react-pdf": "^9.2.1", "react-router-dom": "^7.0.2", "react-select": "^5.8.3", "recharts": "^2.15.4", "shadcn": "^2.1.8", "sidebar": "^1.0.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/chroma-js": "^2.4.4", "@types/node": "^22.5.2", "@types/payment": "^2.1.7", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.9.0", "husky": "^9.1.6", "lint-staged": "^16.1.1", "postcss": "^8.4.44", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.10", "typescript": "5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-svgr": "^4.3.0"}, "lint-staged": {"*.{js,css,ts,tsx,jsx}": ["prettier --write", "eslint --fix"]}}