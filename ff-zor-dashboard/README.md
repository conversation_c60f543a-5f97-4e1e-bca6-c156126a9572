# FF-ZOR-Dashboard

A modern React dashboard built with TypeScript, Vite, and Tailwind CSS for managing ZOR operations.

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone https://github.com/FFmain/ff-zor-dashboard.git
cd ff-zor-dashboard
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables (see [Environment Variables](#-environment-variables) section)

4. Start the development server:

```bash
npm run dev
```

5. Open your browser and navigate to `http://localhost:5173`

## 🌐 Deployment & Branches

This project uses a dual-branch deployment strategy with automatic CI/CD:

### Branch Structure

- **`main`** - Production branch for client-facing deployment
- **`dev`** - Development branch for QA and testing

### Hosting

Both branches are hosted on **Vercel** with automatic deployments:

- **Production**: Automatically deploys from `main` branch
- **Development**: Automatically deploys from `dev` branch

When you push commits to either branch, Vercel automatically rebuilds and deploys the application.

### Database Architecture

The project uses **Supabase Branch Features** for database management:

- **Main branch** → **Supabase Production Branch** (client-facing data)
- **Dev branch** → **Supabase Development Branch** (testing/QA data)
- **Local development** → **Supabase Development Branch** (consistent with dev environment)

This setup ensures complete data isolation between production and development environments while maintaining schema consistency.

## 🔧 Environment Variables

### The Challenge

Environment variables are crucial for this dashboard to function properly. The current setup presents a configuration challenge:

1. **Vercel Environments**: Separate env variables for production (`main`) and development (`dev`)
2. **Local Development**: Requires `.env.local` file
3. **Current Local Setup**: Configured for localhost URLs (for local microservices)

### Local Development Options

You have two approaches for local development:

#### Option 1: Full Local Development (Current Setup)

Use this when you want to run all microservices locally:

- Backend APIs point to `localhost` URLs
- Requires running all dependent services locally
- Best for comprehensive local development

#### Option 2: Dashboard-Only Development

Use this when you only want to work on the dashboard:

- Backend APIs point to dev environment URLs
- No need to run other microservices locally
- Best for frontend-focused development

### Environment Setup

1. Create `.env.local` in the project root:

```bash
touch .env.local
```

2. Add your environment variables to `.env.local` (this file is gitignored)

3. Choose your configuration based on your development needs:

**For Full Local Development:**

```env
# Example - replace with your actual localhost URLs
VITE_API_URL=http://localhost:8080
VITE_CLERK_API_URL=http://localhost:3001
VITE_TERRITORIES_API_URL=http://localhost:3000
# Uses Supabase development branch
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_dev_branch_anon_key
```

**For Dashboard-Only Development:**

```env
# Example - replace with your actual dev URLs
VITE_API_URL=https://your-dev-api.awsapprunner.com
VITE_CLERK_API_URL=https://clerk-apis-dev.franchiseframeworks.com
VITE_TERRITORIES_API_URL=https://territories-apis-dev.franchiseframeworks.com
# Uses Supabase development branch (same as live dev)
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_dev_branch_anon_key
```

## 🔧 Available Scripts

### Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Code Quality

- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run lint:fix` - Format and fix linting issues

### Environment Management

- `npm run env:local` - Switch to full local development environment
- `npm run env:dashboard` - Switch to dashboard-only development environment
- `npm run env:help` - Show environment switching help

## 🛡️ Code Quality

This project uses several tools to maintain code quality:

### Pre-commit Hooks (Husky)

Husky is configured to run checks before each commit:

- **TypeScript**: Type checking
- **ESLint**: Code linting
- **Prettier**: Code formatting

### Lint-staged

Automatically formats and lints staged files:

- Runs Prettier on staged files
- Runs ESLint with auto-fix on staged files

### Tools Used

- **Husky**: Git hooks management
- **lint-staged**: Run linters on staged files
- **ESLint**: JavaScript/TypeScript linting
- **Prettier**: Code formatting
- **TypeScript**: Type checking

## 🌟 Testing the Dashboard

### Local Testing

1. Ensure you have the correct environment variables set up
2. Run `npm run dev`
3. Navigate to `http://localhost:5173`
4. Sign up with a new email or use your existing account

### Live Testing

- **Development Environment**: Test new features on the dev branch deployment
- **Production Environment**: Test client-facing features on the main branch deployment

## 💡 Environment Variable Management Tips

### Recommended Approach

To handle different local development scenarios, consider creating example environment files:

1. **`.env.local.example`** - Template for local development
2. **`.env.local.full`** - Full local development configuration
3. **`.env.local.dashboard`** - Dashboard-only development configuration

**Setup:**

```bash
# Copy the appropriate template
cp .env.local.full .env.local
# or
cp .env.local.dashboard .env.local
```

### Environment Switching (Automated Solution)

We've created an automated environment management system with templates and scripts:

```bash
# Switch to full local development (with local APIs)
npm run env:local

# Switch to dashboard-only development (with live dev APIs)
npm run env:dashboard

# Show help and available options
npm run env:help
```

**Template Files Available:**

- `env-templates/local-full-development.env` - Full local development setup
- `env-templates/dashboard-only-development.env` - Dashboard-only development setup

**Features:**

- Automatic backup of existing `.env.local`
- Easy switching between development modes
- Clear documentation of each configuration
- Safety checks and validation

📁 **See `env-templates/README.md` for detailed documentation**

## 📁 Project Structure

```
ff-zor-dashboard/
├── src/                    # Source code
├── public/                 # Static assets
├── env-templates/          # Environment variable templates
│   ├── README.md          # Environment management guide
│   ├── local-full-development.env     # Local development template
│   ├── dashboard-only-development.env # Dashboard-only template
│   └── production-reference.env       # Production config reference
├── scripts/               # Utility scripts
│   └── switch-env.js     # Environment switching script
├── .husky/               # Git hooks (pre-commit, etc.)
├── node_modules/         # Dependencies
├── .env.local           # Local environment variables (gitignored)
├── package.json        # Project configuration & scripts
├── vite.config.ts      # Vite configuration
├── tailwind.config.js  # Tailwind CSS configuration
├── tsconfig.json       # TypeScript configuration
└── README.md          # This file
```

## 🤝 Contributing

1. Create a feature branch from `dev`
2. Make your changes
3. Ensure all pre-commit hooks pass
4. Submit a pull request to `dev`

## 📞 Support

For issues or questions, please contact the development team or create an issue in the repository.

---

**Note**: Always ensure your `.env.local` file is properly configured before starting development. The dashboard requires proper environment variables to function correctly.
