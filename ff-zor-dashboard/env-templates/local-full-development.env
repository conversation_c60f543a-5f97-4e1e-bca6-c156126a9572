# FF-ZOR-Dashboard - Full Local Development Environment
# Copy this content to .env.local when you want to run all services locally

# ===========================================
# FULL LOCAL DEVELOPMENT SETUP
# ===========================================
# Use this configuration when you're running:
# - Local backend APIs
# - Local microservices
# - Local authentication service

# Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_test_aW5ub2NlbnQtaGF3ay00Mi5jbGVyay5hY2NvdW50cy5kZXYk

# Live Development API Endpoints
VITE_API_URL=http://localhost:8080
VITE_CLERK_API_URL=http://localhost:3001
VITE_TERRITORIES_API_URL=http://localhost:3000

# Database (Supabase - Development Branch)
# Using Supabase development branch for local development
VITE_SUPABASE_URL=https://frulfqfacsxjyatjosfa.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZydWxmcWZhY3N4anlhdGpvc2ZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NDQ0MzgsImV4cCI6MjA2NDEyMDQzOH0.x6Kq58JJR-3uBerN-sQZ0EOnccKB0AG5lLE-pNleZNY

# External Services (can use dev keys)
VITE_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyB4jdmdmQB5D_fVwyFOv-K5Ci-C95J6Zsc
