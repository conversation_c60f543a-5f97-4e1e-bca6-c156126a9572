# Shared Component Library Implementation

## 1. 🎨 Shared Component Library Solution

### Current Problem
The three dashboards have 70-80% code duplication with nearly identical UI components:

```typescript
// ff-zor-dashboard/src/components/ui/button.tsx
// ff-zee-dashboard-new/src/components/ui/button.tsx  
// ff-consultant-dashboard-new/src/components/ui/button.tsx
// All contain the same button component with minor variations
```

### Solution: Create `@ff/ui-components` Package

#### Step 1: Create Shared Package Structure
```bash
mkdir ff-shared-ui
cd ff-shared-ui
npm init -y
```

#### Step 2: Package.json Configuration
```json
{
  "name": "@ff/ui-components",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "files": ["dist"],
  "scripts": {
    "build": "tsup src/index.ts --format cjs,esm --dts",
    "dev": "tsup src/index.ts --format cjs,esm --dts --watch",
    "lint": "eslint src --ext .ts,.tsx",
    "type-check": "tsc --noEmit"
  },
  "peerDependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.3.3",
    "@types/react-dom": "^18.3.0",
    "tsup": "^8.0.0",
    "typescript": "^5.0.0"
  },
  "dependencies": {
    "@radix-ui/react-slot": "^1.2.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.5.5"
  }
}
```

#### Step 3: Unified Button Component
```typescript
// ff-shared-ui/src/components/Button.tsx
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { forwardRef } from 'react';
import { cn } from '../utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
        outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        // Role-specific variants
        zor: 'bg-blue-600 text-white hover:bg-blue-700',
        zee: 'bg-green-600 text-white hover:bg-green-700',
        consultant: 'bg-purple-600 text-white hover:bg-purple-700',
      },
      size: {
        default: 'h-9 px-4 py-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-10 rounded-md px-8',
        icon: 'h-9 w-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
```

#### Step 4: Shared Form Components
```typescript
// ff-shared-ui/src/components/Form.tsx
import { useForm, FormProvider, FieldValues, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface FormProps<T extends FieldValues> {
  schema: z.ZodSchema<T>;
  onSubmit: SubmitHandler<T>;
  defaultValues?: Partial<T>;
  children: React.ReactNode;
  className?: string;
}

export function Form<T extends FieldValues>({
  schema,
  onSubmit,
  defaultValues,
  children,
  className,
}: FormProps<T>) {
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className={className}>
        {children}
      </form>
    </FormProvider>
  );
}

// Shared form field component
export function FormField({ name, label, type = 'text', ...props }) {
  const { register, formState: { errors } } = useFormContext();
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">{label}</label>
      <input
        {...register(name)}
        type={type}
        className="w-full px-3 py-2 border rounded-md"
        {...props}
      />
      {errors[name] && (
        <p className="text-sm text-red-600">{errors[name]?.message}</p>
      )}
    </div>
  );
}
```

#### Step 5: Shared Data Table Component
```typescript
// ff-shared-ui/src/components/DataTable.tsx
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
} from '@tanstack/react-table';
import { useState } from 'react';

interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  searchPlaceholder?: string;
  pageSize?: number;
}

export function DataTable<T>({
  data,
  columns,
  searchPlaceholder = "Search...",
  pageSize = 10,
}: DataTableProps<T>) {
  const [sorting, setSorting] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize,
      },
    },
  });

  return (
    <div className="space-y-4">
      <input
        placeholder={searchPlaceholder}
        value={globalFilter}
        onChange={(e) => setGlobalFilter(e.target.value)}
        className="max-w-sm px-3 py-2 border rounded-md"
      />
      
      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th key={header.id} className="px-4 py-2 text-left">
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="px-4 py-2">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
        <span className="text-sm text-gray-600">
          Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </span>
      </div>
    </div>
  );
}
```

#### Step 6: Export All Components
```typescript
// ff-shared-ui/src/index.ts
export { Button, buttonVariants } from './components/Button';
export { Form, FormField } from './components/Form';
export { DataTable } from './components/DataTable';
export { Card, CardHeader, CardContent, CardFooter } from './components/Card';
export { Dialog, DialogContent, DialogHeader, DialogTitle } from './components/Dialog';
export { Input } from './components/Input';
export { Label } from './components/Label';
export { Select, SelectContent, SelectItem, SelectTrigger } from './components/Select';
export { Textarea } from './components/Textarea';
export { Toast, useToast } from './components/Toast';

// Utility exports
export { cn } from './utils';
export type { ButtonProps } from './components/Button';
```

#### Step 7: Build Configuration
```typescript
// ff-shared-ui/tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": false,
    "declaration": true,
    "declarationMap": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "outDir": "dist"
  },
  "include": ["src"],
  "exclude": ["node_modules", "dist"]
}
```

#### Step 8: Update Dashboard Dependencies
```json
// In each dashboard's package.json
{
  "dependencies": {
    "@ff/ui-components": "file:../ff-shared-ui",
    // Remove individual UI dependencies
  }
}
```

#### Step 9: Update Dashboard Imports
```typescript
// Before (in each dashboard)
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

// After (in each dashboard)
import { Button, Card, Input } from '@ff/ui-components';
```

### Benefits of This Approach

1. **Reduced Bundle Size**: Eliminate duplicate components (save ~200KB per dashboard)
2. **Consistent UI**: Single source of truth for design system
3. **Faster Development**: Reuse components across dashboards
4. **Easier Maintenance**: Fix bugs once, apply everywhere
5. **Role-based Theming**: Support different themes per user role

### Implementation Timeline
- **Week 1**: Set up shared package structure
- **Week 2**: Migrate basic UI components (Button, Input, Card)
- **Week 3**: Migrate complex components (DataTable, Forms)
- **Week 4**: Update all dashboards to use shared components
- **Week 5**: Remove duplicate components and test

### Estimated Impact
- **Bundle Size Reduction**: 30-40% smaller bundles
- **Development Speed**: 50% faster for new features
- **Maintenance**: 70% less code to maintain
- **Consistency**: 100% UI consistency across dashboards
