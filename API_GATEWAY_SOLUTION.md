# API Gateway Implementation

## 2. 🌐 API Gateway Solution

### Current Problem
Multiple backend services with no centralized routing, rate limiting, or request management:

```typescript
// Current: Frontend apps call services directly
const zorData = await fetch(`${API_URL}/v1/zors`);
const territories = await fetch(`${TERRITORIES_API_URL}/api/territories`);
const auth = await fetch(`${CLERK_API_URL}/api/send-org-invitation`);
```

### Solution: Implement API Gateway with Next.js

#### Step 1: Create API Gateway Service
```bash
mkdir ff-api-gateway
cd ff-api-gateway
npm init -y
npm install next@latest react@latest react-dom@latest
npm install @types/node typescript --save-dev
```

#### Step 2: Gateway Configuration
```typescript
// ff-api-gateway/src/config/services.ts
export const SERVICES = {
  main: {
    url: process.env.MAIN_API_URL || 'http://localhost:8000',
    prefix: '/v1',
    timeout: 30000,
  },
  territories: {
    url: process.env.TERRITORIES_API_URL || 'http://localhost:3000',
    prefix: '/api/territories',
    timeout: 15000,
  },
  clerk: {
    url: process.env.CLERK_API_URL || 'http://localhost:3001',
    prefix: '/api/auth',
    timeout: 10000,
  },
} as const;

export const RATE_LIMITS = {
  default: { requests: 100, window: '15m' },
  auth: { requests: 10, window: '15m' },
  upload: { requests: 5, window: '1m' },
  territories: { requests: 50, window: '1m' },
} as const;
```

#### Step 3: Rate Limiting Middleware
```typescript
// ff-api-gateway/src/middleware/rateLimiter.ts
import { NextRequest, NextResponse } from 'next/server';
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

interface RateLimit {
  requests: number;
  window: string;
}

export class RateLimiter {
  private static parseWindow(window: string): number {
    const unit = window.slice(-1);
    const value = parseInt(window.slice(0, -1));
    
    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      default: return value;
    }
  }

  static async checkLimit(
    identifier: string,
    limit: RateLimit
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = `rate_limit:${identifier}`;
    const windowMs = this.parseWindow(limit.window);
    const now = Date.now();
    const windowStart = now - windowMs;

    // Remove old entries
    await redis.zremrangebyscore(key, 0, windowStart);
    
    // Count current requests
    const currentRequests = await redis.zcard(key);
    
    if (currentRequests >= limit.requests) {
      const oldestRequest = await redis.zrange(key, 0, 0, 'WITHSCORES');
      const resetTime = oldestRequest.length > 0 
        ? parseInt(oldestRequest[1]) + windowMs 
        : now + windowMs;
        
      return {
        allowed: false,
        remaining: 0,
        resetTime,
      };
    }

    // Add current request
    await redis.zadd(key, now, `${now}-${Math.random()}`);
    await redis.expire(key, Math.ceil(windowMs / 1000));

    return {
      allowed: true,
      remaining: limit.requests - currentRequests - 1,
      resetTime: now + windowMs,
    };
  }
}

export async function rateLimitMiddleware(
  request: NextRequest,
  limit: RateLimit
) {
  const identifier = request.ip || 'anonymous';
  const result = await RateLimiter.checkLimit(identifier, limit);

  if (!result.allowed) {
    return NextResponse.json(
      { error: 'Rate limit exceeded' },
      { 
        status: 429,
        headers: {
          'X-RateLimit-Limit': limit.requests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': result.resetTime.toString(),
        },
      }
    );
  }

  return null; // Continue to next middleware
}
```

#### Step 4: Authentication Middleware
```typescript
// ff-api-gateway/src/middleware/auth.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClerkClient } from '@clerk/backend';

const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY,
});

export async function authMiddleware(request: NextRequest) {
  const token = request.headers.get('Authorization')?.split(' ')[1];
  
  if (!token) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  try {
    const authStatus = await clerkClient.authenticateRequest(request, {
      publishableKey: process.env.CLERK_PUBLISHABLE_KEY,
    });

    if (!authStatus.isSignedIn) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    // Add user info to headers for downstream services
    const response = NextResponse.next();
    response.headers.set('X-User-ID', authStatus.userId);
    response.headers.set('X-User-Role', authStatus.sessionClaims?.role || '');
    response.headers.set('X-User-Org-ID', authStatus.sessionClaims?.org_id || '');
    
    return response;
  } catch (error) {
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 401 }
    );
  }
}
```

#### Step 5: Service Proxy Handler
```typescript
// ff-api-gateway/src/lib/proxy.ts
import { NextRequest, NextResponse } from 'next/server';
import { SERVICES } from '../config/services';

export async function proxyRequest(
  request: NextRequest,
  serviceName: keyof typeof SERVICES,
  path: string
) {
  const service = SERVICES[serviceName];
  const targetUrl = `${service.url}${path}`;
  
  // Forward headers (excluding host)
  const headers = new Headers();
  request.headers.forEach((value, key) => {
    if (key.toLowerCase() !== 'host') {
      headers.set(key, value);
    }
  });

  try {
    const response = await fetch(targetUrl, {
      method: request.method,
      headers,
      body: request.body,
      signal: AbortSignal.timeout(service.timeout),
    });

    // Forward response headers
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      responseHeaders.set(key, value);
    });

    return new NextResponse(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error(`Proxy error for ${serviceName}:`, error);
    
    if (error.name === 'TimeoutError') {
      return NextResponse.json(
        { error: 'Service timeout' },
        { status: 504 }
      );
    }
    
    return NextResponse.json(
      { error: 'Service unavailable' },
      { status: 503 }
    );
  }
}
```

#### Step 6: Main API Routes
```typescript
// ff-api-gateway/src/app/api/v1/[...path]/route.ts
import { NextRequest } from 'next/server';
import { rateLimitMiddleware } from '@/middleware/rateLimiter';
import { authMiddleware } from '@/middleware/auth';
import { proxyRequest } from '@/lib/proxy';
import { RATE_LIMITS } from '@/config/services';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params.path);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params.path);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params.path);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleRequest(request, params.path);
}

async function handleRequest(request: NextRequest, pathSegments: string[]) {
  const path = `/v1/${pathSegments.join('/')}`;
  
  // Apply rate limiting
  const rateLimitResult = await rateLimitMiddleware(request, RATE_LIMITS.default);
  if (rateLimitResult) return rateLimitResult;
  
  // Apply authentication
  const authResult = await authMiddleware(request);
  if (authResult && authResult.status !== 200) return authResult;
  
  // Proxy to main API
  return proxyRequest(request, 'main', path);
}
```

#### Step 7: Territories API Routes
```typescript
// ff-api-gateway/src/app/api/territories/[...path]/route.ts
import { NextRequest } from 'next/server';
import { rateLimitMiddleware } from '@/middleware/rateLimiter';
import { authMiddleware } from '@/middleware/auth';
import { proxyRequest } from '@/lib/proxy';
import { RATE_LIMITS } from '@/config/services';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  const path = `/api/${params.path.join('/')}`;
  
  // Apply territories-specific rate limiting
  const rateLimitResult = await rateLimitMiddleware(request, RATE_LIMITS.territories);
  if (rateLimitResult) return rateLimitResult;
  
  // Apply authentication
  const authResult = await authMiddleware(request);
  if (authResult && authResult.status !== 200) return authResult;
  
  // Proxy to territories service
  return proxyRequest(request, 'territories', path);
}

// Similar for POST, PUT, DELETE...
```

#### Step 8: Health Check and Monitoring
```typescript
// ff-api-gateway/src/app/api/health/route.ts
import { NextResponse } from 'next/server';
import { SERVICES } from '@/config/services';

export async function GET() {
  const healthChecks = await Promise.allSettled(
    Object.entries(SERVICES).map(async ([name, service]) => {
      try {
        const response = await fetch(`${service.url}/health`, {
          signal: AbortSignal.timeout(5000),
        });
        return {
          service: name,
          status: response.ok ? 'healthy' : 'unhealthy',
          responseTime: Date.now(),
        };
      } catch (error) {
        return {
          service: name,
          status: 'unhealthy',
          error: error.message,
        };
      }
    })
  );

  const results = healthChecks.map((check) => 
    check.status === 'fulfilled' ? check.value : check.reason
  );

  const allHealthy = results.every(result => result.status === 'healthy');

  return NextResponse.json(
    {
      status: allHealthy ? 'healthy' : 'degraded',
      services: results,
      timestamp: new Date().toISOString(),
    },
    { status: allHealthy ? 200 : 503 }
  );
}
```

#### Step 9: Update Frontend Configuration
```typescript
// Update all dashboards to use single API gateway
// ff-zor-dashboard/src/constants/config.ts
interface Config {
  API_URL: string; // Single gateway URL
}

export const configuration: Config = {
  API_URL: import.meta.env.VITE_API_GATEWAY_URL || 'http://localhost:4000/api',
};
```

#### Step 10: Docker Configuration
```dockerfile
# ff-api-gateway/Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 4000
CMD ["npm", "start"]
```

### Benefits of API Gateway

1. **Centralized Routing**: Single entry point for all API calls
2. **Rate Limiting**: Prevent API abuse and ensure fair usage
3. **Authentication**: Centralized auth validation
4. **Monitoring**: Single place to monitor all API traffic
5. **Load Balancing**: Distribute requests across service instances
6. **Caching**: Cache responses to improve performance
7. **Request/Response Transformation**: Modify requests/responses as needed

### Implementation Timeline
- **Week 1**: Set up basic gateway structure and routing
- **Week 2**: Implement rate limiting and authentication
- **Week 3**: Add monitoring and health checks
- **Week 4**: Update all frontends to use gateway
- **Week 5**: Performance testing and optimization

### Estimated Impact
- **Security**: Centralized security policies
- **Performance**: 20-30% improvement with caching
- **Monitoring**: 100% visibility into API usage
- **Maintenance**: Easier to manage API policies
