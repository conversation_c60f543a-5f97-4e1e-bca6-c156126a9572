# Testing and Monitoring Solutions

## 5. 📝 Testing Infrastructure

### Current Problem
No test files found in any project - zero test coverage:

```bash
# Search results across all projects
find . -name "*.test.*" -o -name "*.spec.*" -o -name "__tests__"
# Returns: No results found
```

### Solution: Comprehensive Testing Strategy

#### 1. Frontend Testing Setup

##### Vitest Configuration
```typescript
// ff-shared-ui/vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
    },
  },
});
```

##### Test Setup
```typescript
// ff-shared-ui/src/test/setup.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, vi } from 'vitest';

// Cleanup after each test
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));
```

##### Component Testing Examples
```typescript
// ff-shared-ui/src/components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Button } from '../Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies variant styles correctly', () => {
    render(<Button variant="destructive">Delete</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

##### Hook Testing
```typescript
// ff-zor-dashboard/src/hooks/__tests__/useZors.test.tsx
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useZors } from '../useZors';

// Mock API
vi.mock('@/helpers/api.manager', () => ({
  apiManager: {
    get: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useZors', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('fetches zors successfully', async () => {
    const mockZors = [
      { id: '1', name: 'Test Zor', slug: 'test-zor' },
    ];
    
    vi.mocked(apiManager.get).mockResolvedValue({ data: mockZors });

    const { result } = renderHook(() => useZors(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockZors);
  });

  it('handles error states', async () => {
    vi.mocked(apiManager.get).mockRejectedValue(new Error('API Error'));

    const { result } = renderHook(() => useZors(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error).toBeInstanceOf(Error);
  });
});
```

#### 2. Backend Testing Setup

##### FastAPI Testing Configuration
```python
# ff-backend-new/tests/conftest.py
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel

from app.main import app
from app.core.database import get_session
from app.core.config import settings

# Test database URL
TEST_DATABASE_URL = "postgresql+asyncpg://test:test@localhost:5432/test_franchise_frameworks"

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)
    
    yield engine
    
    # Drop tables
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.drop_all)

@pytest.fixture
async def test_session(test_engine):
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session

@pytest.fixture
async def client(test_session):
    def get_test_session():
        return test_session
    
    app.dependency_overrides[get_session] = get_test_session
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()

@pytest.fixture
def mock_clerk_user():
    return {
        "user_id": "user_123",
        "role": "org:zor",
        "org_id": "org_456",
        "email": "<EMAIL>"
    }
```

##### API Endpoint Testing
```python
# ff-backend-new/tests/test_zor_endpoints.py
import pytest
from httpx import AsyncClient
from sqlmodel import select

from app.models.zor.zor import Zor

class TestZorEndpoints:
    
    @pytest.mark.asyncio
    async def test_create_zor(self, client: AsyncClient, test_session, mock_clerk_user):
        zor_data = {
            "name": "Test Franchise",
            "slug": "test-franchise",
            "logo_url": "https://example.com/logo.png",
            "headquarter_location": "New York, NY",
            "franchise_since": 2020,
            "industry": "Food & Beverage",
            "number_of_units": 50,
            "website": "https://testfranchise.com",
            "fdd_renewal_date": "2024-12-31"
        }
        
        response = await client.post(
            "/v1/zors",
            json=zor_data,
            headers={"Authorization": f"Bearer mock_token"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == zor_data["name"]
        assert data["slug"] == zor_data["slug"]
        
        # Verify in database
        result = await test_session.exec(select(Zor).where(Zor.slug == "test-franchise"))
        zor = result.first()
        assert zor is not None
        assert zor.name == "Test Franchise"

    @pytest.mark.asyncio
    async def test_get_zors(self, client: AsyncClient, test_session, mock_clerk_user):
        # Create test data
        zor = Zor(
            id="test_id",
            name="Test Zor",
            slug="test-zor",
            logo_url="https://example.com/logo.png",
            headquarter_location="Test Location",
            franchise_since=2020,
            industry="Test Industry",
            number_of_units=10,
            website="https://test.com",
            fdd_renewal_date="2024-12-31"
        )
        test_session.add(zor)
        await test_session.commit()
        
        response = await client.get(
            "/v1/zors",
            headers={"Authorization": f"Bearer mock_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "Test Zor"

    @pytest.mark.asyncio
    async def test_unauthorized_access(self, client: AsyncClient):
        response = await client.get("/v1/zors")
        assert response.status_code == 401
```

##### Service Layer Testing
```python
# ff-backend-new/tests/test_zor_service.py
import pytest
from sqlmodel import select

from app.models.zor.zor import Zor
from app.services.zor_service import ZorService

class TestZorService:
    
    @pytest.mark.asyncio
    async def test_create_zor(self, test_session):
        service = ZorService(test_session)
        
        zor_data = {
            "name": "Service Test Zor",
            "slug": "service-test-zor",
            "logo_url": "https://example.com/logo.png",
            "headquarter_location": "Test City",
            "franchise_since": 2021,
            "industry": "Technology",
            "number_of_units": 25,
            "website": "https://servicetest.com",
            "fdd_renewal_date": "2025-01-01"
        }
        
        zor = await service.create_zor(zor_data)
        
        assert zor.name == "Service Test Zor"
        assert zor.slug == "service-test-zor"
        
        # Verify persistence
        result = await test_session.exec(select(Zor).where(Zor.id == zor.id))
        persisted_zor = result.first()
        assert persisted_zor is not None

    @pytest.mark.asyncio
    async def test_get_zor_by_slug(self, test_session):
        service = ZorService(test_session)
        
        # Create test zor
        zor = Zor(
            id="test_slug_id",
            name="Slug Test Zor",
            slug="slug-test-zor",
            logo_url="https://example.com/logo.png",
            headquarter_location="Slug City",
            franchise_since=2022,
            industry="Retail",
            number_of_units=15,
            website="https://slugtest.com",
            fdd_renewal_date="2025-06-01"
        )
        test_session.add(zor)
        await test_session.commit()
        
        found_zor = await service.get_zor_by_slug("slug-test-zor")
        
        assert found_zor is not None
        assert found_zor.name == "Slug Test Zor"
        
        # Test non-existent slug
        not_found = await service.get_zor_by_slug("non-existent")
        assert not_found is None
```

#### 3. Integration Testing

##### End-to-End Testing with Playwright
```typescript
// tests/e2e/zor-dashboard.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Zor Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ user: { role: 'org:zor' } })
      });
    });
    
    await page.goto('/');
  });

  test('should display dashboard overview', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Dashboard');
    await expect(page.locator('[data-testid="metrics-cards"]')).toBeVisible();
  });

  test('should navigate to zees management', async ({ page }) => {
    await page.click('[data-testid="nav-zees"]');
    await expect(page).toHaveURL('/zees');
    await expect(page.locator('h1')).toContainText('Franchisees');
  });

  test('should create new zee', async ({ page }) => {
    await page.goto('/zees');
    await page.click('[data-testid="add-zee-button"]');
    
    await page.fill('[data-testid="zee-name-input"]', 'Test Zee');
    await page.fill('[data-testid="zee-email-input"]', '<EMAIL>');
    await page.click('[data-testid="save-zee-button"]');
    
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});
```

## 6. 📊 Monitoring and Logging

### Application Monitoring Setup

#### Error Tracking with Sentry
```typescript
// ff-shared-ui/src/utils/monitoring.ts
import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

export function initializeMonitoring() {
  Sentry.init({
    dsn: process.env.VITE_SENTRY_DSN,
    environment: process.env.VITE_ENVIRONMENT,
    integrations: [
      new BrowserTracing({
        tracingOrigins: ['localhost', /^https:\/\/.*\.franchiseframeworks\.com/],
      }),
    ],
    tracesSampleRate: 0.1,
    beforeSend(event) {
      // Filter out development errors
      if (process.env.NODE_ENV === 'development') {
        return null;
      }
      return event;
    },
  });
}

export function captureException(error: Error, context?: Record<string, any>) {
  Sentry.withScope(scope => {
    if (context) {
      scope.setContext('additional', context);
    }
    Sentry.captureException(error);
  });
}

export function captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info') {
  Sentry.captureMessage(message, level);
}
```

#### Backend Logging
```python
# ff-backend-new/app/core/logging.py
import logging
import json
from datetime import datetime
from typing import Any, Dict
from contextlib import contextmanager

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Create structured formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def log_structured(self, level: str, message: str, **kwargs):
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'message': message,
            'level': level,
            **kwargs
        }
        
        getattr(self.logger, level.lower())(json.dumps(log_data))

    def info(self, message: str, **kwargs):
        self.log_structured('INFO', message, **kwargs)

    def error(self, message: str, error: Exception = None, **kwargs):
        error_data = {}
        if error:
            error_data = {
                'error_type': type(error).__name__,
                'error_message': str(error),
            }
        
        self.log_structured('ERROR', message, **error_data, **kwargs)

    def warning(self, message: str, **kwargs):
        self.log_structured('WARNING', message, **kwargs)

    @contextmanager
    def log_operation(self, operation: str, **context):
        start_time = datetime.utcnow()
        self.info(f"Starting {operation}", operation=operation, **context)
        
        try:
            yield
            duration = (datetime.utcnow() - start_time).total_seconds()
            self.info(
                f"Completed {operation}",
                operation=operation,
                duration_seconds=duration,
                status="success",
                **context
            )
        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds()
            self.error(
                f"Failed {operation}",
                error=e,
                operation=operation,
                duration_seconds=duration,
                status="error",
                **context
            )
            raise

# Usage in services
logger = StructuredLogger(__name__)

async def create_zor(zor_data: dict) -> Zor:
    with logger.log_operation("create_zor", zor_name=zor_data.get("name")):
        # Implementation
        pass
```

### Implementation Timeline

#### Week 1: Basic Testing Setup
- Configure Vitest for frontend projects
- Set up pytest for backend
- Create basic test examples

#### Week 2: Component and API Testing
- Write tests for shared UI components
- Create API endpoint tests
- Set up test databases

#### Week 3: Integration Testing
- Set up Playwright for E2E tests
- Create critical user journey tests
- Add CI/CD integration

#### Week 4: Monitoring Setup
- Implement Sentry for error tracking
- Set up structured logging
- Create performance monitoring

#### Week 5: Coverage and Optimization
- Achieve 80%+ test coverage
- Optimize test performance
- Create testing documentation

### Expected Benefits

1. **Bug Reduction**: 70-80% fewer production bugs
2. **Deployment Confidence**: Safe refactoring and feature additions
3. **Performance Monitoring**: Real-time performance insights
4. **Error Tracking**: Immediate notification of production issues
5. **Code Quality**: Improved code quality through TDD practices
