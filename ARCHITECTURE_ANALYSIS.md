# Franchise Frameworks System Architecture Analysis

## Executive Summary

This document provides a comprehensive analysis of the Franchise Frameworks ecosystem, consisting of 6 interconnected projects that form a complete franchise management platform. The system supports three primary user roles: Franchisors (Zors), Franchisees (Zees), and Consultants, each with dedicated dashboards and workflows.

## System Overview

The Franchise Frameworks platform is a multi-tenant SaaS solution designed to streamline franchise operations, from initial discovery through onboarding and ongoing management. The architecture follows a microservices pattern with role-based frontend applications and specialized backend services.

### Core Components

1. **ff-backend-new** - Main business logic API (FastAPI/Python)
2. **ff-clerk-backend** - Authentication service (Next.js)
3. **ff-territories-backend-new** - Geographic territory management (Next.js)
4. **ff-consultant-dashboard-new** - Consultant interface (React/Vite)
5. **ff-zee-dashboard-new** - Franchisee interface (React/Vite)
6. **ff-zor-dashboard** - Franchisor interface (React/Vite)

## Technology Stack

### Backend Services
- **Primary API**: FastAPI + SQLModel + PostgreSQL + Supabase
- **Authentication**: Next.js + Clerk + Supabase
- **Territories**: Next.js + Supabase + JWT
- **Database**: PostgreSQL via Supabase
- **Email**: SendGrid integration
- **Deployment**: Vercel + AWS App Runner

### Frontend Applications
- **Framework**: React 18+ with TypeScript
- **Build Tool**: Vite
- **State Management**: Zustand
- **UI Components**: Radix UI + Tailwind CSS
- **Forms**: React Hook Form + Zod validation
- **Data Fetching**: TanStack Query (React Query)
- **Maps**: Google Maps API + Turf.js
- **Authentication**: Clerk React

### External Services
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **Email**: SendGrid
- **Deployment**: Vercel
- **Maps**: Google Maps API
- **AI/Chat**: Voiceflow (Zee dashboard)

## System Architecture Diagram

```mermaid
graph TB
    subgraph "Frontend Applications"
        ZOR[ff-zor-dashboard<br/>Franchisor UI<br/>React + Vite]
        ZEE[ff-zee-dashboard-new<br/>Franchisee UI<br/>React + Vite]
        CON[ff-consultant-dashboard-new<br/>Consultant UI<br/>React + Vite]
    end
    
    subgraph "Backend Services"
        MAIN[ff-backend-new<br/>Main API<br/>FastAPI + SQLModel]
        CLERK[ff-clerk-backend<br/>Auth Service<br/>Next.js + Clerk]
        TERR[ff-territories-backend-new<br/>Territories API<br/>Next.js]
    end
    
    subgraph "External Services"
        CLERKAUTH[Clerk Authentication]
        SUPABASE[Supabase Database<br/>PostgreSQL]
        SENDGRID[SendGrid Email]
        VERCEL[Vercel Deployment]
        GMAPS[Google Maps API]
    end
    
    ZOR --> MAIN
    ZOR --> CLERK
    ZOR --> TERR
    
    ZEE --> MAIN
    ZEE --> CLERK
    ZEE --> TERR
    
    CON --> MAIN
    CON --> CLERK
    CON --> TERR
    
    MAIN --> SUPABASE
    MAIN --> SENDGRID
    MAIN --> CLERKAUTH
    
    CLERK --> CLERKAUTH
    CLERK --> SUPABASE
    
    TERR --> SUPABASE
    TERR --> CLERKAUTH
    
    ZOR --> GMAPS
    ZEE --> GMAPS
    CON --> GMAPS
```

## Data Flow & API Communication

### Authentication Flow
1. Users authenticate via Clerk in frontend applications
2. JWT tokens are issued and stored in localStorage
3. API requests include Bearer tokens in Authorization headers
4. Backend services verify tokens with Clerk
5. User roles and organization data are extracted from tokens

### API Communication Patterns
- **Main API**: All business logic operations (`/v1/*` endpoints)
- **Clerk API**: Authentication operations (`/api/*` endpoints)
- **Territories API**: Geographic operations (`/api/*` endpoints)
- **Cross-service**: Services communicate via HTTP APIs

### Database Architecture
- **Primary Database**: PostgreSQL via Supabase
- **Tables**: Organized by domain (zors, zees, consultants, territories, etc.)
- **Relationships**: Proper foreign key constraints
- **Access**: Direct database access via Supabase client libraries

## Environment Configuration

All frontend applications use similar environment variable patterns:

```typescript
interface Config {
  API_URL: string;              // Main backend API
  API_KEY: string;              // API authentication key
  CLERK_API_URL: string;        // Clerk backend service
  TERRITORIES_API_URL: string;  // Territories service
}
```

### Environment-Specific URLs
- **Local**: localhost ports (5173, 5174, 5175)
- **Development**: `*-dev.franchiseframeworks.com`
- **Production**: `*.franchiseframeworks.com`

## Security Architecture

### Authentication & Authorization
- **Provider**: Clerk for authentication
- **Method**: JWT tokens with role-based claims
- **Roles**: `org:zor`, `org:zee`, `org:partner_consultant`
- **Multi-tenancy**: Organization-based isolation

### API Security
- **Authentication**: Bearer token validation
- **CORS**: Currently allows all origins (⚠️ Security Risk)
- **Rate Limiting**: Not implemented (⚠️ Missing)
- **Input Validation**: Partial implementation

### Data Security
- **Database**: Supabase with RLS (Row Level Security)
- **File Storage**: Supabase Storage with access controls
- **Encryption**: HTTPS for all communications

## Performance Considerations

### Frontend Performance
- **Bundle Splitting**: Vite provides automatic code splitting
- **State Management**: Zustand for efficient state updates
- **Data Fetching**: TanStack Query for caching and optimization
- **UI Optimization**: Radix UI for accessible, performant components

### Backend Performance
- **Async Operations**: FastAPI with async/await patterns
- **Database**: SQLModel with async PostgreSQL driver
- **Connection Pooling**: Handled by Supabase
- **Caching**: Not implemented (⚠️ Missing)

### Scalability Factors
- **Horizontal Scaling**: Stateless services support scaling
- **Database**: Supabase handles scaling automatically
- **CDN**: Vercel provides global CDN for static assets
- **Load Balancing**: Vercel handles load balancing

## Deployment Architecture

### Frontend Deployment
- **Platform**: Vercel
- **Build**: Vite production builds
- **Environment**: Separate deployments per environment
- **Domains**: Custom domains per application type

### Backend Deployment
- **Main API**: AWS App Runner (based on apprunner.yaml files)
- **Clerk Backend**: Vercel
- **Territories Backend**: Vercel
- **Database**: Supabase (managed PostgreSQL)

## Integration Points

### External Service Integrations
1. **Clerk**: Authentication and user management
2. **Supabase**: Database and file storage
3. **SendGrid**: Email delivery
4. **Google Maps**: Territory visualization and geocoding
5. **Vercel**: Deployment and hosting
6. **Voiceflow**: AI chatbot (Zee dashboard only)

### Internal Service Communication
- **API-First**: All services expose REST APIs
- **Authentication**: Shared Clerk authentication
- **Data Consistency**: Eventual consistency model
- **Error Handling**: HTTP status codes and error responses

## Development Workflow

### Code Organization
- **Monorepo Structure**: All projects in single repository
- **Shared Patterns**: Similar structure across frontend apps
- **TypeScript**: Consistent typing across all projects
- **Linting**: ESLint + Prettier for code quality

### Build & Development
- **Local Development**: Each service runs independently
- **Hot Reload**: Vite for fast frontend development
- **API Development**: FastAPI with auto-reload
- **Environment Management**: `.env` files per project

This architecture provides a solid foundation for a comprehensive franchise management platform, with clear separation of concerns and modern technology choices.
